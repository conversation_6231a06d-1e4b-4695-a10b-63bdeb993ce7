#!/bin/bash
set -e

echo "Starting Laravel application setup..."

# Wait for database to be ready (optional - uncomment if using separate DB container)
# echo "Waiting for database..."
# while ! nc -z $DB_HOST $DB_PORT; do sleep 1; done

# Fix permissions for cache directories
echo "Setting up permissions..."
chown -R www-data:www-data /var/www/html/storage
chown -R www-data:www-data /var/www/html/bootstrap/cache
chmod -R 775 /var/www/html/storage
chmod -R 775 /var/www/html/bootstrap/cache

# Ensure session directory exists and has proper permissions
echo "Setting up session storage..."
mkdir -p /var/www/html/storage/framework/sessions
chown -R www-data:www-data /var/www/html/storage/framework/sessions
chmod -R 775 /var/www/html/storage/framework/sessions

# Clear all caches with better error handling
echo "Clearing application caches..."
php artisan config:clear 2>/dev/null || echo "Config cache clear skipped (may not exist)"
php artisan route:clear 2>/dev/null || echo "Route cache clear skipped (may not exist)"
php artisan view:clear 2>/dev/null || echo "View cache clear skipped (may not exist)"

# Clear cache directory manually if artisan cache:clear fails
if ! php artisan cache:clear 2>/dev/null; then
    echo "Artisan cache:clear failed, clearing manually..."
    rm -rf /var/www/html/storage/framework/cache/data/* 2>/dev/null || true
    rm -rf /var/www/html/bootstrap/cache/*.php 2>/dev/null || true
    echo "Manual cache clear completed"
fi

# Create storage link
echo "Creating storage link..."
php artisan storage:link || true

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Verify configuration before caching
echo "Verifying application configuration..."
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Check environment
echo 'APP_ENV: ' . config('app.env') . PHP_EOL;
echo 'APP_URL: ' . config('app.url') . PHP_EOL;

// Check database connection
try {
    \$pdo = \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo '✅ Database connection: SUCCESS' . PHP_EOL;
} catch (Exception \$e) {
    echo '❌ Database connection: FAILED - ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}

// Check session configuration
\$sessionDriver = config('session.driver');
\$sessionDomain = config('session.domain');
\$sessionSecure = config('session.secure');
\$sessionSameSite = config('session.same_site');
echo 'Session driver: ' . \$sessionDriver . PHP_EOL;
echo 'Session domain: ' . (\$sessionDomain ?: 'not set') . PHP_EOL;
echo 'Session secure: ' . (\$sessionSecure ? 'true' : 'false') . PHP_EOL;
echo 'Session same_site: ' . \$sessionSameSite . PHP_EOL;

// Check Livewire configuration
\$disk = config('livewire.temporary_file_upload.disk');
echo 'Livewire upload disk: ' . (\$disk ?: 'default') . PHP_EOL;

// Check proxy configuration
echo 'Trusted proxies: ' . (config('trustedproxy.proxies') ?: 'not set') . PHP_EOL;

// Check if HTTPS is being detected
echo 'Request scheme: ' . (isset(\$_SERVER['REQUEST_SCHEME']) ? \$_SERVER['REQUEST_SCHEME'] : 'not set') . PHP_EOL;
echo 'HTTPS header: ' . (isset(\$_SERVER['HTTPS']) ? \$_SERVER['HTTPS'] : 'not set') . PHP_EOL;

if (\$disk === 'local') {
    echo '✅ GOOD: Using local storage for uploads' . PHP_EOL;
} else {
    echo '⚠️  WARNING: Using ' . \$disk . ' storage for uploads' . PHP_EOL;
}
"

# Cache configuration for production
echo "Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "Laravel application setup completed!"

# Final permission check
echo "Final permission setup..."
chown -R www-data:www-data /var/www/html/storage
chown -R www-data:www-data /var/www/html/bootstrap/cache

# Start the Laravel development server
echo "Starting Laravel development server..."
exec php artisan serve --host=0.0.0.0 --port=8000
