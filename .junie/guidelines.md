# eTender Legacy Project Development Guidelines

This document provides specific information for developers working on the eTender Legacy project.

## Build/Configuration Instructions

### Local Development Setup

1. **Environment Setup**:
   - PHP 8.2 is required
   - MySQL database
   - Node.js and npm for frontend assets

2. **Installation Steps**:
   ```bash
   # Install PHP dependencies
   composer install

   # Install and build frontend assets
   npm install && npm run dev

   # Create storage symlink
   php artisan storage:link

   # Configure database credentials in .env file

   # Run migrations and seed the database
   php artisan migrate --seed
   ```

3. **Docker Development Environment**:
   The project includes Docker configuration for local development:
   ```bash
   # Build and start the container
   docker-compose up -d

   # The application will be available at http://localhost:8000
   ```

   The Docker setup:
   - Uses PHP 8.2-fpm as the base image
   - Installs necessary PHP extensions (gd, pdo, pdo_mysql, zip)
   - Runs migrations automatically on startup
   - Mounts the project directory to /var/www/html for live code changes

## Testing Information

### Test Configuration

The project uses PHPUnit for testing with the following configuration:
- Test suites: Unit and Feature
- Test files should have the "Test.php" suffix
- Tests are located in the `tests` directory
- Environment variables for testing are configured in `phpunit.xml`

### Running Tests

```bash
# Run all tests
./vendor/bin/phpunit

# Run a specific test file
./vendor/bin/phpunit tests/Unit/StringUtilTest.php

# Run a specific test suite
./vendor/bin/phpunit --testsuite Unit

# Run with coverage report
./vendor/bin/phpunit --coverage-html coverage
```

### Writing Tests

1. **Unit Tests**:
   - Extend `PHPUnit\Framework\TestCase`
   - Focus on testing individual functions/methods in isolation
   - Located in `tests/Unit` directory
   
   Example:
   ```php
   <?php
   
   namespace Tests\Unit;
   
   use PHPUnit\Framework\TestCase;
   
   class StringUtilTest extends TestCase
   {
       public function test_string_reversal()
       {
           $original = "Hello World";
           $reversed = strrev($original);
           
           $this->assertEquals("dlroW olleH", $reversed);
       }
   }
   ```

2. **Feature Tests**:
   - Extend `Tests\TestCase` (Laravel's test case)
   - Test application features, routes, and HTTP requests
   - Located in `tests/Feature` directory
   
   Example:
   ```php
   <?php
   
   namespace Tests\Feature;
   
   use Tests\TestCase;
   
   class RouteTest extends TestCase
   {
       public function test_home_page_loads()
       {
           $response = $this->get('/');
           $response->assertStatus(200);
       }
   }
   ```

3. **Database Testing**:
   - Use the `RefreshDatabase` trait to reset the database between tests
   - Configure a test database in `phpunit.xml` (currently commented out)
   - Consider using SQLite in-memory database for faster tests

## AWS Deployment

The project includes a deployment script for AWS ECS with Fargate:

```bash
# First deployment (requires database password)
./deploy.sh --db-password "YourSecurePassword"

# Subsequent deployments
./deploy.sh
```

### Deployment Configuration

- Uses Fargate Spot instances for cost optimization (70% savings)
- Auto-scaling with scale-to-zero capability
- RDS MySQL database (t4g.micro)
- Application Load Balancer with HTTPS support

### Environment Configuration

Environment variables are stored in S3 and loaded during container startup:
- `s3://etender-laravel-env/env_${BranchName}.env`

## Additional Development Information

### Code Style

The project follows Laravel's coding standards:
- PSR-4 autoloading
- PSR-12 coding style
- Use Laravel's built-in helpers when appropriate

### Project Structure

- TALL stack architecture (Tailwind, Alpine.js, Laravel, Livewire)
- Livewire components in `app/Http/Livewire` and `resources/views/livewire`
- Blade templates in `resources/views`
- Routes defined in `routes/web.php`

### Debugging

- Use Laravel's built-in debugging tools:
  ```php
  // Dump and die
  dd($variable);
  
  // Log to storage/logs/laravel.log
  \Log::info('Debug message', ['context' => $data]);
  ```

- For Docker environment, logs are available via:
  ```bash
  docker-compose logs -f app
  ```

- In AWS, logs are sent to CloudWatch with a 7-day retention period