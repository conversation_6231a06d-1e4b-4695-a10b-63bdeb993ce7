AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template to deploy Laravel application on ECS Fargate with cost-optimized settings.

Parameters:
  DeploymentVersion:
    Type: String
    Default: "v1"
    Description: A version or hash to force new ECS task definition
  BranchName:
    Type: String
    Description: The branch name for the .env file.
  VPCId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be deployed.

  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: List of subnet IDs for ECS tasks.

  ImageTag:
    Type: String
    Default: latest
    Description: Docker image tag for the Laravel application.

  ExistingECRRepositoryUri:
    Type: String
    Description: URI of the existing ECR repository.

  ExistingECSClusterName:
    Type: String
    Description: Name of the existing ECS cluster.
    
  DBName:
    Type: String
    Default: etender
    Description: Name for the RDS database.
    
  DBUsername:
    Type: String
    Default: etender
    Description: Username for the RDS database.
    
  DBPassword:
    Type: String
    NoEcho: true
    Description: Password for the RDS database. Must be at least 8 characters.


Resources:
  # Task Execution Role
  TaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ECSFargateExecutionPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                  - logs:DescribeLogStreams
                  - logs:DescribeLogGroups
                  - s3:GetObject
                  - s3:ListBucket
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:PutObjectAcl
                  - s3:CreateBucket
                  - s3:ListAllMyBuckets
                  - secretsmanager:GetSecretValue
                  - ssm:GetParameters
                  - ssm:GetParameter
                  - ssm:GetParametersByPath
                Resource: "*"

  # DB Subnet Group for RDS
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS instance
      SubnetIds: !Ref Subnets

  # Security Group for RDS
  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for RDS instance
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        # Public access from anywhere
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          
  # RDS Instance (t4g.micro for cost optimization)
  Database:
    Type: AWS::RDS::DBInstance
    DependsOn: RDSSecurityGroup
    Properties:
      DBName: !Ref DBName
      Engine: mysql
      EngineVersion: "8.0"
      MasterUsername: !Ref DBUsername
      MasterUserPassword: !Ref DBPassword
      DBInstanceClass: db.t4g.micro
      AllocatedStorage: 20
      MaxAllocatedStorage: 100
      StorageType: gp3
      StorageEncrypted: true
      MultiAZ: false
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - !GetAtt RDSSecurityGroup.GroupId
      BackupRetentionPeriod: 7
      DeletionProtection: false
      PubliclyAccessible: true
      EnablePerformanceInsights: false
      MonitoringInterval: 0
    DeletionPolicy: Snapshot

  ContainerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for ECS containers
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          DestinationSecurityGroupId: !Ref RDSSecurityGroup
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0 # For outbound HTTPS connections if needed
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0 # For HTTP connections
        - IpProtocol: tcp
          FromPort: 1024
          ToPort: 65535
          CidrIp: 0.0.0.0/0 # For ephemeral ports

  # Security Group for Load Balancer
  LoadBalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for Load Balancer
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        # HTTP inbound (port 80) from anywhere - only needed if you keep an HTTP → HTTPS redirect
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0

        # HTTPS inbound (port 443) from anywhere
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          CidrIp: 0.0.0.0/0

  # Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: LaravelLoadBalancer
      Scheme: internet-facing
      Subnets: !Ref Subnets
      SecurityGroups:
        - !Ref LoadBalancerSecurityGroup

  # Target Group
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: LaravelTargetGroup
      TargetType: ip
      Port: 8000
      Protocol: HTTP
      VpcId: !Ref VPCId
      HealthCheckProtocol: HTTP
      HealthCheckPort: traffic-port
      HealthCheckPath: /api/health
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200

  # HTTP Listener (redirect HTTP -> HTTPS)
  LoadBalancerListenerHTTP:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref LoadBalancer
      Port: 80
      Protocol: HTTP
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: "443"
            Host: "#{host}"
            Path: "/#{path}"
            Query: "#{query}"
            StatusCode: HTTP_301
  # HTTPS Listener
  LoadBalancerListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref LoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Sub "arn:aws:acm:ap-south-1:881490112356:certificate/c9a86a45-c382-4613-ac38-a5ff3eb983e8"
      # Use a secure policy from the available ELB security policies
      SslPolicy: ELBSecurityPolicy-TLS-1-2-2017-01
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup

  # ECS Task Definition
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    DependsOn: TaskExecutionRole
    Properties:
      Family: laravel-task
      Cpu: "512"
      Memory: "1024"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt TaskExecutionRole.Arn
      ContainerDefinitions:
        - Name: laravel
          Image: !Sub "${ExistingECRRepositoryUri}:${ImageTag}"
          Cpu: 512
          Memory: 1024
          PortMappings:
            - ContainerPort: 8000
              Protocol: tcp
          Essential: true
          Environment:
            - Name: DEPLOYMENT_VERSION
              Value: !Ref DeploymentVersion
            # Add essential database connection parameters that will override env file
            - Name: DB_CONNECTION
              Value: mysql
            - Name: DB_HOST
              Value: !GetAtt Database.Endpoint.Address
            - Name: DB_PORT
              Value: !GetAtt Database.Endpoint.Port
            - Name: DB_DATABASE
              Value: !Ref DBName
            - Name: DB_USERNAME
              Value: !Ref DBUsername
            - Name: DB_PASSWORD
              Value: !Ref DBPassword
          EnvironmentFiles:
            - Type: s3
              Value: !Sub "arn:aws:s3:::etender-laravel-env/env_${BranchName}.env"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref CloudWatchLogsGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: etender
              awslogs-create-group: "true"
              mode: non-blocking
              max-buffer-size: "16m"
              awslogs-multiline-pattern: "^(\\[\\d{4}-\\d{2}-\\d{2}|\\d{4}-\\d{2}-\\d{2})"
          HealthCheck:
            Command:
              - CMD-SHELL
              - php artisan db:monitor || exit 1
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 120

  # CloudWatch Logs Group for Container Logs
  CloudWatchLogsGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/ecs/etender-${BranchName}"
      RetentionInDays: 7

  # Security Group Ingress Rules for RDS (added to avoid circular dependency)
  RDSSecurityGroupIngressFromContainer:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !GetAtt RDSSecurityGroup.GroupId
      IpProtocol: tcp
      FromPort: 3306
      ToPort: 3306
      SourceSecurityGroupId: !GetAtt ContainerSecurityGroup.GroupId
    DependsOn:
      - RDSSecurityGroup
      - ContainerSecurityGroup
      
  # Explicit ingress rule to allow connections from the internet
  RDSSecurityGroupIngressFromInternet:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !GetAtt RDSSecurityGroup.GroupId
      IpProtocol: tcp
      FromPort: 3306
      ToPort: 3306
      CidrIp: 0.0.0.0/0
    DependsOn:
      - RDSSecurityGroup

  # ECS Service
  ECSService:
    Type: AWS::ECS::Service
    DependsOn:
      - LoadBalancerListenerHTTP
      - LoadBalancerListenerHTTPS
      - Database
      - RDSSecurityGroupIngressFromContainer
      - RDSSecurityGroupIngressFromInternet
    Properties:
      Cluster: !Ref ExistingECSClusterName
      DesiredCount: 1
      # Using CapacityProviderStrategy with FARGATE_SPOT for cost savings
      CapacityProviderStrategy:
        - CapacityProvider: FARGATE_SPOT
          Weight: 1
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets: !Ref Subnets
          SecurityGroups:
            - !Ref ContainerSecurityGroup
          AssignPublicIp: ENABLED
      LoadBalancers:
        - TargetGroupArn: !Ref TargetGroup
          ContainerName: laravel
          ContainerPort: 8000
      TaskDefinition: !Ref TaskDefinition
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
        MaximumPercent: 200

  # Auto Scaling for ECS
  AutoScalingRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole

  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MinCapacity: 0
      MaxCapacity: 2
      ResourceId: !Sub "service/${ExistingECSClusterName}/${ECSService.Name}"
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
      RoleARN: !GetAtt AutoScalingRole.Arn
    DependsOn: 
      - ECSService
      - Database

  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    DependsOn: ScalableTarget
    Properties:
      PolicyName: ScaleDownPolicy
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref ScalableTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        TargetValue: 20.0
        ScaleInCooldown: 300
        ScaleOutCooldown: 180

Outputs:
  LoadBalancerDNS:
    Description: DNS Name of the Load Balancer
    Value: !GetAtt LoadBalancer.DNSName
  LoadBalancerHTTPSListener:
    Description: ARN of HTTPS Listener
    Value: !Ref LoadBalancerListenerHTTPS
  TargetGroupARN:
    Description: ARN of Target Group
    Value: !Ref TargetGroup
  DatabaseEndpoint:
    Description: Endpoint of the RDS database
    Value: !GetAtt Database.Endpoint.Address
  DatabasePort:
    Description: Port of the RDS database
    Value: !GetAtt Database.Endpoint.Port
