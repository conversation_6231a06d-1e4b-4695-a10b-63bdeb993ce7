AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for network infrastructure for ETender application.

Parameters:
  VPCId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be deployed.

  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: List of subnet IDs for resources.

  DBSecurityGroupId:
    Type: String
    Description: Security Group ID of the database to allow access from app containers.

  EnvironmentName:
    Type: String
    Default: prod
    Description: Environment name (e.g., dev, staging, prod)
    AllowedValues:
      - dev
      - staging
      - prod

  ACMCertificateArn:
    Type: String
    Default: "arn:aws:acm:ap-south-1:881490112356:certificate/c9a86a45-c382-4613-ac38-a5ff3eb983e8"
    Description: ARN of the ACM certificate for HTTPS

Resources:
  # Security Group for the containers
  ContainerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for ECS containers
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          DestinationSecurityGroupId: !Ref DBSecurityGroupId
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0 # For outbound HTTPS connections if needed
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0 # For HTTP connections
        - IpProtocol: tcp
          FromPort: 1024
          ToPort: 65535
          CidrIp: 0.0.0.0/0 # For ephemeral ports
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName

  # Security Group for Load Balancer
  LoadBalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for Load Balancer
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        # HTTP inbound (port 80) from anywhere
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        # HTTPS inbound (port 443) from anywhere
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName

  # Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub "ETender-${EnvironmentName}-LB"
      Scheme: internet-facing
      Subnets: !Ref Subnets
      SecurityGroups:
        - !Ref LoadBalancerSecurityGroup
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName

  # Target Group
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub "ETender-${EnvironmentName}-TG"
      TargetType: ip
      Port: 8000
      Protocol: HTTP
      VpcId: !Ref VPCId
      HealthCheckProtocol: HTTP
      HealthCheckPort: traffic-port
      HealthCheckPath: /
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 4
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName

  # HTTP Listener (redirect HTTP -> HTTPS)
  LoadBalancerListenerHTTP:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref LoadBalancer
      Port: 80
      Protocol: HTTP
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: "443"
            Host: "#{host}"
            Path: "/#{path}"
            Query: "#{query}"
            StatusCode: HTTP_301

  # HTTPS Listener
  LoadBalancerListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref LoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref ACMCertificateArn
      SslPolicy: ELBSecurityPolicy-TLS-1-2-2017-01
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup

  # Security Group Ingress Rules for RDS - explicit permission from container SG
  RDSSecurityGroupIngressFromContainer:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref DBSecurityGroupId
      IpProtocol: tcp
      FromPort: 3306
      ToPort: 3306
      SourceSecurityGroupId: !Ref ContainerSecurityGroup

Outputs:
  LoadBalancerDNS:
    Description: DNS Name of the Load Balancer
    Value: !GetAtt LoadBalancer.DNSName
    Export:
      Name: !Sub "${AWS::StackName}-LoadBalancerDNS"

  LoadBalancerHTTPSListener:
    Description: ARN of HTTPS Listener
    Value: !Ref LoadBalancerListenerHTTPS
    Export:
      Name: !Sub "${AWS::StackName}-LBListener"

  TargetGroupARN:
    Description: ARN of Target Group
    Value: !Ref TargetGroup
    Export:
      Name: !Sub "${AWS::StackName}-TargetGroupARN"

  ContainerSecurityGroupId:
    Description: Security Group ID for the ECS containers
    Value: !Ref ContainerSecurityGroup
    Export:
      Name: !Sub "${AWS::StackName}-ContainerSG"