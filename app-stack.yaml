AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for ECS service and tasks for ETender application.

Parameters:
  DeploymentVersion:
    Type: String
    Default: "v1"
    Description: A version or hash to force new ECS task definition

  BranchName:
    Type: String
    Description: The branch name for the .env file.

  VPCId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be deployed.

  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: List of subnet IDs for ECS tasks.

  ImageTag:
    Type: String
    Default: latest
    Description: Docker image tag for the Laravel application.

  ExistingECRRepositoryUri:
    Type: String
    Description: URI of the existing ECR repository.

  ExistingECSClusterName:
    Type: String
    Description: Name of the existing ECS cluster.

  TargetGroupARN:
    Type: String
    Description: ARN of the Target Group for the load balancer.

  ContainerSecurityGroupId:
    Type: String
    Description: Security Group ID for the ECS containers.

  DatabaseEndpoint:
    Type: String
    Description: Endpoint of the RDS database.

  DatabasePort:
    Type: String
    Description: Port of the RDS database.

  DBName:
    Type: String
    Default: etender
    Description: Name for the RDS database.

  DBUsername:
    Type: String
    Default: etender
    Description: Username for the RDS database.

  DBPassword:
    Type: String
    NoEcho: true
    Description: Password for the RDS database.

  EnvironmentName:
    Type: String
    Default: prod
    Description: Environment name (e.g., dev, staging, prod)
    AllowedValues:
      - dev
      - staging
      - prod

Resources:
  # Task Execution Role
  TaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ECSFargateExecutionPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                  - logs:DescribeLogStreams
                  - logs:DescribeLogGroups
                  - s3:GetObject
                  - s3:ListBucket
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:PutObjectAcl
                  - s3:CreateBucket
                  - s3:ListAllMyBuckets
                  - secretsmanager:GetSecretValue
                  - ssm:GetParameters
                  - ssm:GetParameter
                  - ssm:GetParametersByPath
                Resource: "*"

  # CloudWatch Logs Group for Container Logs
  CloudWatchLogsGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/ecs/etender-${BranchName}-${EnvironmentName}"
      RetentionInDays: 7

  # ECS Task Definition
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    DependsOn: TaskExecutionRole
    Properties:
      Family: !Sub "etender-${EnvironmentName}"
      Cpu: "512"
      Memory: "1024"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt TaskExecutionRole.Arn
      ContainerDefinitions:
        - Name: laravel
          Image: !Sub "${ExistingECRRepositoryUri}:${ImageTag}"
          Cpu: 512
          Memory: 1024
          PortMappings:
            - ContainerPort: 8000
              Protocol: tcp
          Essential: true
          Environment:
            - Name: DEPLOYMENT_VERSION
              Value: !Ref DeploymentVersion
            # Database connection parameters
            - Name: DB_HOST
              Value: !Ref DatabaseEndpoint
            - Name: DB_PORT
              Value: !Ref DatabasePort
            - Name: DB_DATABASE
              Value: !Ref DBName
            - Name: DB_USERNAME
              Value: !Ref DBUsername
            - Name: DB_PASSWORD
              Value: !Ref DBPassword
            # Add HEALTHCHECK flag to indicate we're checking health
            - Name: APP_HEALTHCHECK_ENABLED
              Value: "true"
            # Explicitly use a proper reference to the password parameter
            - Name: DB_PASSWORD
              Value: !Ref DBPassword
          EnvironmentFiles:
            - Type: s3
              Value: !Sub "arn:aws:s3:::etender-laravel-env/env_${BranchName}.env"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref CloudWatchLogsGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: etender
              awslogs-create-group: "true"
              mode: non-blocking
              max-buffer-size: "16m"
              awslogs-multiline-pattern: "^(\\[\\d{4}-\\d{2}-\\d{2}|\\d{4}-\\d{2}-\\d{2})"
          HealthCheck:
            # Use a simpler initial health check that doesn't require DB connectivity
            Command:
              - CMD-SHELL
              - >-
                php -r "echo file_exists('/var/www/html/artisan') ? 'ok' : 'missing artisan';" || exit 1
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60

  # ECS Service with simplified initial deployment
  ECSService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub "etender-${EnvironmentName}-service"
      Cluster: !Ref ExistingECSClusterName
      DesiredCount: 1
      # Using CapacityProviderStrategy with FARGATE_SPOT for cost savings
      CapacityProviderStrategy:
        - CapacityProvider: FARGATE_SPOT
          Weight: 1
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets: !Ref Subnets
          SecurityGroups:
            - !Ref ContainerSecurityGroupId
          AssignPublicIp: ENABLED
      LoadBalancers:
        - TargetGroupArn: !Ref TargetGroupARN
          ContainerName: laravel
          ContainerPort: 8000
      TaskDefinition: !Ref TaskDefinition
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
        MaximumPercent: 200
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName

  # Auto Scaling for ECS
  AutoScalingRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole

  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MinCapacity: 0
      MaxCapacity: 2
      ResourceId: !Sub "service/${ExistingECSClusterName}/${ECSService.Name}"
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
      RoleARN: !GetAtt AutoScalingRole.Arn
    DependsOn: ECSService

  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    DependsOn: ScalableTarget
    Properties:
      PolicyName: ScaleDownPolicy
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref ScalableTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        TargetValue: 20.0
        ScaleInCooldown: 300
        ScaleOutCooldown: 180

Outputs:
  TaskDefinitionArn:
    Description: ARN of the Task Definition
    Value: !Ref TaskDefinition

  ServiceName:
    Description: Name of the ECS Service
    Value: !GetAtt ECSService.Name

  ServiceStatus:
    Description: Status of the ECS Service deployment
    Value: !Sub "https://${AWS::Region}.console.aws.amazon.com/ecs/home?region=${AWS::Region}#/clusters/${ExistingECSClusterName}/services/${ECSService.Name}/details"