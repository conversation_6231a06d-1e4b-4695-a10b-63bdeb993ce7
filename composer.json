{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "ext-zip": "*", "doctrine/dbal": "^3.5", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "hammerstone/fast-paginate": "^0.1.10", "imagekit/imagekit": "^2.0", "itsmalikjones/blade-iconic": "^1.2", "jenssegers/agent": "^2.6", "larabug/larabug": "^2.5", "laravel/breeze": "^1.3", "laravel/framework": "^9.0", "laravel/sanctum": "^2.14", "laravel/tinker": "^2.5", "league/commonmark": "^2.2", "league/csv": "^9.0", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^2.5", "milon/barcode": "^9.0", "mpdf/mpdf": "^8.1", "razorpay/razorpay": "^2.8", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-ignition": "^1.7", "spatie/simple-excel": "^3.4", "symfony/process": "^6.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan vendor:publish --force --tag=livewire:assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "minimum-stability": "dev", "prefer-stable": true}