# E-Tender System: User Roles & Functionality Documentation

## Overview

This document provides a comprehensive reference for all user roles in the e-tender system, detailing their permissions, responsibilities, and access levels.

## Tender Classifications

The system supports different tender classifications that determine how tenders are managed and who can access them:

### Classification Types

1. **Open Tenders**
   - Accessible to all registered company users
   - Visible in the public tender listings
   - Any qualified company can apply and bid
   - Standard visibility and accessibility rules apply

2. **Selective Tenders**
   - Restricted to specifically invited companies only
   - Only visible to companies that have been granted access
   - Limited visibility in public tender listings
   - Requires explicit company selection during tender creation
   - Access controlled through specialized middleware

### Tender Categories

1. **Civil Work**
   - Construction and infrastructure projects
   - Physical development and building works
   - Engineering and structural projects

2. **Service**
   - Service-based tenders
   - Operational and maintenance contracts
   - Non-physical deliverables

3. **Consultancy**
   - Professional consulting services
   - Advisory and expertise-based projects
   - Specialized knowledge deliverables

4. **Procurement**
   - Purchase of goods and equipment
   - Supply chain and inventory acquisition
   - Material and product sourcing

### Tender Types

1. **QCBS (Quality and Cost-Based Selection)**
   - Evaluation based on both technical quality and cost
   - Weighted scoring system for multiple factors
   - Balanced assessment of proposal quality and pricing

2. **Rate**
   - Rate-based tender evaluation
   - Focus on unit pricing and rate structures
   - Standardized cost comparison approach

## Role Hierarchy

The system employs the following role hierarchy, from highest to lowest privileges:

1. Super Administrator
2. Administrator
3. Maker/Checker (Department Users)
4. Evaluator
5. Company
6. Regular User

## Role Definitions & Functionality

### 1. Super Administrator (`super-admin`)

**Primary Function**: Complete system oversight and management with unlimited access rights.

**Key Capabilities**:
- Full system configuration and maintenance
- User management (create, view, update, and delete all users)
- Department management (create, configure, assign)
- Tender oversight across all departments
- System-wide reporting and statistics
- Can override any permission restriction
- View and manage all aspects of the tender process

### 2. Administrator (`admin`)

**Primary Function**: System administration with extensive but controlled access rights.

**Key Capabilities**:
- User management (create, view, update, delete users)
- Department-level tender management
- Access to administrative dashboard
- View department statistics and tender reports
- Manage tender details, including editing capabilities
- Company management and oversight
- Access to protected administrative routes

**Admin Dashboard Functionality**:
- Department overview with tender counts
- Tender status monitoring
- Access to department-wise tender listings
- Filter and search capabilities for tenders

### 3. Maker (`maker`)

**Primary Function**: Create and initiate tenders within assigned department.

**Key Capabilities**:
- Create new tenders with details
- Configure tender parameters (cost, dates, requirements)
- Upload tender documents
- Set tender items and specifications
- Limited to department-specific tenders
- Track created tenders through the process

**Maker-Specific Functionality**:
- Cannot approve their own tenders
- Create and edit functionality with department restrictions
- Document upload capabilities
- Tender timelines and specification management

### 4. Checker (`checker`)

**Primary Function**: Review and approve tenders created by makers.

**Key Capabilities**:
- Review tenders created by makers
- Approve or reject tenders with comments
- Monitor tender status
- View department-specific tenders
- Participate in tender approval workflow
- Access tender documentation

**Checker-Specific Functionality**:
- Cannot create new tenders (review only)
- Approval workflows
- Status change capabilities
- Department-restricted access

### 5. Evaluator (`evaluator`)

**Primary Function**: Evaluate tender bids and participate in bid assessment.

**Key Capabilities**:
- Access to assigned tenders for evaluation
- View and assess bid documents
- Participate in technical bid opening
- Contribute to comparative statements
- Rate and score bidder submissions

### 6. Company (`company`)

**Primary Function**: Participate in the tender process as a bidder.

**Key Capabilities**:
- View and search available tenders
- Purchase tender documents
- Submit EMD (Earnest Money Deposit) payments
- Upload bid documents (technical and financial)
- Track bidding status
- Manage company profile and credentials
- View tender results and comparative statements

**Bidding Capabilities**:
- Search and filter tenders
- View tender-specific details
- Payment processing for document fees and EMD
- Document upload with digital signatures
- Tracking of bid status

**Progress Documentation**:
- Upload tender progress images through the biddings dashboard
- Document work completion and milestone achievements
- Submit visual evidence of tender execution
- Track implementation progress through images
- Associate documentation with specific tender results

### 7. Regular User (`user`)

**Primary Function**: Basic system access with minimal privileges.

**Key Capabilities**:
- View public tender information
- Profile management
- Dashboard access with limited information
- Read-only access to public documents
- Base role with minimal system interaction

## Tender Workflow & Role Interactions

The e-tender system workflow integrates these roles in a structured process:

1. **Tender Creation**: 
   - Maker creates tender with details and documents
   - Sets parameters, dates, and requirements

2. **Tender Review**:
   - Checker reviews tender for accuracy and compliance
   - Approves or rejects with comments

3. **Tender Publication**:
   - Admin publishes approved tenders
   - Sets visibility and accessibility

4. **Bidding Phase**:
   - Companies view published tenders
   - Purchase documents and submit EMD
   - Upload technical and financial bids

5. **Evaluation Phase**:
   - Evaluators assess technical bids
   - Create comparative statements
   - Rate company submissions

6. **Award Process**:
   - Admin/Checker manages bid selection
   - Finalizes tender results
   - Updates tender status

7. **Monitoring & Reporting**:
   - Admin users access statistics and reports
   - Track tender status and participation
   - Generate department-wise analysis
   
8. **Progress Documentation**:
   - Company users upload progress images for awarded tenders
   - Visual documentation of project milestones and completion
   - Evidence collection for tender execution verification

## User Interface Adaptations

The interface adapts based on user role:

- Sidebar navigation shows role-appropriate options
- Different dashboard views per role
- Color-coding for visual role identification
- Action buttons visibility controlled by permissions

## Security Considerations

The role-based access system provides several security advantages:

1. **Separation of Duties**: Different stages of the tender process require different roles
2. **Least Privilege**: Users have only the permissions needed for their role
3. **Accountability**: Actions are traceable to specific users and roles
4. **Department Isolation**: Users can only access data within their department
5. **Hierarchical Oversight**: Higher roles can monitor actions of lower roles

## Conclusion

The e-tender system implements a comprehensive role-based access control mechanism that effectively segregates responsibilities across different user types in the tender management workflow. Each role has clearly defined capabilities and limitations, ensuring proper governance of the tender process while maintaining security and accountability.