services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: etender
    ports:
      - 8000:8000
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
    volumes:
      - .:/var/www/html
    networks:
      - laravel_net
    command: bash -c "php artisan migrate --force && php artisan serve --host=0.0.0.0 --port=8000"

networks:
  laravel_net:
    driver: bridge
