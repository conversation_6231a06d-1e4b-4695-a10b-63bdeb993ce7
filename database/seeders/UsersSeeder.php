<?php

namespace Database\Seeders;

use App\Models\Department;
use Faker\Factory;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // DB::table('users')->truncate();

        // Super Admin User
        User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('secret'),
            'role' => 'super-admin',
        ]);

        // Admin User
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('secret'),
            'role' => 'admin',
        ]);

        // Maker
        User::create([
            'name' => 'Maker',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('secret'),
            'role' => 'maker',
        ]);

        // Checker
        User::create([
            'name' => 'Checker',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('secret'),
            'role' => 'checker',
        ]);
    }
}
