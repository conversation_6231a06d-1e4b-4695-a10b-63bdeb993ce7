<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DepartmentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('departments')->truncate();

        Department::create([
            'name' => 'Other',
            'slug' => 'other'
        ]);

        Department::create([
            'name' => 'PWD',
            'slug' => 'pwd'
        ]);

        Department::create([
            'name' => 'Irrigation',
            'slug' => 'irrigation'
        ]);
    }
}
