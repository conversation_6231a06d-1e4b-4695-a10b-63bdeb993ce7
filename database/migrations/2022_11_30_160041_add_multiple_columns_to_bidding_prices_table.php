<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bidding_prices', function (Blueprint $table) {
            $table->after('bidding_price', function ($table) {
                $table->smallInteger('quantity')->nullable();
                $table->decimal('rate', 16, 2)->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bidding_prices', function (Blueprint $table) {
            //
        });
    }
};
