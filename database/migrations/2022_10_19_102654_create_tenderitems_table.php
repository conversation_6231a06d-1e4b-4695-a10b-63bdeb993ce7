<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tenderitems', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('tender_id')->index()->nullable();
            $table->text('item_type')->nullable();
            $table->string('quantity', 25)->nullable();
            $table->string('unit', 25)->nullable();
            $table->smallInteger('order')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tenderitems');
    }
};
