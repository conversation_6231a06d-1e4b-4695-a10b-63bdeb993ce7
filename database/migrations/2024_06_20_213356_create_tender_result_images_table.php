<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTenderResultImagesTable extends Migration
{
    public function up()
    {
        Schema::create('tender_result_images', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('tender_result_id');
            $table->string('title');
            $table->string('path');
            $table->unsignedBigInteger('size');
            $table->string('extension');
            $table->json('meta')->nullable();
            $table->timestamps();

            $table->foreign('tender_result_id')->references('id')->on('tender_results')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('tender_result_images');
    }
}