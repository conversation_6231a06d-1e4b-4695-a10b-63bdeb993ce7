<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emd_payments', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('user_id')->index()->nullable();
            $table->foreignUlid('tender_id')->index()->nullable();
            $table->foreignUlid('bidding_id')->index()->nullable();
            $table->string('payment_name')->nullable();
            $table->string('payment_bank')->nullable();
            $table->string('payment_type')->nullable();
            $table->string('razorpay_order_id')->nullable();
            $table->string('razorpay_payment_id')->nullable();
            $table->string('payment_ref_no')->nullable();
            $table->string('challan_or_dd_no')->nullable();
            $table->text('photo')->nullable();
            $table->decimal('amount', 20, 2)->nullable();
            $table->string('status')->nullable();
            $table->timestamp('payment_at')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emd_payments');
    }
};
