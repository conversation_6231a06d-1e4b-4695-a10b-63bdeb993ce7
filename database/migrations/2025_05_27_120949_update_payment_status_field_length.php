<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_payments', function (Blueprint $table) {
            // Increase status field length to accommodate enum values like "authorized"
            $table->string('status', 20)->nullable()->change();
        });

        Schema::table('emd_payments', function (Blueprint $table) {
            // Also update EMD payments status field length
            $table->string('status', 20)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_payments', function (Blueprint $table) {
            $table->string('status', 10)->nullable()->change();
        });

        Schema::table('emd_payments', function (Blueprint $table) {
            $table->string('status', 10)->nullable()->change();
        });
    }
};
