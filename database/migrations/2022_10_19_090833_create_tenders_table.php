<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tenders', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('user_id')->index()->nullable();
            $table->foreignUlid('checker_id')->index()->nullable();
            $table->foreignUlid('department_id')->index()->nullable();
            
            // Tender Basic Details
            $table->text('tender_title')->nullable();
            $table->string('tender_number')->nullable();
            $table->string('tender_uin')->nullable();
            $table->string('tender_address')->nullable();
            $table->text('tender_details')->nullable();
            $table->text('tender_called_by')->nullable();
            $table->string('tender_category')->nullable(); // civil work / service / consultancy / procurement  
            $table->string('tender_type', 20)->nullable();
            $table->decimal('tender_cost', 16, 2)->nullable();
            $table->decimal('tender_emd', 16, 2)->nullable();
            $table->decimal('tender_value', 20, 2)->nullable();

            // Dates
            $table->date('published_date')->nullable();
            $table->timestamp('last_date_of_submission')->nullable();
            $table->date('last_date_of_document_sale')->nullable();
            $table->date('last_date_for_clarification')->nullable();

            // Pre Bid Date & Venue
            $table->date('pre_bid_meeting_date')->nullable();
            $table->string('pre_bid_meeting_venue')->nullable();

            // Checker 
            $table->string('tender_status', 20)->nullable();
            $table->text('checker_remarks')->nullable();
            $table->timestamp('live_at')->nullable();

            // tender opening date
            $table->timestamp('tender_opening_date')->nullable();
            $table->json('document_types')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tenders');
    }
};
