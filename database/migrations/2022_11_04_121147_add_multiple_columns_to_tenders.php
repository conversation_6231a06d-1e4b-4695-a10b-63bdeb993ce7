<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tenders', function (Blueprint $table) {
            $table->after('pre_bid_meeting_venue', function ($table) {
                // $table->timestamp('tender_opening_date')->nullable();
                $table->string('tender_opening_otp', 8)->nullable();
                $table->timestamp('tender_opened_at')->nullable();
                $table->string('tender_opening_venue')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tenders', function (Blueprint $table) {
            //
        });
    }
};
