<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_profile_tender', function (Blueprint $table) {
            $table->id();
            $table->string('tender_id');
            $table->string('company_profile_id');
            $table->timestamps();
            
            $table->foreign('tender_id')
                ->references('id')
                ->on('tenders')
                ->onDelete('cascade');
                
            $table->foreign('company_profile_id')
                ->references('id')
                ->on('company_profiles')
                ->onDelete('cascade');
                
            $table->unique(['tender_id', 'company_profile_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_profile_tender');
    }
};
