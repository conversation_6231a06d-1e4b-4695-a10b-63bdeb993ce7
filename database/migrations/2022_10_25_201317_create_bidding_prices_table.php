<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bidding_prices', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('bidding_id')->index()->nullable();
            $table->foreignUlid('tender_id')->index()->nullable();
            $table->foreignUlid('tenderitem_id')->index()->nullable();
            $table->decimal('bidding_price', 20, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bidding_prices');
    }
};
