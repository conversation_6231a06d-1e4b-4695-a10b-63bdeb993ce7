<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('document_payments', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('tender_id')->index()->nullable();
            $table->foreignUlid('bidding_id')->index()->nullable();
            $table->foreignUlid('user_id')->index()->nullable();
            $table->string('payment_ref_no')->nullable();
            $table->decimal('amount', 20, 2);
            $table->string('status', 10)->nullable();
            $table->string('payment_mode', 20)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('document_payments');
    }
};
