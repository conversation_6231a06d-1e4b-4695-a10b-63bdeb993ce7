<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tender_results', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('tender_id')->index()->nullable();
            $table->foreignUlid('bidding_id')->index()->nullable();
            $table->foreignUlid('user_id')->index()->nullable();
            $table->text('message')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tender_results');
    }
};
