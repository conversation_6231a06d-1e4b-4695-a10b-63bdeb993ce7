<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Tender;
use Illuminate\Support\Str;
use App\Policies\UserPolicy;
use App\Policies\TenderPolicy;
use App\Policies\BiddingPolicy;
use App\Policies\EmdPaymentPolicy;
use Illuminate\Support\Facades\Gate;
use App\Policies\DocumentPaymentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        User::class => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        $this->roleGates();
        $this->permissionsGates();

        // Write your custom gates below...
        Gate::define('view-bidding', [BiddingPolicy::class, 'view']);
        Gate::define('view-tdocument', [DocumentPaymentPolicy::class, 'view']);
        Gate::define('update-tender', [TenderPolicy::class, 'update']);
        Gate::define('view-emdpayment', [EmdPaymentPolicy::class, 'view']);
    }

    public function roleGates()
    {
        Gate::before(function ($user) {
            if ($user->isSuperAdministrator()) {
                return true;
            }
        });

        Gate::define('super', function ($user) {
            if ($user->isAdministratorOrSuper()) {
                return true;
            }
        });
        
        foreach (array_keys(config('freshman.roles')) as $role) {
            Gate::define(Str::camel($role), function ($user) use ($role) {
                return $user->role === $role;
            });
        }
    }

    public function permissionsGates()
    {
        foreach (config('freshman.permissions') as $permission) {
            Gate::define(Str::camel($permission), function ($user) use ($permission) {
                // if (in_array($permission, $user->permissions)) {
                //     return true;
                // }
                if ($user->hasPermission($permission)) {
                    return true;
                }
            });
        }
    }
}
