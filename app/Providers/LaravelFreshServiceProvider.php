<?php

namespace App\Providers;

use Livewire\Component;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Services\ImagekitService;
use Illuminate\Support\Facades\Blade;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use League\CommonMark\MarkdownConverter;
use Illuminate\Database\Eloquent\Builder;
use League\CommonMark\Environment\Environment;
use Illuminate\Pagination\LengthAwarePaginator;
use League\CommonMark\Extension\Table\TableExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\TableOfContents\TableOfContentsExtension;
use League\CommonMark\Extension\HeadingPermalink\HeadingPermalinkExtension;

class LaravelFreshServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Model::preventLazyLoading(! $this->app->isProduction());
        
        Component::macro('bannerMessage', function ($message, $style = 'success') {
            $this->dispatchBrowserEvent('banner-message', [
                'style' => $style, // success|danger
                'message' => $message
            ]);
        });

        Component::macro('notify', function ($message, $messageType = 'success') {
            $this->dispatchBrowserEvent('notify', [
                'type' => $messageType, // notice|success|error
                'text' => $message
            ]);
        });

        Str::macro('arrayToHtmlAttributes', function ($array) {
            // return str_replace("=", '=', http_build_query($data, null, ' ', PHP_QUERY_RFC3986)).'';
        
            $tag = '';
            foreach ($array as $key => $value) {
                $tag .= $key . '=' . htmlspecialchars($value) . ' ';
            }
            return $tag;
        });

        Str::macro('bytesToHuman', function ($bytes) {
            $units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

            for ($i = 0; $bytes > 1024; $i++) {
                $bytes /= 1024;
            }

            return round((float) number_format($bytes, 2)) . ' ' . $units[$i];
        });

        Str::macro('generateRandomNumber', function ($uniqueId = null, $prefix = null) {
            if ($prefix) {
                $prefix = $prefix . '_';
            }

            return $prefix . date('ymdhis') . random_int(10000, 99999) . $uniqueId;
        });

        Str::macro('generateBillNumber', function () {
            return date('Ym').substr(implode(null, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 6);
        });

        Str::macro('customMarkdown', function ($markdown) {
            $config = [
                'heading_permalink' => [
                    'html_class' => 'heading-permalink mr-1 no-underline text-gray-400',
                    'id_prefix' => '',
                    'fragment_prefix' => '',
                    'insert' => 'before',
                    'min_heading_level' => 1,
                    'max_heading_level' => 6,
                    'title' => 'Permalink',
                    'symbol' => '#',
                    'aria_hidden' => true,
                ],
                'table' => [
                    'wrap' => [
                        'enabled' => false,
                        'tag' => 'div',
                        'attributes' => [],
                    ],
                ],
            ];

            $environment = new Environment($config);
            $environment->addExtension(new CommonMarkCoreExtension());

            $environment->addExtension(new HeadingPermalinkExtension());
            $environment->addExtension(new TableOfContentsExtension());
            $environment->addExtension(new TableExtension());

            $converter = new MarkdownConverter($environment);
         
            return $converter->convert($markdown);
        });

        Str::macro('greet', function (string $name) {
            $hour = (int) now()->setTimezone('Asia/Kolkata')->format('H');
            
            if ($hour >= 18) {
                return "Good Evening, " . explode(' ', $name)[0];
            } elseif ($hour >= 12) {
                return "Good Afternoon, " . explode(' ', $name)[0];
            } elseif ($hour < 12) {
                return "Good Morning, " . explode(' ', $name)[0];
            } else {
                return "Welcome back, " . explode(' ', $name)[0];
            }
        });

        Str::macro('generateImageKitUrl', function (string $image = null, array $transformation = []) {
            if (is_null($image)) {
                return '';
            }

            return ImagekitService::make()
                ->forImage($image)
                ->addTransformation($transformation)
                ->generateUrl();
        });

        Blade::directive('inr', function ($amount, $decimal = 2) {
            return "<?php echo '&#8377;' . number_format(floatval(str_replace(',', '', $amount)), $decimal); ?>";
        });

        Blade::directive('date', function ($value) {
            if (empty($value)) {
                return null;
            }
            return "<?php echo ($value)->toFormattedDateString(); ?>";
        });

        Blade::directive('dateWithTime', function ($value) {
            return "<?php echo ($value)->format('d M, Y h:i a'); ?>";
        });

        Builder::macro('whereLike', function ($attributes, string $searchTerm) {
            $this->where(function (Builder $query) use ($attributes, $searchTerm) {
                foreach (Arr::wrap($attributes) as $attribute) {
                    $query->when(
                        Str::contains($attribute, '.'),
                        function (Builder $query) use ($attribute, $searchTerm) {
                            if (count(explode('.', $attribute)) > 2):
                                [$relationName, $relationNameTwo, $relationAttribute] = explode('.', $attribute);
     
                            $query->orWhereHas($relationName.'.'.$relationNameTwo, function (Builder $query) use ($relationAttribute, $searchTerm) {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            }); else:
                                [$relationName, $relationAttribute] = explode('.', $attribute);
     
                            $query->orWhereHas($relationName, function (Builder $query) use ($relationAttribute, $searchTerm) {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            });
                            endif;
                        },
                        function (Builder $query) use ($attribute, $searchTerm) {
                            $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                        }
                    );
                }
            });

            return $this;
        });

        Builder::macro('deferredPaginate', function ($perPage = null, $columns = ['*'], $pageName = 'page', $page = null) {
            $model = $this->newModelInstance();
            $key = $model->getKeyName();
            $table = $model->getTable();
         
            $paginator = $this->clone()
                // We don't need them for this query, they'll remain
                // on the query that actually gets the records.
                ->setEagerLoads([])
                // Only select the primary key, we'll get the full
                // records in a second query below.
                ->select("{$table}.{$key}")
                // Pass through the params to the real paginator.
                ->paginate($perPage, $columns, $pageName, $page);
         
            // Add our values in directly using "raw" instead of adding new bindings.
            // This is basically the `whereIntegerInRaw` that Laravel uses in some
            // places, but we're not guaranteed the primary keys are integers, so
            // we can't use that. We're sure that these values are safe because
            // they came directly from the database in the first place.
            $this->query->wheres[] = [
                'type'   => 'InRaw',
                'column' => "{$table}.{$key}",
                // Get the key values from the records on the *current* page, without mutating them.
                'values'  => $paginator->getCollection()->map->getRawOriginal($key)->toArray(),
                'boolean' => 'and',
            ];
            
            // To avoid any merging of columns we'll specify the table.
            $this->query->select("{$table}.*");
         
            // Create a new paginator so that we can put our full records in,
            // not the ones that we modified to select only the primary key.
            return new LengthAwarePaginator(
                $this->get(),
                $paginator->total(),
                $paginator->perPage(),
                $paginator->currentPage(),
                $paginator->getOptions()
            );
        });
    }
}
