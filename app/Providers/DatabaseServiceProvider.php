<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\Event;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Listen for database query events and log slow queries
        DB::listen(function (QueryExecuted $query) {
            if ($query->time > 1000) {
                Log::warning('Slow query detected', [
                    'sql' => $query->sql,
                    'time' => $query->time,
                    'bindings' => $query->bindings,
                ]);
            }
        });

        // We can't directly listen for disconnect events,
        // but we can monitor for connection errors when they occur
        // and handle reconnection ourselves when needed
        $this->app['events']->listen('illuminate.query', function ($query, $bindings, $time, $connectionName) {
            // This event fires on all queries
            // We only use it here to keep the connection alive and log any issues
        });
    }
}