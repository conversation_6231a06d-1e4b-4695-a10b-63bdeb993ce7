<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class DatabaseMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:monitor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check database connectivity for health monitoring';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Attempt a simple database query to test connection
            $result = DB::select('SELECT 1 as connection_test');

            if (isset($result[0]->connection_test) && $result[0]->connection_test == 1) {
                $this->info('Database connection successful');
                return Command::SUCCESS;
            } else {
                $this->error('Database connection test failed: unexpected result');
                return Command::FAILURE;
            }
        } catch (Exception $e) {
            $this->error('Database connection failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}