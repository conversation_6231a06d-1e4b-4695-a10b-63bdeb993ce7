<?php

namespace App\Policies;

use App\Models\User;
use App\Models\EmdPayment;
use App\Models\DocumentPayment;
use App\Enums\RazorpayPaymentStatus;
use Illuminate\Auth\Access\HandlesAuthorization;

class EmdPaymentPolicy
{
    use HandlesAuthorization;

    public function view(User $user, $tender)
    {
        $documentPaymentExists = DocumentPayment::query()
            ->where('tender_id', $tender)
            ->where('user_id', $user->id)
            ->whereIn('status', [
                RazorpayPaymentStatus::AUTHORIZED, 
                RazorpayPaymentStatus::CAPTURED,
                RazorpayPaymentStatus::PAID
            ])
            ->exists();

        // TODO: Also check the
        $tenderEmdPayment = EmdPayment::query()
            ->where('tender_id', $tender)
            ->where('user_id', $user->id)
            ->whereIn('status', [
                RazorpayPaymentStatus::AUTHORIZED, 
                RazorpayPaymentStatus::CAPTURED,
                RazorpayPaymentStatus::PAID,
            ]) // newly added
            ->exists();

        return $documentPaymentExists && ! $tenderEmdPayment ? true : false;        
    }
}
