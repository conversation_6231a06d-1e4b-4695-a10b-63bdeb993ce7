<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Tender;
use App\Models\DocumentPayment;
use App\Enums\RazorpayPaymentStatus;
use Illuminate\Auth\Access\HandlesAuthorization;

class DocumentPaymentPolicy
{
    use HandlesAuthorization;

    public function view(User $user, $tender)
    {
        $documentPayment = DocumentPayment::query()
            ->where('tender_id', $tender)
            ->where('user_id', $user->id)
            ->whereIn('status', [
                RazorpayPaymentStatus::AUTHORIZED, 
                RazorpayPaymentStatus::CAPTURED,
                RazorpayPaymentStatus::PAID
            ])
            ->first();
        
        $tenderFound = Tender::findOrFail($tender); 

        return ($tenderFound && $tenderFound->tender_cost == 0.00) || $documentPayment ? true : false;
    }
}
