<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TinymceImageUploadController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $fileName = $request->file->store('images', 'public');

        return response()->json([
            'location' => $fileName
        ]);
    }
}
