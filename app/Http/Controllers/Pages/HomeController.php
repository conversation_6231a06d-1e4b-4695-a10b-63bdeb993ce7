<?php

namespace App\Http\Controllers\Pages;

use App\Models\Notice;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        return view('pages.home', [
            'latestNotices' => Cache::remember('latest-notices', now()->addMinutes(5), function() {
                return Notice::query()
                    ->latest('id')
                    ->limit(5)
                    ->get();
            })
        ]);
    }
}
