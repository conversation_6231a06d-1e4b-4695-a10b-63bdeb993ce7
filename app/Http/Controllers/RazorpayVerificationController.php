<?php

namespace App\Http\Controllers;

use Razorpay\Api\Api;
use App\Models\Tender;
use App\Models\EmdPayment;
use Illuminate\Http\Request;
use App\Models\DocumentPayment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class RazorpayVerificationController extends Controller
{
    /**
     * Verify and show logs for a Razorpay order ID
     */
    public function verifyOrder(Request $request, $orderId = null)
    {
        // Get order ID from URL parameter or query string
        $orderId = $orderId ?? $request->input('order_id');

        if (!$orderId) {
            return view('razorpay-verification', [
                'error' => 'Please provide a Razorpay order ID',
                'orderId' => null,
                'logs' => [],
                'razorpayData' => null,
                'paymentData' => null,
                'debugInfo' => null
            ]);
        }

        try {
            // Initialize Razorpay API
            $api = $this->initRazorPay();

            // Fetch order details from Razorpay
            $razorpayOrder = null;
            $razorpayPayments = [];
            $razorpayError = null;

            try {
                $razorpayOrder = $api->order->fetch($orderId);

                // Fetch payments for this order
                $razorpayPayments = $api->order->fetch($orderId)->payments();
            } catch (\Exception $e) {
                $razorpayError = $e->getMessage();
                Log::error('Error fetching Razorpay order: ' . $e->getMessage());
            }

            // Find payment records in our database
            $documentPayment = DocumentPayment::where('razorpay_order_id', $orderId)->first();
            $emdPayment = EmdPayment::where('razorpay_order_id', $orderId)->first();

            $paymentData = $documentPayment ?? $emdPayment;
            $paymentType = $documentPayment ? 'document' : ($emdPayment ? 'emd' : 'unknown');

            // Get tender information if payment exists
            $tender = null;
            if ($paymentData && $paymentData->tender_id) {
                $tender = Tender::find($paymentData->tender_id);
            }

            // Extract logs related to this order ID
            $logs = $this->extractLogsForOrder($orderId);

            return view('razorpay-verification', [
                'orderId' => $orderId,
                'logs' => $logs,
                'razorpayData' => [
                    'order' => $razorpayOrder,
                    'payments' => $razorpayPayments,
                    'error' => $razorpayError
                ],
                'paymentData' => $paymentData,
                'paymentType' => $paymentType,
                'tender' => $tender,
                'error' => null
            ]);

        } catch (\Exception $e) {
            Log::error('Error in order verification: ' . $e->getMessage());

            return view('razorpay-verification', [
                'error' => 'Error verifying order: ' . $e->getMessage(),
                'orderId' => $orderId,
                'logs' => [],
                'razorpayData' => null,
                'paymentData' => null
            ]);
        }
    }

    /**
     * Extract logs related to a specific order ID
     */
    private function extractLogsForOrder($orderId)
    {
        $logs = [];
        $logFile = storage_path('logs/laravel.log');

        if (!File::exists($logFile)) {
            return $logs;
        }

        try {
            $logContent = File::get($logFile);
            $logLines = explode("\n", $logContent);

            // Look for lines containing the order ID
            foreach ($logLines as $line) {
                if (strpos($line, $orderId) !== false) {
                    $logs[] = $line;
                }
            }

            // Also look for related payment logs (last 100 lines for context)
            $recentLines = array_slice($logLines, -500); // Get last 500 lines
            foreach ($recentLines as $line) {
                if (strpos($line, 'payment') !== false ||
                    strpos($line, 'razorpay') !== false ||
                    strpos($line, 'callback') !== false) {
                    if (!in_array($line, $logs)) {
                        $logs[] = $line;
                    }
                }
            }

            // Sort logs by timestamp (assuming Laravel log format)
            usort($logs, function($a, $b) {
                // Extract timestamp from log line
                preg_match('/\[(.*?)\]/', $a, $matchesA);
                preg_match('/\[(.*?)\]/', $b, $matchesB);

                if (isset($matchesA[1]) && isset($matchesB[1])) {
                    return strtotime($matchesA[1]) <=> strtotime($matchesB[1]);
                }
                return 0;
            });

        } catch (\Exception $e) {
            Log::error('Error reading log file: ' . $e->getMessage());
        }

        return array_slice($logs, -50); // Return last 50 relevant logs
    }

    /**
     * Initialize Razorpay API
     */
    protected function initRazorPay()
    {
        return new Api(
            config('freshman.razor_pay_key'),
            config('freshman.razor_pay_secret')
        );
    }
}
