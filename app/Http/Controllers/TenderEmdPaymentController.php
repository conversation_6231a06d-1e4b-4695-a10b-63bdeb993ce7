<?php

namespace App\Http\Controllers;

use Razorpay\Api\Api;
use App\Models\Tender;
use App\Models\EmdPayment;
use Illuminate\Http\Request;
use App\Enums\RazorpayPaymentStatus;
use App\Enums\EmdPaymentTypes;
use App\Notifications\EmdAmountReceived;
use App\Rules\CheckvalidIndianRupees;
use App\Traits\WithUniqueRandomNumberGenerator;
use Illuminate\Support\Facades\Log;

class TenderEmdPaymentController extends Controller
{
    use WithUniqueRandomNumberGenerator;

    /**
     * Handle the incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function process(Request $request, $tender)
    {
        try {
            // Make sure we have a single Tender instance, not a collection
            if (!($tender instanceof Tender)) {
                $tender = Tender::findOrFail($tender);
            }

            Log::info('Processing EMD payment for tender: ' . $tender->id);

            $validated = $request->validate([
                'amount' => ['required', new CheckvalidIndianRupees]
            ]);

            $emdAmount = str_replace(',', '', $validated['amount']);
            Log::info('EMD Amount: ' . $emdAmount);

            $emdPaymentFound = EmdPayment::updateOrCreate(
                [
                    'tender_id' => $tender->id,
                    'user_id' => auth()->id()
                ],
                [
                    'tender_id' => $tender->id,
                    'user_id' => auth()->id(),
                    'amount' => $emdAmount * 100
                ]
            );

            // If we already have an order with the same amount and it's not failed
            if ($emdPaymentFound &&
                !is_null($emdPaymentFound->razorpay_order_id) &&
                $emdAmount == $emdPaymentFound->amount / 100 &&
                (!$emdPaymentFound->status || $emdPaymentFound->status !== RazorpayPaymentStatus::FAILED->value)) {

                // Update user context for existing order
                $emdPaymentFound->update([
                    'user_context' => [
                        'user_id' => auth()->id(),
                        'session_id' => session()->getId(),
                        'created_at' => now()->toISOString()
                    ]
                ]);

                // Set session to track current payment
                session(['emd_payment_session_' . $tender->id => $emdPaymentFound->id]);

                Log::info('Reusing existing EMD payment order', [
                    'order_id' => $emdPaymentFound->razorpay_order_id,
                    'payment_id' => $emdPaymentFound->id,
                    'status' => $emdPaymentFound->status
                ]);

                return view('razorpay-emd', [
                    'order_id' => $emdPaymentFound->razorpay_order_id,
                    'amount' => $emdPaymentFound->amount,
                    'name' => auth()->user()->name,
                    'email' => auth()->user()->email,
                    'phone' => auth()->user()->phone ?? '',
                    'previousUrl' => url()->previous(),
                    'tenderId' => $tender->id,
                    'emdPaymentId' => $emdPaymentFound->id
                ]);
            } else {
                try {
                    $api = $this->initRazorPay();

                    Log::info('Creating new Razorpay order for EMD payment', [
                        'payment_id' => $emdPaymentFound->id,
                        'tender_id' => $tender->id,
                        'amount' => $emdPaymentFound->amount,
                        'user_id' => auth()->id(),
                        'existing_status' => $emdPaymentFound->status
                    ]);

                    // Create order in Razorpay
                    $order = $api->order->create([
                        'receipt' => $this->generateUniqueRandomNumber('rpt', ''),
                        'amount' => $emdPaymentFound->amount,
                        'currency' => 'INR',
                        'notes' => [
                            'tender_id' => $tender->id,
                            'emd_payment_id' => $emdPaymentFound->id,
                            'user_id' => auth()->id(),
                        ]
                    ]);

                    Log::info('Razorpay EMD order created successfully', [
                        'order_id' => $order['id'],
                        'amount' => $order['amount'],
                        'tender_id' => $tender->id,
                        'payment_id' => $emdPaymentFound->id
                    ]);

                    // Update local record with order ID, reset status, and user context
                    $emdPaymentFound->update([
                        'razorpay_order_id' => $order['id'],
                        'status' => RazorpayPaymentStatus::CREATED->value,
                        'payment_ref_no' => null,
                        'razorpay_payment_id' => null,
                        'razorpay_signature' => null,
                        'payment_at' => null,
                        'user_context' => [
                            'user_id' => auth()->id(),
                            'session_id' => session()->getId(),
                            'created_at' => now()->toISOString()
                        ]
                    ]);

                    // Set session to track current payment
                    session(['emd_payment_session_' . $tender->id => $emdPaymentFound->id]);

                    // Return view with order details
                    return view('razorpay-emd', [
                        'order_id' => $order['id'],
                        'amount' => $order['amount'],
                        'name' => auth()->user()->name,
                        'email' => auth()->user()->email,
                        'phone' => auth()->user()->phone ?? '',
                        'previousUrl' => url()->previous(),
                        'tenderId' => $tender->id,
                        'emdPaymentId' => $emdPaymentFound->id
                    ]);
                } catch (\Exception $e) {
                    Log::error('Razorpay EMD payment error: ' . $e->getMessage());
                    session()->flash('error', 'Payment gateway error: ' . $e->getMessage());
                    return redirect()->route('front.tenders.emdpayment', $tender->id);
                }
            }
        } catch (\Exception $e) {
            Log::error('EMD payment processing error: ' . $e->getMessage());
            session()->flash('error', 'Error processing payment: ' . $e->getMessage());
            return redirect()->route('front.tenders.emdpayment', $tender->id);
        }
    }

    protected function initRazorPay()
    {
        return new Api(
            config('freshman.razor_pay_key'),
            config('freshman.razor_pay_secret')
        );
    }

    /**
     * Restore user session from payment record context
     */
    protected function restoreUserSession($payment)
    {
        if (!auth()->check() && $payment->user_context) {
            $userContext = is_array($payment->user_context)
                ? $payment->user_context
                : json_decode($payment->user_context, true);

            if ($userContext && isset($userContext['user_id'])) {
                Log::info('Attempting to recover user context from EMD payment record', [
                    'stored_user_id' => $userContext['user_id'],
                    'stored_session_id' => $userContext['session_id'] ?? 'unknown'
                ]);

                // Try to authenticate the user based on stored context
                $user = \App\Models\User::find($userContext['user_id']);
                if ($user) {
                    // Use remember=true to create a persistent session
                    auth()->login($user, true);

                    // Force session regeneration
                    session()->regenerate(true);

                    Log::info('User context recovered successfully for EMD payment', [
                        'user_id' => $user->id,
                        'new_session_id' => session()->getId()
                    ]);

                    return true;
                }
            }
        }

        return false;
    }

    public function callback(Request $request)
    {
        Log::info('EMD payment callback received', $request->all());

        // Force session start if not already started
        if (!session()->isStarted()) {
            session()->start();
        }

        // Try to recover session using session token if provided
        $sessionToken = $request->input('session_token');
        if ($sessionToken && !auth()->check()) {
            Log::info('Attempting EMD session recovery using session token', ['token' => $sessionToken]);
            // Set the session ID from the token to try to recover the session
            session()->setId($sessionToken);
            session()->start();
        }

        // Log session status for debugging
        Log::info('Session status in EMD callback', [
            'session_id' => session()->getId(),
            'session_started' => session()->isStarted(),
            'user_authenticated' => auth()->check(),
            'user_id' => auth()->id(),
            'session_token_provided' => !empty($sessionToken)
        ]);

        // Check if the request contains an error
        if ($request->has('error')) {
            $errorData = $request->input('error');
            Log::error('EMD payment failed', [
                'error' => $errorData,
                'all_request_data' => $request->all()
            ]);

            // Handle error data - it could be a string or array
            $errorCode = 'PAYMENT_FAILED';
            $errorDescription = 'Payment failed';

            if (is_array($errorData)) {
                $errorCode = $errorData['code'] ?? 'PAYMENT_FAILED';
                $errorDescription = $errorData['description'] ?? $errorData['reason'] ?? 'Payment failed';
            } else {
                $errorDescription = $errorData;
            }

            // Try to find the EMD payment by order ID if available
            $orderId = $request->input('razorpay_order_id');

            // If no direct order ID, try to extract from error metadata
            if (!$orderId && is_array($errorData) && isset($errorData['metadata'])) {
                $metadata = json_decode($errorData['metadata'], true);
                if ($metadata && isset($metadata['order_id'])) {
                    $orderId = $metadata['order_id'];
                    Log::info('Extracted order ID from error metadata: ' . $orderId);
                }
            }

            Log::info('Looking for EMD payment with order ID: ' . $orderId);

            if ($orderId) {
                $emdPayment = EmdPayment::where('razorpay_order_id', $orderId)->first();
                Log::info('EMD payment found: ' . ($emdPayment ? 'Yes' : 'No'));

                if ($emdPayment) {
                    Log::info('Updating EMD payment status to failed for tender: ' . $emdPayment->tender_id);

                    // Restore user session if possible
                    $this->restoreUserSession($emdPayment);

                    // Update payment status to failed
                    $emdPayment->update([
                        'status' => RazorpayPaymentStatus::FAILED->value,
                        'payment_ref_no' => $errorCode,
                        'payment_at' => now()
                    ]);

                    // Store failure message in session
                    session()->flash('error', 'EMD Payment failed! Order ID: ' . $orderId . ' | Error: ' . $errorDescription . ' | Date: ' . now()->format('d M Y, h:i A'));

                    Log::info('Redirecting to tender details page with EMD failure message for tender: ' . $emdPayment->tender_id);
                    return redirect()->route('front.tenders.show', $emdPayment->tender_id);
                }
            } else {
                Log::error('No order ID found in request for failed EMD payment');
            }

            // If no order ID, try to find the most recent pending EMD payment for the user
            if (auth()->check()) {
                $recentPayment = EmdPayment::where('user_id', auth()->id())
                    ->whereIn('status', ['pending', 'created', null])
                    ->latest()
                    ->first();

                if ($recentPayment) {
                    Log::info('Found recent pending EMD payment, using it for failure handling');

                    // Update payment status to failed
                    $recentPayment->update([
                        'status' => RazorpayPaymentStatus::FAILED->value,
                        'payment_ref_no' => $errorCode,
                        'payment_at' => now()
                    ]);

                    // Store failure message in session
                    session()->flash('error', 'EMD Payment failed! Order ID: ' . ($recentPayment->razorpay_order_id ?? 'N/A') . ' | Error: ' . $errorDescription . ' | Date: ' . now()->format('d M Y, h:i A'));

                    return redirect()->route('front.tenders.show', $recentPayment->tender_id);
                }
            }

            // Fallback if no order ID or payment record found
            Log::error('No EMD payment record found for failed payment, redirecting to tenders page');
            session()->flash('error', 'EMD payment failed: ' . $errorDescription);
            return redirect()->route('front.tenders');
        }

        try {
            // Get payment verification parameters from Embedded Checkout
            $paymentId = $request->input('razorpay_payment_id');
            $orderId = $request->input('razorpay_order_id');
            $signature = $request->input('razorpay_signature');

            // Log the entire request for debugging
            Log::info('Full EMD callback request data', ['data' => $request->all()]);

            // Try to find the EMD payment by Razorpay order ID
            $emdPayment = EmdPayment::where('razorpay_order_id', $orderId)->first();

            if (!$emdPayment) {
                Log::error('EMD payment not found by order ID', ['order_id' => $orderId]);
                session()->flash('error', 'EMD payment record not found. Please contact support.');
                return redirect()->route('front.tenders');
            }

            // Try to recover user context if session is lost
            $this->restoreUserSession($emdPayment);

            $emdPaymentId = $emdPayment->id;
            $tenderId = $emdPayment->tender_id;

            // Verify the payment signature
            if (empty($paymentId) || empty($orderId) || empty($signature)) {
                session()->flash('error', 'Invalid payment response');
                return redirect()->route('front.tenders');
            }

            // Load the tender relationship if not already loaded
            if (!$emdPayment->relationLoaded('tender')) {
                $emdPayment->load('tender:id,tender_number,tender_uin');
            }

            // Make sure we have a valid tender relationship
            $tenderUin = null;
            if ($emdPayment->tender_id) {
                $tender = Tender::findOrFail($emdPayment->tender_id);
                $tenderUin = $tender->tender_uin;
                $tenderId = $tender->id;
            } else {
                session()->flash('error', 'Tender information not found');
                return redirect()->route('front.tenders');
            }

            // Verify signature
            $api = $this->initRazorPay();
            $attributes = [
                'razorpay_payment_id' => $paymentId,
                'razorpay_order_id' => $orderId,
                'razorpay_signature' => $signature
            ];

            try {
                $api->utility->verifyPaymentSignature($attributes);

                // If verification successful, fetch payment details from Razorpay
                $paymentData = $api->payment->fetch($paymentId);

                // Map the Razorpay payment method to our enum values
                $paymentMethod = strtolower($paymentData->method ?? 'online');

                // Check if the payment method maps to one of our enum values
                $paymentType = match ($paymentMethod) {
                    'netbanking', 'net_banking' => EmdPaymentTypes::NEFT,
                    'upi' => EmdPaymentTypes::NEFT,
                    'card' => EmdPaymentTypes::NEFT,
                    'wallet' => EmdPaymentTypes::NEFT,
                    default => EmdPaymentTypes::NEFT // Default to NEFT for online payments
                };

                // Update payment status
                $emdPayment->fill([
                    'status' => RazorpayPaymentStatus::AUTHORIZED->value,
                    'razorpay_payment_id' => $paymentId,
                    'razorpay_signature' => $signature,
                    'payment_ref_no' => $paymentId,
                    'payment_type' => $paymentType,
                    'payment_at' => now()
                ])->save();

                // Only send notification if user is authenticated
                if (auth()->check()) {
                    auth()->user()->notify(
                        new EmdAmountReceived(
                            $tenderUin,
                            auth()->user()->name,
                            $emdPayment->created_at->toFormattedDateString()
                        )
                    );
                } else {
                    Log::info('User not authenticated during EMD callback, skipping notification');
                }

                Log::info('EMD payment successfully processed: ' . $paymentId);

                // Clear the payment session tracking
                session()->forget('emd_payment_session_' . $tenderId);

                // Regenerate session ID for security
                session()->regenerate();

                // Store payment success message in session
//                session()->flash('success', 'EMD Payment successful! Order ID: ' . $orderId . ' | Payment ID: ' . $paymentId . ' | Amount: ₹' . number_format($emdPayment->amount/100, 2));

                // Redirect directly to tender details page (no intermediate success page)
                return redirect()->route('front.tenders.show', $tenderId);

            } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
                // Failed signature verification - treat as payment failure
                Log::error('Razorpay signature verification failed: ' . $e->getMessage());

                // Update payment status to failed
                $emdPayment->update([
                    'status' => RazorpayPaymentStatus::FAILED->value,
                    'payment_ref_no' => 'SIGNATURE_VERIFICATION_FAILED',
                    'payment_at' => now()
                ]);

                // Store failure message in session
                session()->flash('error', 'EMD Payment verification failed! Order ID: ' . $orderId . ' | Error: Payment signature verification failed. This could indicate a security issue. | Date: ' . now()->format('d M Y, h:i A'));

                return redirect()->route('front.tenders.show', $tenderId);
            }

        } catch (\Exception $e) {
            Log::error('EMD payment callback error: ' . $e->getMessage());

            // Try to find the EMD payment by order ID if available
            $orderId = $request->input('razorpay_order_id');
            Log::info('Looking for EMD payment with order ID: ' . $orderId);

            if ($orderId) {
                $emdPayment = EmdPayment::where('razorpay_order_id', $orderId)->first();
                if ($emdPayment) {
                    // Update payment status to failed
                    $emdPayment->update([
                        'status' => RazorpayPaymentStatus::FAILED->value,
                        'payment_ref_no' => 'PROCESSING_ERROR',
                        'payment_at' => now()
                    ]);

                    // Store failure message in session
                    session()->flash('error', 'EMD Payment processing error! Order ID: ' . $orderId . ' | Error: An error occurred while processing your payment: ' . $e->getMessage() . ' | Date: ' . now()->format('d M Y, h:i A'));

                    return redirect()->route('front.tenders.show', $emdPayment->tender_id);
                }
            }

            session()->flash('error', 'An error occurred while processing your payment.');
            return redirect()->route('front.tenders');
        }
    }
}
