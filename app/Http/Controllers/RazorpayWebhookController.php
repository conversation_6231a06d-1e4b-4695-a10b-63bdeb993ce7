<?php

namespace App\Http\Controllers;

use Razorpay\Api\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Models\DocumentPayment;
use Illuminate\Support\Facades\Log;
use App\Enums\RazorpayPaymentStatus;

class RazorpayWebhookController extends Controller
{
    public function handle(Request $request)
    {
        // Log::info($request);
        // Log::info($request->header('X-Razorpay-Signature'));

    	if ($request->event === 'order.paid') {
            if ($request->hasHeader('X-Razorpay-Signature')) {
                try {
                    $api = $this->initRazorPay();
                    $api->utility->verifyWebhookSignature(
                        $request->getContent(),
                        $request->header('X-Razorpay-Signature'),
                        config('freshman.razor_pay_webhook_secret')
                    );

                    // Log::info($documentPayment);
                    Log::info($request->payload['payment']['entity']['order_id']);
                    $razorpayOrderId = $request->payload['payment']['entity']['order_id'];

                    $documentPayment = DocumentPayment::query()
                        ->where('razorpay_order_id', $razorpayOrderId)
                        ->where('status', '!=', 'paid')
                        ->orWhereNull('status')
                        ->first();

                    if ($documentPayment) {
                        $documentPayment->update([
                            'status' => RazorpayPaymentStatus::PAID->value, // Use our enum value for paid status
                            'payment_ref_no' => $request->payload['payment']['entity']['id'],
                            'payment_at' => Carbon::parse($request->payload['payment']['entity']['created_at'])->setTimezone('Asia/Kolkata'),
                            'payment_mode' => $request->payload['payment']['entity']['method']
                        ]);

                        return response()->json(['status' => 'ok']);
                    }

                } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
                    Log::info($e->getMessage());
                }
            }
    	}

        return response()->json(['status' => 'ok']);
    }

    protected function initRazorPay()
    {
    	return new Api(
    		config('freshman.razor_pay_key'),
    		config('freshman.razor_pay_secret')
    	);
    }
}
