<?php

namespace App\Http\Controllers;

use Razorpay\Api\Api;
use App\Models\Tender;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\DocumentPayment;
use App\Enums\RazorpayPaymentStatus;
use Illuminate\Support\Facades\Log;
use App\Notifications\SuccessfullBidDocumentPurchase;

use App\Traits\WithUniqueRandomNumberGenerator;

class TenderDocumentPaymentController extends Controller
{
    use WithUniqueRandomNumberGenerator;



    /**
     * Handle the incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function process(Request $request, $tender)
    {
        Log::info('Document Payment Process initiated', ['tender_id' => $tender, 'user_id' => auth()->id()]);

        // Make sure we have a single Tender instance, not a collection
        try {
            if (!($tender instanceof Tender)) {
                $tender = Tender::findOrFail($tender);
            }
        } catch (\Exception $e) {
            Log::error('Tender not found in document payment process', ['tender_id' => $tender]);
            session()->flash('error', 'Tender not found');
            return redirect()->route('front.tenders');
        }

        $documentPaymentFound = DocumentPayment::updateOrCreate(
            [
                'tender_id' => $tender->id,
                'user_id' => auth()->id()
            ],
            [
                'tender_id' => $tender->id,
                'user_id' => auth()->id(),
                'amount' => $tender->tender_cost * 100
            ]
        );

        // Check if we have an existing order that's not failed
        if ($documentPaymentFound &&
            !is_null($documentPaymentFound->razorpay_order_id) &&
            (!$documentPaymentFound->status || $documentPaymentFound->status !== RazorpayPaymentStatus::FAILED->value)) {

            // Update user context for existing order
            $documentPaymentFound->update([
                'user_context' => [
                    'user_id' => auth()->id(),
                    'session_id' => session()->getId(),
                    'created_at' => now()->toISOString()
                ]
            ]);

            // Set session to track current payment
            session(['document_payment_session_' . $tender->id => $documentPaymentFound->id]);

            Log::info('Reusing existing document payment order', [
                'order_id' => $documentPaymentFound->razorpay_order_id,
                'payment_id' => $documentPaymentFound->id,
                'status' => $documentPaymentFound->status
            ]);

            return view('razorpay', [
                'order_id' => $documentPaymentFound->razorpay_order_id,
                'amount' => $documentPaymentFound->amount,
                'name' => auth()->user()->name,
                'email' => auth()->user()->email,
                'phone' => auth()->user()->phone,
                'previousUrl' => url()->previous(),
                'tenderId' => $tender->id,
                'documentPaymentId' => $documentPaymentFound->id
            ]);
        } else {
            try {
                $api = $this->initRazorPay();

                Log::info('Creating new Razorpay order for document payment', [
                    'payment_id' => $documentPaymentFound->id,
                    'tender_id' => $tender->id,
                    'amount' => $documentPaymentFound->amount,
                    'user_id' => auth()->id(),
                    'existing_status' => $documentPaymentFound->status
                ]);

                // Create order in Razorpay
                $order = $api->order->create([
                    'receipt' => $this->generateUniqueRandomNumber('rpt', ''),
                    'amount' => $documentPaymentFound->amount,
                    'currency' => 'INR',
                    'notes' => [
                        'tender_id' => $tender->id,
                        'document_payment_id' => $documentPaymentFound->id,
                        'user_id' => auth()->id(),
                    ]
                ]);

                // Update local record with order ID and reset status
                $documentPaymentFound->update([
                    'razorpay_order_id' => $order['id'],
                    'status' => RazorpayPaymentStatus::CREATED->value,
                    'payment_ref_no' => null,
                    'razorpay_payment_id' => null,
                    'razorpay_signature' => null,
                    'payment_mode' => null,
                    'payment_at' => null
                ]);

                Log::info('Razorpay order created successfully', [
                    'order_id' => $order['id'],
                    'amount' => $order['amount'],
                    'tender_id' => $tender->id,
                    'payment_id' => $documentPaymentFound->id
                ]);

                // Store user context in the document payment for callback recovery
                $documentPaymentFound->update([
                    'user_context' => [
                        'user_id' => auth()->id(),
                        'session_id' => session()->getId(),
                        'created_at' => now()->toISOString()
                    ]
                ]);

                // Set session to track current payment
                session(['document_payment_session_' . $tender->id => $documentPaymentFound->id]);

                // Return view with order details
                return view('razorpay', [
                    'order_id' => $order['id'],
                    'amount' => $order['amount'],
                    'name' => auth()->user()->name,
                    'email' => auth()->user()->email,
                    'phone' => auth()->user()->phone,
                    'previousUrl' => url()->previous(),
                    'tenderId' => $tender->id,
                    'documentPaymentId' => $documentPaymentFound->id
                ]);
            } catch (\Exception $e) {
                Log::error('Razorpay document payment error: ' . $e->getMessage());
                session()->flash('error', $e->getMessage());
                return back();
            }
        }
    }

    protected function initRazorPay()
    {
        return new Api(
            config('freshman.razor_pay_key'),
            config('freshman.razor_pay_secret')
        );
    }

    /**
     * Restore user session from payment record context
     */
    protected function restoreUserSession($payment)
    {
        if (!auth()->check() && $payment->user_context) {
            $userContext = is_array($payment->user_context)
                ? $payment->user_context
                : json_decode($payment->user_context, true);

            if ($userContext && isset($userContext['user_id'])) {
                Log::info('Attempting to recover user context from payment record', [
                    'stored_user_id' => $userContext['user_id'],
                    'stored_session_id' => $userContext['session_id'] ?? 'unknown'
                ]);

                // Try to authenticate the user based on stored context
                $user = \App\Models\User::find($userContext['user_id']);
                if ($user) {
                    // Use remember=true to create a persistent session
                    auth()->login($user, true);

                    // Force session regeneration
                    session()->regenerate(true);

                    Log::info('User context recovered successfully', [
                        'user_id' => $user->id,
                        'new_session_id' => session()->getId()
                    ]);

                    return true;
                }
            }
        }

        return false;
    }

    public function callback(Request $request)
    {
        // Enhanced logging for production debugging
        Log::info('Document payment callback received', [
            'request_data' => $request->all(),
            'headers' => $request->headers->all(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'environment' => app()->environment()
        ]);

        // Force session start if not already started
        if (!session()->isStarted()) {
            session()->start();
        }

        // Try to recover session using session token if provided
        $sessionToken = $request->input('session_token');
        if ($sessionToken && !auth()->check()) {
            Log::info('Attempting session recovery using session token', ['token' => $sessionToken]);
            try {
                // Set the session ID from the token to try to recover the session
                session()->setId($sessionToken);
                session()->start();
                Log::info('Session recovery attempted', ['new_session_id' => session()->getId()]);
            } catch (\Exception $e) {
                Log::warning('Session recovery failed', ['error' => $e->getMessage()]);
            }
        }

        // Log session status for debugging
        Log::info('Session status in callback', [
            'session_id' => session()->getId(),
            'session_started' => session()->isStarted(),
            'user_authenticated' => auth()->check(),
            'user_id' => auth()->id(),
            'session_token_provided' => !empty($sessionToken),
            'environment' => app()->environment()
        ]);

        // Try to get order ID early for better error tracking
        $orderId = $request->input('razorpay_order_id');
        if ($orderId) {
            Log::info('Processing callback for order', ['order_id' => $orderId]);
        }

        // Check if the request contains an error
        if ($request->has('error')) {
            $errorData = $request->input('error');
            Log::error('Payment failed', [
                'error' => $errorData,
                'all_request_data' => $request->all()
            ]);

            // Handle error data - it could be a string or array
            $errorCode = 'PAYMENT_FAILED';
            $errorDescription = 'Payment failed';

            if (is_array($errorData)) {
                $errorCode = $errorData['code'] ?? 'PAYMENT_FAILED';
                $errorDescription = $errorData['description'] ?? $errorData['reason'] ?? 'Payment failed';
            } else {
                $errorDescription = $errorData;
            }

            // Try to find the document payment by order ID if available
            $orderId = $request->input('razorpay_order_id');

            // If no direct order ID, try to extract from error metadata
            if (!$orderId && is_array($errorData) && isset($errorData['metadata'])) {
                $metadata = json_decode($errorData['metadata'], true);
                if ($metadata && isset($metadata['order_id'])) {
                    $orderId = $metadata['order_id'];
                    Log::info('Extracted order ID from error metadata: ' . $orderId);
                }
            }

            Log::info('Looking for document payment with order ID: ' . $orderId);

            if ($orderId) {
                $documentPayment = DocumentPayment::where('razorpay_order_id', $orderId)->first();
                Log::info('Document payment found: ' . ($documentPayment ? 'Yes' : 'No'));

                if ($documentPayment) {
                    Log::info('Updating document payment status to failed for tender: ' . $documentPayment->tender_id);

                    // Restore user session if possible
                    $this->restoreUserSession($documentPayment);

                    // Update payment status to failed
                    $documentPayment->update([
                        'status' => RazorpayPaymentStatus::FAILED->value,
                        'payment_ref_no' => $errorCode,
                        'payment_mode' => 'failed',
                        'payment_at' => now()
                    ]);

                    // Store failure message in session
                    session()->flash('error', 'Payment failed! Order ID: ' . $orderId . ' | Error: ' . $errorDescription . ' | Date: ' . now()->format('d M Y, h:i A'));

                    Log::info('Redirecting to document payment page with failure message for tender: ' . $documentPayment->tender_id);
                    return redirect()->route('front.tenders.documentpayment', $documentPayment->tender_id);
                }
            } else {
                Log::error('No order ID found in request for failed payment');
            }

            // If no order ID, try to find the most recent pending document payment for the user
            if (auth()->check()) {
                $recentPayment = DocumentPayment::where('user_id', auth()->id())
                    ->whereIn('status', ['pending', 'created', null])
                    ->latest()
                    ->first();

                if ($recentPayment) {
                    Log::info('Found recent pending payment, using it for failure handling');

                    // Update payment status to failed
                    $recentPayment->update([
                        'status' => RazorpayPaymentStatus::FAILED->value,
                        'payment_ref_no' => $errorCode,
                        'payment_mode' => 'failed',
                        'payment_at' => now()
                    ]);

                    // Store failure message in session
                    session()->flash('error', 'Payment failed! Order ID: ' . ($recentPayment->razorpay_order_id ?? 'N/A') . ' | Error: ' . $errorDescription . ' | Date: ' . now()->format('d M Y, h:i A'));

                    return redirect()->route('front.tenders.documentpayment', $recentPayment->tender_id);
                }
            }

            // Fallback if no order ID or payment record found
            Log::error('No payment record found for failed payment, redirecting to tenders page');
            session()->flash('error', 'Payment failed: ' . $errorDescription);
            return redirect()->route('front.tenders');
        }

        try {
            // Get payment verification parameters from Embedded Checkout
            $paymentId = $request->input('razorpay_payment_id');
            $orderId = $request->input('razorpay_order_id');
            $signature = $request->input('razorpay_signature');

            // Log the entire request for debugging
            Log::info('Full callback request data', ['data' => $request->all()]);

            // Try to find the document payment by Razorpay order ID
            $documentPayment = DocumentPayment::where('razorpay_order_id', $orderId)->first();

            if (!$documentPayment) {
                Log::error('Document payment not found by order ID', ['order_id' => $orderId]);
                session()->flash('error', 'Payment record not found. Please contact support.');
                return redirect()->route('front.tenders');
            }

            // Try to recover user context if session is lost
            $this->restoreUserSession($documentPayment);

            $documentPaymentId = $documentPayment->id;
            $tenderId = $documentPayment->tender_id;

            // Verify the payment signature
            if (empty($paymentId) || empty($orderId) || empty($signature)) {
                session()->flash('error', 'Invalid payment response');
                return redirect()->route('front.tenders');
            }

            // Load the tender relationship if not already loaded
            if (!$documentPayment->relationLoaded('tender')) {
                $documentPayment->load('tender:id,tender_number,user_id');
            }

            // Make sure we have a valid tender relationship
            $tenderNumber = null;
            if ($documentPayment->tender_id) {
                $tender = Tender::findOrFail($documentPayment->tender_id);
                $tenderNumber = $tender->tender_number;
                $tenderId = $tender->id;
            } else {
                session()->flash('error', 'Tender information not found');
                return redirect()->route('front.tenders');
            }

            // Verify signature
            $api = $this->initRazorPay();
            $attributes = [
                'razorpay_payment_id' => $paymentId,
                'razorpay_order_id' => $orderId,
                'razorpay_signature' => $signature
            ];

            try {
                $api->utility->verifyPaymentSignature($attributes);

                // If verification successful, fetch payment details from Razorpay
                $paymentData = $api->payment->fetch($paymentId);

                // Update payment status
                $documentPayment->fill([
                    'status' => RazorpayPaymentStatus::AUTHORIZED->value,
                    'razorpay_payment_id' => $paymentId,
                    'razorpay_signature' => $signature,
                    'payment_ref_no' => $paymentId,
                    'payment_mode' => $paymentData->method ?? 'online',
                    'payment_at' => now()
                ])->save();

                // Only send notification if user is authenticated
                if (auth()->check()) {
                    auth()->user()->notify(new SuccessfullBidDocumentPurchase($tenderNumber));
                } else {
                    Log::info('User not authenticated during callback, skipping notification');
                }

                // Clear the payment session tracking
                session()->forget('document_payment_session_' . $tenderId);

                // Regenerate session ID for security
                session()->regenerate();

//                // Store payment success message in session with audit-compliant details
//                session()->flash('payment_success', [
//                    'type' => 'document',
//                    'order_id' => $orderId,
//                    'payment_id' => $paymentId,
//                    'amount' => number_format($documentPayment->amount / 100, 2),
//                    'payment_date' => now()->format('d M Y, h:i A'),
//                    'message' => 'Document payment completed successfully!'
//                ]);

                // Redirect directly to document payment page (no intermediate success page)
                return redirect()->route('front.tenders.documentpayment', $tenderId);

            } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
                // Failed signature verification - treat as payment failure
                Log::error('Razorpay signature verification failed: ' . $e->getMessage());

                // Update payment status to failed
                $documentPayment->update([
                    'status' => RazorpayPaymentStatus::FAILED->value,
                    'payment_ref_no' => 'SIGNATURE_VERIFICATION_FAILED',
                    'payment_mode' => 'failed',
                    'payment_at' => now()
                ]);

                // Store failure message in session
                session()->flash('error', 'Payment verification failed! Order ID: ' . $orderId . ' | Error: Payment signature verification failed. This could indicate a security issue. | Date: ' . now()->format('d M Y, h:i A'));

                return redirect()->route('front.tenders.documentpayment', $tenderId);
            }

        } catch (\Exception $e) {
            // Enhanced error logging for production debugging
            Log::error('Document payment callback error', [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'session_id' => session()->getId(),
                'user_authenticated' => auth()->check(),
                'user_id' => auth()->id(),
                'environment' => app()->environment(),
                'url' => $request->url(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            // Try to find the document payment by order ID if available
            $orderId = $request->input('razorpay_order_id');

            // If no direct order ID, try to extract from error data if available
            if (!$orderId && $request->has('error')) {
                $errorData = $request->input('error');
                if (is_array($errorData) && isset($errorData['metadata'])) {
                    $metadata = json_decode($errorData['metadata'], true);
                    if ($metadata && isset($metadata['order_id'])) {
                        $orderId = $metadata['order_id'];
                        Log::info('Extracted order ID from exception error metadata: ' . $orderId);
                    }
                }
            }

            if ($orderId) {
                $documentPayment = DocumentPayment::where('razorpay_order_id', $orderId)->first();
                if ($documentPayment) {
                    Log::error('Marking payment as failed due to processing error', [
                        'order_id' => $orderId,
                        'payment_id' => $documentPayment->id,
                        'tender_id' => $documentPayment->tender_id,
                        'original_status' => $documentPayment->status
                    ]);

                    // Don't immediately mark as failed - let webhooks handle it
                    // Only mark as failed if this is a genuine processing error
                    // Check if this might be a successful payment that failed processing
                    $paymentId = $request->input('razorpay_payment_id');
                    $signature = $request->input('razorpay_signature');

                    if ($paymentId && $signature) {
                        // This looks like a successful payment that failed during processing
                        // Don't mark as failed, let webhook handle it or manual verification
                        Log::warning('Payment appears successful but callback processing failed', [
                            'order_id' => $orderId,
                            'payment_id' => $paymentId,
                            'error' => $e->getMessage()
                        ]);

                        session()->flash('warning', 'Payment processing encountered an issue. Your payment may have been successful. Please check your payment status or contact support. Order ID: ' . $orderId);
                    } else {
                        // Genuine failure - mark as failed
                        $documentPayment->update([
                            'status' => RazorpayPaymentStatus::FAILED->value,
                            'payment_ref_no' => 'PROCESSING_ERROR',
                            'payment_mode' => 'failed',
                            'payment_at' => now()
                        ]);

                        session()->flash('error', 'Payment processing error! Order ID: ' . $orderId . ' | Error: An error occurred while processing your payment: ' . $e->getMessage() . ' | Date: ' . now()->format('d M Y, h:i A'));
                    }

                    return redirect()->route('front.tenders.documentpayment', $documentPayment->tender_id);
                }
            }

            session()->flash('error', 'An error occurred while processing your payment. Please try again or contact support.');
            return redirect()->route('front.tenders');
        }
    }
}