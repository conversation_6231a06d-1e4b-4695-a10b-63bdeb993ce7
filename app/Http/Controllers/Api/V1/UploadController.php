<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UploadController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'file' => ['required', 'image', 'max:5048']
        ]);

        if ($request->file('file')) {
            try {
                if (is_array($request->file)) {
                    $path = collect($request->file)->map->store('/', 'api-uploads');
                } else {
                    $path = $request->file->store('/', 'api-uploads');
                }

                return response()->json([
                    'url' => $path
                ], 200);
            } catch (\Exception $e) {
                logger()->error('API file upload failed: ' . $e->getMessage());
                return response()->json([
                    'error' => 'File upload failed',
                    'message' => 'Unable to store file in S3'
                ], 500);
            }
        }

        return response()->json([
            'url' => ''
        ], 200);
    }
}
