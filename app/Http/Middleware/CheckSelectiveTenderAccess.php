<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\Tender;
use App\Enums\TenderClassification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckSelectiveTenderAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to company users
        if (!Auth::check() || Auth::user()->role !== 'company') {
            return $next($request);
        }
        
        // Get the tender from the route if it exists
        $tenderId = $request->route('tender');
        if (!$tenderId) {
            return $next($request);
        }
        
        // Verify tender exists
        try {
            $tender = Tender::findOrFail($tenderId);
        } catch (\Exception $e) {
            return redirect()->route('front.tenders')->with('error', 'Tender not found');
        }
        
        // Make sure we're working with a Model instance, not a Collection
        if ($tender instanceof \Illuminate\Database\Eloquent\Collection) {
            // If it's somehow a collection, grab the first item
            if ($tender->count() > 0) {
                $tender = $tender->first();
            } else {
                return redirect()->route('front.tenders')->with('error', 'Tender not found');
            }
        }
        
        // If it's an open tender, allow access
        if ($tender->isOpenTender()) {
            return $next($request);
        }
        
        // If it's a selective tender, check access
        if ($tender->isSelectiveTender()) {
            // Check if the company has access
            if (!$tender->companyCanAccessTender(Auth::user())) {
                return redirect()->route('front.tenders')
                    ->with('error', 'You do not have access to this selective tender');
            }
        }
        
        return $next($request);
    }
}
