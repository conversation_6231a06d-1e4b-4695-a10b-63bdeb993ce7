<?php

namespace App\Http\Middleware;

use App\Models\Tender;
use Closure;
use Illuminate\Http\Request;

class CheckValidTender
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {   
        $tender = Tender::findOrFail($request->route('tender'));

        // if ($tender && $tender->last_date_of_submission < now()) {
        //     abort(404);
        // }   

        return $next($request);
    }
}
