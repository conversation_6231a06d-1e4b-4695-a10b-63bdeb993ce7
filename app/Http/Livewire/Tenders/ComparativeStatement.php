<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use App\Models\TenderResult;
use Livewire\WithFileUploads;
use App\Traits\InteractsWithBanner;
use Illuminate\Validation\Rules\File;

class ComparativeStatement extends Component
{
    use WithFileUploads;
    use InteractsWithBanner;

    public $tenderId;
    public $comparativeStatement;

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function save()
    {
        $this->validate([
            'comparativeStatement' => [
                'required',
                File::types(['pdf'])->max(8 * 1024),
            ],
        ]);


        $this->tender->update([
            'comparative_statement' => $this->comparativeStatement->store('/', 'comparative-statements'),
        ]);

        $this->banner('Comparative Statement added.');

        return redirect()->route('tenders.biddings', $this->tenderId);
    }

    public function render()
    {
        return view('livewire.tenders.comparative-statement');
    }
}
