<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Http\Livewire\Modal;
use App\Traits\InteractsWithBanner;
use Livewire\WithFileUploads;
use Illuminate\Validation\Rules\File;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Result extends Modal
{
    use WithFileUploads;
    use InteractsWithBanner;

    public $tenderId;
    public $tenderNumber;
    public $aocDocument;
    public $company;
    public $message;


    public $base64;
    public $img_content;
    public $aoc;
    public $signedAocDocument;

    public function mount(Tender $tender)
    {
        $this->tenderId = $tender->id;
    }

    public function save()
    {
        $validated = $this->validate([
            'company' => ['required'],
            'message' => ['nullable'],
            'aocDocument' => [
                'required',
                File::types(['pdf'])->max(8 * 1024),
            ],
        ]);

        $pdfData = base64_decode($this->signedAocDocument);
        $documentName = 'aoc-' . $this->tenderId . '.pdf';
        // $file = new UploadedFile($pdfData, $documentName, 'application/pdf', null, false);
        // dd($file);
        Storage::disk('aoc-documents')->put($documentName, $pdfData);

        $this->tender->result()->updateOrCreate([
            'bidding_id' => $validated['company'],
            'tender_id' => $this->tenderId,
        ], [
            'bidding_id' => $validated['company'],
            'message' => $validated['message'],
            'user_id' => $this->user->id,
            'aoc_document' => $documentName
        ]);

        // $this->closeModal();

        // $this->emit('refreshBiddings');

        $this->banner('Tender result added.');

        return redirect()->route('tenders.biddings', $this->tenderId);
    }

    public function getTenderProperty()
    {
        return Tender::with('acceptedBiddings')->findOrFail($this->tenderId);
    }

    public function getCompaniesProperty()
    {
        return $this->tender->acceptedBiddings->pluck('company_name', 'id')->all();
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function updatedAocDocument($file)
    {
        $certificate_path = $file->getRealPath();
        $type = pathinfo($certificate_path, PATHINFO_EXTENSION);
        $data = file_get_contents($certificate_path);
        $this->base64 = base64_encode($data);
        $img_base64 = public_path('valid.txt');
        $img_base64_handler = fopen($img_base64, 'r');
        $this->img_content = fread($img_base64_handler, filesize($img_base64));
        fclose($img_base64_handler);
    }

    public function render()
    {

        return view('livewire.tenders.result', [
            'tid' => $this->tenderId
        ]);
    }
}
