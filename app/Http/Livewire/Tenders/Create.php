<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Models\User;
use App\Models\CompanyProfile;
use Livewire\Component;
use App\Enums\TenderType;
use App\Enums\TenderCategory;
use App\Enums\TenderStatus;
use App\Enums\TenderClassification;
use App\Traits\InteractsWithBanner;
use App\Traits\WithUniqueRandomNumberGenerator;
use Illuminate\Support\Facades\DB;

class Create extends Component
{
    use InteractsWithBanner;
    use WithUniqueRandomNumberGenerator;

    // Basic details
    public $tender_category;
    public $tender_type;
    public $tender_title;
    public $tender_number;
    public $tender_address;
    public $tender_details;
    public $tender_called_by;
    
    // Finance details
    public $tender_cost = 0;
    public $tender_emd;
    public $tender_value = 0;
    
    // Dates
    public $published_date;
    public $bid_starting_date;
    public $technicalbid_opening_date;
    public $last_date_of_submission;
    public $last_date_of_document_sale;
    public $last_date_for_clarification;
    public $pre_bid_meeting_date;
    public $pre_bid_meeting_venue;
    
    // Classification and companies
    public $tender_classification = 'open'; // Default to 'open'
    public $company_details = [];
    public $available_companies = [];

    protected $rules = [
        'tender_category' => ['required'],
        'tender_type' => ['required'],
        'tender_title' => ['required'],
        'tender_number' => ['required'],
        'tender_address' => ['required'],
        'tender_details' => ['required'],
        'tender_called_by' => ['required'],
        'tender_cost' => ['required', 'regex:/^(?:\d+|\d{1,2},(?:\d{2},)*\d{3})(?:\.\d{2})?$/'],
        'tender_value' => ['required', 'regex:/^(?:\d+|\d{1,2},(?:\d{2},)*\d{3})(?:\.\d{2})?$/'],
        'tender_emd' => ['required'],
        'published_date' => ['required'],
        'bid_starting_date' => ['required'],
        'technicalbid_opening_date' => ['required'],
        'last_date_of_submission' => ['required'],
        'last_date_of_document_sale' => ['required'],
        'last_date_for_clarification' => ['required'],
        'pre_bid_meeting_date' => ['nullable'],
        'pre_bid_meeting_venue' => ['nullable'],
        'tender_classification' => ['required', 'in:open,selective'],
        'company_details' => ['nullable', 'array'],
    ];

    public function mount()
    {
        // Prevent checker users from accessing the create page
        if (auth()->user()->role === 'checker') {
            $this->banner('You do not have permission to create tenders.', 'danger');
            return redirect()->route('tenders');
        }
        
        // Set default classification explicitly
        $this->tender_classification = 'open';
        $this->loadCompanies();
    }
    
    protected function loadCompanies()
    {
        $companies = User::where('role', 'company')
            ->with('companyProfile')
            ->get()
            ->filter(function($user) {
                return $user->companyProfile !== null;
            })
            ->map(function($user) {
                return [
                    'id' => $user->companyProfile->id,
                    'name' => $user->name . ' (' . ($user->companyProfile->company_name ?? 'No Company Name') . ')'
                ];
            });
            
        $this->available_companies = $companies;
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName, $this->getValidationRules());
    }

    /**
     * Get dynamic validation rules based on the current state
     */
    private function getValidationRules()
    {
        $rules = $this->rules;

        // If tender classification is selective, company_details is required
        if ($this->tender_classification === 'selective') {
            $rules['company_details'] = ['required', 'array', 'min:1'];
        } else {
            $rules['company_details'] = ['nullable', 'array'];
        }

        return $rules;
    }

    public function save()
    {
        // Prevent checker users from creating tenders
        if (auth()->user()->role === 'checker') {
            $this->banner('You do not have permission to create tenders.', 'danger');
            return;
        }
        
        $validated = $this->validate($this->getValidationRules());

        // Clean up numeric values by removing commas
        $validated['tender_cost'] = str_replace(',', '', $validated['tender_cost']);
        $validated['tender_value'] = str_replace(',', '', $validated['tender_value']);

        // Start a database transaction to ensure all operations succeed or fail together
        DB::beginTransaction();

        try {
            // Create the tender record
            $tender = Tender::create($validated + [
                    'tender_uin' => $this->generateUniqueRandomNumber(),
                    'user_id' => $this->user->id,
                    'department_id' => $this->user->department_id,
                    'tender_status' => TenderStatus::PENDING->value
                ]);

            // If this is a selective tender, save the company relationships
            if ($this->tender_classification === 'selective' && !empty($this->company_details)) {
                $now = now();
                $companyData = collect($this->company_details)->mapWithKeys(function($id) use ($now) {
                    return [$id => [
                        'created_at' => $now,
                        'updated_at' => $now
                    ]];
                })->all();
                
                $tender->companies()->attach($companyData);
            }

            // Commit the transaction
            DB::commit();

            // Reset the form
            $this->resetExcept('tender_classification', 'available_companies');
            $this->company_details = [];

            // Show success message
            $this->banner('Tender details saved successfully.');

            // Redirect to tenders list
            return redirect()->route('tenders');

        } catch (\Exception $e) {
            // If anything goes wrong, rollback the transaction
            DB::rollBack();

            // Log the error
            \Log::error('Failed to save tender: ' . $e->getMessage());

            // Show error message
            $this->banner('Failed to save tender. Please try again.', 'danger');
        }
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getTenderCategoriesProperty()
    {
        return TenderCategory::cases();
    }

    public function getTenderTypesProperty()
    {
        return TenderType::cases();
    }
    
    public function getTenderClassificationsProperty()
    {
        return TenderClassification::cases();
    }

    // This is now handled by Alpine.js on the frontend
    public function updatedTenderClassification($value)
    {
        // Reset company details if classification changes to open
        if ($value === 'open') {
            $this->company_details = [];
        }
    }

    public function render()
    {
        return view('livewire.tenders.create')
            ->layoutData(['title' => 'Create Tender']);
    }
}