<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Models\Tenderitem;
use Livewire\Component;

class Items extends Component
{
    public $tenderId;
    public $item_type;
    public $estimated_price = 0;
    public $quantity;
    public $unit = 'nos';

    protected $listeners = [
        'refreshDocumentTypes' => '$refresh'
    ];

    public function addItem()
    {
        $validated = $this->validate([
            'item_type' => ['required'],
            'estimated_price' => ['required'],
            'quantity' => ['required'],
            'unit' => ['required'],
        ], [
            'item_type.required' => 'Item description is required',
            'quantity.required' => 'Required',
            'unit.required' => 'Required',
            'estimated_price.required' => 'Required'
        ]);
 
        $this->tender->tenderitems()->create($validated);

        $this->reset(['item_type', 'estimated_price', 'quantity', 'unit']);

        $this->emit('refreshDocumentTypes');
        $this->emit('refreshTenders');

        $this->notify('Tender items saved.');
    }

    public function deleteItemType($itemId)
    {
        $tenderItem = Tenderitem::findOrFail($itemId);
        $tenderItem->delete();

        $this->emit('refreshDocumentTypes');
        $this->emit('refreshTenders');

        $this->notify('Tender items deleted.');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }
 
    public function render()
    {
        return view('livewire.tenders.items', [
            'items' => $this->tender->tenderitems
        ]);
    }
}
