<?php

namespace App\Http\Livewire\Tenders;

// use App\Http\Livewire\Modal;

use App\Models\Tender;
use App\Models\Bidding;
use Livewire\Component;
use App\Models\OtpOfficial;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Notifications\TenderBidOpened;

class OpenTender extends Component
{
    public $tenderId;
    public $otp;

    public $showOpenTenderModal = false;

    protected $listeners = [
        'showOpenTenderModal'
    ];

    public function showOpenTenderModal()
    {
        if (is_null($this->tender->tender_opening_otp)) {
            $otpGenerated = mt_rand(111111, 999999);
        } else {
            $otpGenerated = $this->tender->tender_opening_otp;
        }

        // TODO: Send to the concerned official via SMS
        // Notification::send($users, new SendTenderOpeningOtp(123456, $this->tender->tender_number));
        //  Find otp_officials for the concerned logged in department
        try {
            DB::transaction (function() use ($otpGenerated) {
                if (!is_null($this->officialUserPhone)) {
                    // SmsService::make('63749128ed5e82760666890a')
                    SmsService::make('637490e47c248750be1db026')
                        ->to($this->officialUserPhone)
                        ->addVariables([
                            'number' => $otpGenerated,
                            'tendernumber' => $this->tender->tender_uin
                        ])
                        ->send();
                }
 
                $this->tender->update([
                    'tender_opening_otp' => $otpGenerated
                ]);
            });
        } catch (\Exception $e) {
            // dd($e);
            $this->notify('Something went wrong.', 'error');
        }
    
        $this->resetErrorBag();
        $this->showOpenTenderModal = true;
    }

    public function closeModal()
    {
        $this->resetErrorBag();
        $this->showOpenTenderModal = false;
    }

    public function verifyOtp()
    {
        // TODO: must match with the generated OTP send
        $otp = $this->tender->tender_opening_otp;
        
        $this->validate([
            'otp' => ['required', 'digits:6', function ($attribute, $value, $fail) use ($otp) {
                if ($value != $otp) {
                    return $fail('The '. $attribute . ' is invalid.');
                }
            }]
        ]);

        try {
            $biddings = Bidding::with(['user', 'tender'])->accepted()->get();
            $biddings->each(function ($bidding) {
                $bidding->user->notify(
                    new TenderBidOpened(
                        $this->tender->tender_uin, 
                        now()->toFormattedDateString()
                    )
                );
            });

            $this->tender->update([
                'tender_opening_otp' => null,
                'tender_opened_at' => now()
            ]);
    
            $this->closeModal();
    
            $this->emit('refreshBiddings');
    
            $this->notify('Tender successfully opened.');
        } catch (\Exception $e) {
            dd($e);
            $this->notify('Something went wrong.', 'error');
        }
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getOfficialUserPhoneProperty()
    {
        $otpOfficial = OtpOfficial::where('department_id', $this->user->department_id)->first();

        return $otpOfficial ? $otpOfficial->phone : null;
    }

    public function render()
    {
        return view('livewire.tenders.open-tender');
    }
}
