<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use Illuminate\Validation\Rule;

class Review extends Component
{
    public $tenderId;
    public $review;
    public $status;
    public $liveAt;
 
    protected $listeners = [
        'refreshReview' => '$refresh'
    ];
 
    public function saveReview()
    {
        $validated = $this->validate([
            'review' => ['required'],
            'status' => ['required', Rule::in(['approved', 'cancelled'])],
        ]);

        $this->tender->fill([
            'tender_status' => $validated['status'],
            'checker_remarks' => $validated['review'],
            'live_at' => now()
        ])->save();
        
        $this->reset(['status', 'review']);
        $this->emit('refreshTenders');
        $this->notify('Tender review saved.');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function render()
    {
        return view('livewire.tenders.review', [
            'tenderReview' => [
                'tender_status' => $this->tender->tender_status,
                // 'checker_remarks' => preg_replace('/(<br\s*\/>\s*)+/', '<p>&nbsp;</p>', nl2br(e($this->tender->checker_remarks))),
                'checker_remarks' => nl2br(e($this->tender->checker_remarks)),
                'live_at' => $this->tender?->live_at?->format('d M, Y h:i a')
            ]
        ]);
    }
}
