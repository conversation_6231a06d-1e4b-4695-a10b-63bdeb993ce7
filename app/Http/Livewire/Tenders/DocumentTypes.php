<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use Illuminate\Support\Str;

class DocumentTypes extends Component
{
    public $tenderId;
    public $documentName;
    public $type = 'application/pdf';
    public $size = '2MB';

    protected $listeners = [
        'refreshDocumentTypes' => '$refresh'
    ];

    public function addDocument()
    {
        $validated = $this->validate([
            'documentName' => ['required'],
            'type' => ['required'],
            'size' => ['required'],
        ], [
            'type.required' => 'Required',  
            'size.required' => 'Required'   
        ]);

        $validated['id'] = Str::uuid()->toString();
 
        if ($this->tender->document_types) {
            $this->tender->document_types = collect($this->tender->document_types)->push($validated)->toArray();
        } else {
            $this->tender->document_types = [$validated];
        }
        
        $this->tender->save();
        
        $this->reset(['documentName', 'type', 'size']);

        $this->emit('refreshDocumentTypes');
        $this->emit('refreshTenders');

        $this->notify('Tender document types saved.');
    }

    public function deleteDocumentType($documentId)
    {
        $documentTypes = collect($this->tender->document_types)->filter(fn ($item) => $item['id'] != $documentId)->toArray();

        $this->tender->document_types = count($documentTypes) == 0 ? null : $documentTypes;
        $this->tender->save();

        $this->emit('refreshDocumentTypes');
        $this->emit('refreshTenders');

        $this->notify('Tender document type deleted.');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }
 
    public function render()
    {
        return view('livewire.tenders.document-types', [
            'tenderDocumentTypes' => $this->tender->document_types
        ]);
    }
}
