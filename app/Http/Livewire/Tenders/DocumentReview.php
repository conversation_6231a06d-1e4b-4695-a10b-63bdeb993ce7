<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Bidding;
use App\Http\Livewire\Modal;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;
use App\Notifications\SendTenderBidAccepted;
use App\Notifications\SendTenderBidRejected;
use App\Models\DocumentReview as DocumentReviewModel;

class DocumentReview extends Modal
{
    public $tenderId;
    public $biddingId;
    public $review;
    public $status;
    public $companyName;
    public $tenderNumber;
    public $tenderUin;
    
    public function show($bidding = null, $company = null)
    {
        $this->biddingId = $bidding;
        $this->companyName = $company;

        $this->openModal();
    }

    public function save()
    {
        $validated = $this->validate([
            'review' => ['required', 'string'],
            'status' => ['required', 'in:accept,reject']
        ]);

        try {
            DB::transaction(function () use ($validated) {
                DocumentReviewModel::create([
                    'user_id' => $this->user->id,
                    'bidding_id' => $this->biddingId,
                    'tender_id' => $this->tenderId,
                    'review' => $validated['review'],
                    'status' => $validated['status'] 
                ]);
        
                if ($validated['status'] === 'accept') {
                    $this->bidding->user->notify(new SendTenderBidAccepted($this->tenderUin));
                    $this->bidding->update(['bidding_status' => 'accepted']);
                }
        
                if ($validated['status'] === 'reject') {
                    $this->bidding->user->notify(new SendTenderBidRejected($this->tenderUin));
                    $this->bidding->update(['bidding_status' => 'rejected']);
                }
            });

            $this->reset(['review', 'status']);

            $this->emit('refreshBiddings');

            $this->closeModal();

            $this->notify('Tender review added.');
        } catch (\Exception $e) {
            // dd($e);
            $this->notify('Something went wrong. Try again.', 'error');
        }
    }

    public function getBiddingProperty()
    {
        return Bidding::query()
            ->with('user:id,name,phone,email')
            ->findOrFail($this->biddingId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.tenders.document-review');
    }
}
