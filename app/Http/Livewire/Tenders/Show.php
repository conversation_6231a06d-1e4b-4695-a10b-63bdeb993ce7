<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;

class Show extends Component
{
    public $tender;
    
    protected $listeners = [
        'refreshTenders' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        $tender->loadExists(['documents', 'tenderitems']);
 
        $this->tender = $tender;
    }

    public function render()
    {
        return view('livewire.tenders.show');
    }
}
