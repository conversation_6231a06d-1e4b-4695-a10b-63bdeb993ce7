<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Validation\Rules\File;

class PreBidMinutesUpload extends Component
{
    use WithFileUploads;

    public $tenderId;
    public $pre_bid_meeting_minutes;
    
    protected $listeners = [
        'refreshTenderDocuments' => '$refresh'
    ];

    public function saveDocument()
    {
        $validated = $this->validate([
            // 'pre_bid_meeting_minutes' => ['required', 'file', 'mimes:pdf', 'max:8192'],
            'pre_bid_meeting_minutes' => [
                'required', 
                File::types(['pdf'])->max(8 * 1024),
            ],
        ]);

        $this->tender->update([
            'pre_bid_meeting_minutes' => $validated['pre_bid_meeting_minutes']->store('/', 'minutes-documents')
        ]);

        $this->reset('pre_bid_meeting_minutes');
        $this->dispatchBrowserEvent('destroy-filepond');

        $this->notify('Pre-bid meeting minutes added.');

        $this->emit('refreshTenderDocuments');
        $this->emit('refreshTenders');
    }

    public function deleteDocument()
    {
        $this->tender->pre_bid_meeting_minutes = null;
        $this->tender->save();

        // TODO: delete from storage as well

        $this->emit('refreshTenderDocuments');
        $this->emit('refreshTenders');

        $this->notify('Pre-bid meeting minutes deleted.');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function render()
    {
        return view('livewire.tenders.pre-bid-minutes-upload', [
            'preBidMinutesDocument' => $this->tender->pre_bid_minutes_document_path
        ]);
    }
}
