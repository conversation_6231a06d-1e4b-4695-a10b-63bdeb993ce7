<?php

namespace App\Http\Livewire\Tenders;

use App\Models\OtpOfficial;
use App\Rules\CheckValidPhoneNumber;
use Livewire\Component;

class OfficialPersonForOtp extends Component
{
    public $name;
    public $phone;

    public function saveOfficial()
    {
        $validated = $this->validate([
            'name' => ['bail', 'required', 'string'],
            'phone' => ['required', 'digits:10', new CheckValidPhoneNumber],
        ]);

        OtpOfficial::updateOrCreate(
            [
                'user_id' => $this->user->id,
                'department_id' => auth()->user()->department_id
            ],
            [
                'name' => $validated['name'],
                'phone' => $validated['phone'],
                'department_id' => auth()->user()->department_id
            ]
        );

        $this->emit('saved');

        $this->notify('Official person saved.');
    }

    public function getOfficialProperty()
    {
        return OtpOfficial::where('user_id', $this->user->id)->first();
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        if ($this->official) {
            $this->name = $this->official->name;
            $this->phone = $this->official->phone;
        }

        return view('livewire.tenders.official-person-for-otp');
    }
}
