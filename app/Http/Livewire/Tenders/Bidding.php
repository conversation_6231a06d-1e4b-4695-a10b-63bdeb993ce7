<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;
use Illuminate\Support\Str;
use Spatie\SimpleExcel\SimpleExcelWriter;

class Bidding extends Component
{
    public $tenderId;
    public $tenderNumber;
    
    public $name;
    public $biddings;

    protected $listeners = [
        'refreshBiddings' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        $tender->loadMissing([
            'biddings.biddingDocuments', 
            'biddings.pricings.tenderitem', 
            'biddings.pricings.bidding', 
            'biddings.documentReview',
            'biddings.emdPayment'
        ]);
       
        $this->tenderId = $tender->id;
        $this->tenderNumber = $tender->tender_number;
        $this->biddings = $tender->biddings;
    }

    public function download()
    {
        $data = $this->acceptedTenderBiddings;

        $filename = 'tender-financialbid-' . date('Y-m-d') . '-' . Str::random(6) . '.csv';

        return response()->streamDownload(function () use ($data, $filename) {
            $writer = SimpleExcelWriter::streamDownload($filename);
            $writer->addRows($data);
            $writer->close();
        }, $filename);

        // $headers = [
        //     'Cache-Control'       => 'must-revalidate, post-check=0, pre-check=0',
        //     'Content-type'        => 'text/csv',
        //     'Content-Disposition' => "attachment; filename=$filename",
        //     'Expires'             => '0',
        //     'Pragma'              => 'public',
        // ];

        // return response()->stream(function () use ($data) {
        //     $csvWriter = Writer::createFromFileObject(
        //         new SplFileObject('php://output', 'w+')
        //     );
        //     $csvWriter->insertOne(array_keys(collect($data)->first()));
        //     $csvWriter->insertAll($data);
        // }, 200, $headers);
    }

    public function getTenderProperty()
    {
        return Tender::query()
            ->withExists(['biddingPrices', 'result', 'documentReviews'])
            ->with(['acceptedBiddings.pricings.tenderitem', 'biddingCompanies', 'tenderitems.biddingPrices.bidding', 'result.bidding'])
            ->findOrFail($this->tenderId);
    }

    public function getBiddingsProperty()
    {
        return $this->tender->acceptedBiddings;
    }

    public function getAcceptedTenderBiddingsProperty()
    {
        return $this->tender->tenderitems->map(fn ($item) => [
            "item" => $item->item_type,
            "quantity" => $item->quantity,
            "unit" => $item->unit,
            "estimated_price" => $item->estimated_price,

            ...$item->biddingPrices->flatMap(function ($biddingPrice) {
                if ($biddingPrice->bidding) {
                    return [
                        $biddingPrice?->bidding?->company_name => "{$biddingPrice->bidding_price}" ?? 0
                    ];
                }
            })->all()
        ])->all();
    }

    public function isTenderOpeningDateToday()
    {
        return $this->tender->tender_opening_date <= now();
    }

    public function render()
    {
        return view('livewire.tenders.bidding', [
            'tender' => $this->tender,
            'biddingHeaders' => $this->acceptedTenderBiddings ? array_keys($this->acceptedTenderBiddings[0]) : []
        ]);
    }
}
