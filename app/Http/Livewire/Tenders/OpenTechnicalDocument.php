<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Models\User;
use Livewire\Component;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;
use App\Enums\TenderStatus;

class OpenTechnicalDocument extends Component
{
    public $tenderId;
    public $otps = [];
    public $evaluators = [];
    public $show = false;

    protected $listeners = [
        'refreshTenders'
    ];

    public function mount($tender)
    {
        $this->tenderId = $tender;

        // Get the authenticated user directly instead of using property accessor
        $user = auth()->user();

        // Ensure user is authenticated and has a department
        if (!$user || !$user->department_id) {
            abort(403, 'Unauthorized access or missing department information.');
        }

        $this->tender = Tender::with('evaluators')
            ->where('department_id', $user->department_id)
            ->findOrFail($tender);

        $this->evaluators = $this->tender->evaluators()->get()->toArray();
        $this->sendOtps();
    }

    private function sendOtps()
    {
        $otpGenerated = [];

        try {
            DB::transaction(function () use (&$otpGenerated) {
                foreach ($this->evaluators as $evaluator) {
                    $otp = mt_rand(111111, 999999);
                    $this->otps[$evaluator['id']] = $otp;

                    SmsService::make('637490e47c248750be1db026')
                        ->to($evaluator['phone'])
                        ->addVariables([
                            'number' => $otp,
                            'tendernumber' => $this->tender->tender_uin
                        ])
                        ->send();
                }
                logger()->info('Generated OTPs: ', $this->otps);
                
                $this->tender->update([
                    'tender_technical_opening_otps' => json_encode($this->otps)
                ]);
                logger()->info('Updated Tender: ', $this->tender->toArray());
            });
        } catch (\Exception $e) {
            $this->notify('Something went wrong.', 'error');
            logger()->error('Error in sending OTPs: ', ['exception' => $e]);
        }

        $this->resetErrorBag();
        $this->show = true;
    }

    public function verifyOtp()
    {   
        $storedOtps = json_decode($this->tender->tender_technical_opening_otps, true);
        logger()->info('Stored Otps: ', $storedOtps);
        // Check if $storedOtps is not null and is an array
        if (is_null($storedOtps) || !is_array($storedOtps)) {
            $this->notify('Invalid OTP data.', 'error');
            return;
        }

        $this->validate([
            'otps.*' => ['required', 'digits:6', function ($attribute, $value, $fail) use ($storedOtps) {
                $evaluatorId = explode('.', $attribute)[1];

                if (!isset($storedOtps[$evaluatorId]) || $value != $storedOtps[$evaluatorId]) {
                    return $fail('The OTP for evaluator ' . $evaluatorId . ' is invalid.');
                }
            }]
        ]);

        try {
            $this->tender->update([
                'tender_technical_opening_otps' => null,
                'technicalbid_opening_date' => now(),
                'tender_status' => TenderStatus::TECHNICAL->value
            ]);

            $this->show = false;
            $this->emit('refreshBiddings');

            $this->notify('Tender opened for technical bidding.');
            return redirect()->route('tenders.show', ['tender' => $this->tender->id]);
        } catch (\Exception $e) {
            $this->notify('Something went wrong.', 'error');
        }
    }


    public function closeModal() {
        $this->show = false;
        return redirect()->route('tenders.show', ['tender' => $this->tender->id]);
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.tenders.open-technical-document', [
            'evaluators' => $this->evaluators
        ]);
    }
}