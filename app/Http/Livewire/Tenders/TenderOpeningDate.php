<?php

namespace App\Http\Livewire\Tenders;

use Carbon\Carbon;
use App\Models\Tender;
use App\Models\Bidding;
use App\Http\Livewire\Modal;
use App\Notifications\SendFinancialBidOpeningDate;

class TenderOpeningDate extends Modal
{
    public $tenderOpeningDate;
    public $venue;

    public $tenderId;

    public function save()
    {
        $validated = $this->validate([
            'tenderOpeningDate' => ['required'],
            'venue' => ['nullable']
        ]);

        try {
            // TODO: Find all the companies associated with the bidding that are accepted and then send the SMS
            // TODO: use lazy()
            $biddings = Bidding::with(['user', 'tender'])->accepted()->get();
            $biddings->each(function ($bidding) use ($validated) {
                $bidding->user->notify(
                    new SendFinancialBidOpeningDate(
                        $this->tender->tender_uin, 
                        Carbon::parse($validated['tenderOpeningDate'])->toFormattedDateString()
                    )
                );
            });
    
            $this->tender->update([
                'tender_opening_date' => Carbon::parse($validated['tenderOpeningDate']),
                'tender_opening_venue' => $validated['venue'],
            ]);

            $this->closeModal();
    
            $this->reset(['tenderOpeningDate', 'venue']);
    
            $this->emit('refreshBiddings');
    
            $this->notify('Tender opening date added.');
        } catch (\Exception $e) {
            dd($e);
            $this->notify('Something went wrong. Try again.', 'error');
        }
    }

    public function getTenderProperty()
    {
        return Tender::query()
            ->with('biddings')
            ->findOrFail($this->tenderId);
    }

    public function render()
    {
        return view('livewire.tenders.tender-opening-date');
    }
}
