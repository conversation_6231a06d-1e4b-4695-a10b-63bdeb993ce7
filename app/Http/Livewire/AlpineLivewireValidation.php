<?php

namespace App\Http\Livewire;

use Livewire\Component;

class AlpineLivewireValidation extends Component
{
    public $name;
    public $email;
    public $phone;

    public $accounts = [];


    public function mount()
    {
        $this->accounts = [['name' => '', 'url' => '']];
    }

    public function save()
    {
        // try {
        //     $validated = $this->validate([
        //         'name' => ['required'],
        //         'email' => ['required'],
        //         'phone' => ['required'],
                
        //         'accounts' => 'required|array|min:1',
        //         'accounts.*.name' => 'required|string|distinct',
        //         'accounts.*.url' => 'required|url|distinct',
        //     ], [
        //         'accounts.*.name.required' => 'Name is required',
        //         'accounts.*.url.required' => 'URL is required',
        //     ]);
        // } catch (\Illuminate\Validation\ValidationException $e) {
        //     $this->dispatchBrowserEvent('validation-errors', $e->errors());
        //     $validated = $this->validate([
        //         'name' => ['required'],
        //         'email' => ['required'],
        //         'phone' => ['required'],

        //         'accounts' => 'required|array|min:1',
        //         'accounts.*.name' => 'required|string|distinct',
        //         'accounts.*.url' => 'required|url|distinct',
        //     ], [
        //         'accounts.*.name.required' => 'Name is required',
        //         'accounts.*.url.required' => 'URL is required',
        //     ]);
        // }
        
        $validated = $this->validate([
            'name' => ['required'],
            'email' => ['required'],
            'phone' => ['required'],

            'accounts' => 'required|array|min:1',
            'accounts.*.name' => 'required|string|distinct',
            'accounts.*.url' => 'required|url|distinct',
        ], [
            'accounts.*.name.required' => 'Name is required',
            'accounts.*.url.required' => 'URL is required',
        ]);
        
        dd($validated);
    }

    public function render()
    {
        return view('livewire.alpine-livewire-validation');
    }
}
