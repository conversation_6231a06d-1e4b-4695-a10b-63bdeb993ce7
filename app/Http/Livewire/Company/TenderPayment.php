<?php

namespace App\Http\Livewire\Company;

use App\Models\Tender;
use Livewire\Component;
use App\Models\DocumentPayment;

class TenderPayment extends Component
{
    public $openSlideover = false;
    public $tenderId;

    public $documentPaymentRazorpayPaymentId;
    public $documentPaymentAmount;
    public $documentPaymentStatus;
    public $documentPaymentMode;
    public $documentPaymentAt;
    public $tenderTitle;
    public $tenderNumber;

    public $hasFinishedTenderDocumentPayment = false;

    protected $listeners = [
        'openTenderPaymentSlideover'
    ];

    public function openTenderPaymentSlideover($tenderId = null)
    {
        $documentPayment = DocumentPayment::query()
            ->with('tender:id,tender_title,tender_number')
            ->where('user_id', $this->user->id)
            ->where('tender_id', $tenderId)
            ->latest('created_at')
            ->first();

        if ($documentPayment) {
            $this->documentPaymentRazorpayPaymentId = $documentPayment->razorpay_payment_id;
            $this->documentPaymentAmount = $documentPayment->amount / 100;
            $this->documentPaymentStatus = $documentPayment->status?->statusLabel();
            $this->documentPaymentMode = $documentPayment->payment_mode;
            $this->documentPaymentAt = $documentPayment->payment_at?->format('d/m/Y h:i:s');
            $this->tenderTitle = $documentPayment?->tender?->tender_title;
            $this->tenderNumber = $documentPayment?->tender?->tender_number;

            $this->hasFinishedTenderDocumentPayment = true;
        }

        $this->openSlideover = true;
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.company.tender-payment');
    }
}
