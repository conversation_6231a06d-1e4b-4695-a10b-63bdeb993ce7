<?php

namespace App\Http\Livewire\Company;

use App\Models\Tender;
use Livewire\Component;

class BidDetails extends Component
{
    public $openSlideover = false;
    public $tenderId;
    public $timelines = [];

    protected $listeners = [
        'openBidDetailsSlideover'
    ];

    public function openBidDetailsSlideover($tenderId = null)
    {
        $tender = Tender::query()
            ->with(['companyBidding', 'companyDocumentPayment', 'companyEmdPayment', 'result.bidding'])
            ->findOrFail($tenderId);

        $this->timelines = [
            'documentPaymentDate' => $tender->companyDocumentPayment?->payment_at?->format('d/m/Y'),
            'documentPaymentMode' => $tender->companyDocumentPayment?->payment_mode,
            'documentPaymentStatus' => $tender->companyDocumentPayment?->status?->statusLabel() == 'Paid' ? 'Purchased' : 'Not yet',
            'preBidMeetingVenue' => $tender->pre_bid_meeting_venue,
            'preBidMeetingDate' => $tender->pre_bid_meeting_date?->format('d/m/Y'),
            'preBidMeetingMinutes' => $tender->pre_bid_minutes_document_path,
            'emdPaymentStatus' => $tender->companyEmdPayment?->status?->statusLabel() ?? $tender->companyEmdPayment?->status,
            'emdPaymentDate' => $tender->companyEmdPayment?->payment_at?->format('d/m/Y'),
            'bidSubmissionDate' => $tender->companyBidding?->created_at?->format('d/m/Y'),
            'technicalBidDate' => $tender->companyBidding?->updated_at?->format('d/m/Y'),
            'technicalBidStatus' => $tender->companyBidding?->bidding_status,
            'financialBidDate' => $tender->result?->created_at?->format('d/m/Y'),
            'financialBidStatus' => $tender->result?->company_status,
            'aocDocument' => $tender->result?->aoc_document_url,
            'aocCompanyName' => $tender->result?->bidding?->company_name,
        ];

        $this->openSlideover = true;
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.company.bid-details');
    }
}
