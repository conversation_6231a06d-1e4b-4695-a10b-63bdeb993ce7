<?php

namespace App\Http\Livewire\Company;

use Livewire\Component;
use App\Models\EmdPayment;

class EmdPaymentDetails extends Component
{
    public $openSlideover = false;
    public $tenderId;

    public $emdPaymentRazorpayPaymentId;
    public $emdPaymentRefNo;
    public $emdPaymentAmount;
    public $emdPaymentStatus;
    public $emdPaymentMode;
    public $emdPaymentAt;
    public $tenderTitle;
    public $tenderNumber;

    public $hasFinishedTenderEmdPayment = false;

    protected $listeners = [
        'openTenderEmdPaymentSlideover'
    ];

    public function openTenderEmdPaymentSlideover($tenderId = null)
    {
        $emdPayment = EmdPayment::query()
            ->with('tender:id,tender_title,tender_number')
            ->where('user_id', $this->user->id)
            ->where('tender_id', $tenderId)
            ->latest('created_at')
            ->first();

        if ($emdPayment) {
            $this->emdPaymentRazorpayPaymentId = $emdPayment->razorpay_payment_id;
            $this->emdPaymentRefNo = $emdPayment->payment_ref_no;
            $this->emdPaymentAmount = $emdPayment->amount;
            $this->emdPaymentStatus = $emdPayment->status?->statusLabel() ?? $emdPayment->status;
            $this->emdPaymentMode = $emdPayment->payment_type;
            $this->emdPaymentAt = $emdPayment->created_at->toFormattedDateString();

            $this->tenderTitle = $emdPayment?->tender?->tender_title;
            $this->tenderNumber = $emdPayment?->tender?->tender_number;

            $this->hasFinishedTenderEmdPayment = true;
        }

        $this->openSlideover = true;
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.company.emd-payment-details');
    }
}
