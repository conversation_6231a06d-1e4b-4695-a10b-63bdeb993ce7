<?php

namespace App\Http\Livewire\Company;

use App\Enums\CompanyBusinessType;
use App\Models\CompanyProfile;
use Illuminate\Validation\Rules\Enum;
use Livewire\Component;

class ProfileUpdate extends Component
{
    public $gstNumber;
    public $registrationNumber;
    public $businessType = 'company';
    public $address;

    public function mount()
    {
        $this->getCompanyDetail();
    }

    public function updateCompanyProfile()
    {
        $validated = $this->validate([
            'gstNumber' => ['required'],
            'registrationNumber' => ['required'],
            'businessType' => ['required', new Enum(CompanyBusinessType::class)],
            'address' => ['required'],
        ]);

        CompanyProfile::updateOrCreate(
            [
                'user_id' => $this->user->id,
            ],
            [
                'user_id' => $this->user->id,
                'gst_number' => $validated['gstNumber'],
                'nchac_registration_no' => $validated['registrationNumber'],
                'type' => $validated['businessType'],
                'address' => $validated['address'],
            ]
        );

        $this->getCompanyDetail();

        $this->emit('saved');

        $this->notify('Company profile saved.');
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getCompanyDetail()
    {
        if ($this->user->companyProfile) {
            $this->gstNumber = data_get($this->user->companyProfile, 'gst_number');
            $this->registrationNumber = data_get($this->user->companyProfile, 'nchac_registration_no');
            $this->businessType = data_get($this->user->companyProfile, 'type');
            $this->address = data_get($this->user->companyProfile, 'address');
        }
    }

    public function getBusinessTypesProperty()
    {
        return collect(CompanyBusinessType::cases())->map(fn($item) => [
            'label' => $item->name,
            'value' => $item->value,
        ]);
    }

    public function render()
    {
        return view('livewire.company.profile-update');
    }
}
