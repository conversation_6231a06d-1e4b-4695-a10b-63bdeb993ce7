<?php

namespace App\Http\Livewire\Company;

use App\Models\Bidding;
use Livewire\Component;

class TenderBidding extends Component
{
    public $tenderTitle;
    public $tenderNumber;
    public $openSlideover = false;
    public $biddingPrices = [];

    protected $listeners = [
        'openTenderBiddingSlideover'
    ];

    public function openTenderBiddingSlideover(Bidding $bidding)
    {
        $bidding->loadMissing(['tender:id,tender_title,tender_number', 'pricings.tenderitem']);
     
        $this->tenderTitle = $bidding?->tender?->tender_title;
        $this->tenderNumber = $bidding?->tender?->tender_number;

        $this->biddingPrices = $bidding?->pricings->map(fn($item) => [
            'price' => $item->bidding_price,
            'item_name' => data_get($item, 'tenderitem.item_type'),
            'item_estimated_price' => data_get($item, 'tenderitem.estimated_price'),
            'quantity' => data_get($item, 'tenderitem.quantity'),
            'unit' => data_get($item, 'tenderitem.unit')
        ]);
 
        $this->openSlideover = true;
    }

    public function render()
    {
        return view('livewire.company.tender-bidding');
    }
}
