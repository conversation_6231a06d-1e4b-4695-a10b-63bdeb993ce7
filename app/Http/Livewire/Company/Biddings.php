<?php

namespace App\Http\Livewire\Company;

use App\Models\Bidding;
use Livewire\Component;
use Livewire\WithPagination;

class Biddings extends Component
{
    use WithPagination;

    public $search;
   
    protected $queryString = [
        'search' => ['except' => '']
    ];

    public function resetFilter()
    {
        $this->reset(['search']);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.company.biddings', [
            'biddings' => Bidding::query()
                ->with(['tender', 'tender.user:id,name,email', 'tender.department:id,name'])
                ->where('user_id', $this->user->id)
                ->when($this->search != '', fn ($query) => $query->whereLike(['tender.tender_title', 'tender.tender_number'], $this->search))
                ->latest('id')
                ->fastPaginate(10)
        ]);
    }
}
