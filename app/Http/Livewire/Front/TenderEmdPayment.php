<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use Livewire\Component;
use App\Models\EmdPayment;
use Illuminate\Support\Str;
use Livewire\WithFileUploads;
use App\Enums\EmdPaymentTypes;
use App\Enums\RazorpayPaymentStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Notifications\EmdAmountReceived;
use App\Traits\WithUniqueRandomNumberGenerator;

class TenderEmdPayment extends Component
{
    use WithFileUploads;
    use WithUniqueRandomNumberGenerator;

    public $tenderId;
    public $tenderFee;
    public $tenderNumber;
    public $tenderUin;

    public $payeeName;
    public $bankName;
    public $issueDate;
    public $ddPhoto;
    public $ddNumber;
    public $amount;

    protected $listeners = [
        'refreshTenderEmdPayment' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        // Check if this is a selective tender and if the current company has access
        if ($tender->isSelectiveTender()) {
            // Check if the company has access to this tender
            if (!$tender->companyCanAccessTender(auth()->user())) {
                session()->flash('error', 'You do not have access to this selective tender');
                return redirect()->route('front.tenders');
            }
        }

        $this->tenderId = $tender->id;
        $this->tenderFee = $tender->tender_cost;
        $this->tenderNumber = $tender->tender_number;
        $this->tenderUin = $tender->tender_uin;
    }

    public function save()
    {
        $validated = $this->validate([
            'payeeName' => ['required', 'string'],
            'bankName' => ['required', 'string'],
            'issueDate' => ['required'],
            'ddPhoto' => ['required', 'image', 'max:2024'],
            'ddNumber' => ['required'],
            'amount' => ['required'],
        ]);

        try {
            return DB::transaction(function () use ($validated) {
                $emdPayment = EmdPayment::create([
                    'user_id' => $this->user->id,
                    'tender_id' => $this->tenderId,
                    'payment_name' => $validated['payeeName'],
                    'payment_bank' => $validated['bankName'],
                    'payment_type' => EmdPaymentTypes::DEMAND_DRAFT,
                    'payment_ref_no' => $this->generateUniqueRandomNumber('emd', ''),
                    'challan_or_dd_no' => $validated['ddNumber'],
                    'photo' => $validated['ddPhoto']->store('/', 'tender-emd'),
                    'amount' => $validated['amount'],
                    'status' => RazorpayPaymentStatus::PAID->value,
                    'payment_at' => $validated['issueDate']
                ]);

                $this->user->notify(
                    new EmdAmountReceived(
                        $this->tenderUin,
                        $this->user->name,
                        $emdPayment->created_at->toFormattedDateString()
                    )
                );

                return redirect()->route('front.tenders.show', $this->tenderId);
            });
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            $this->notify('Something went wrong. Try again.', 'error');
        }
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getEmdPaymentProperty()
    {
        // First check if there's a current payment session
        $sessionKey = 'emd_payment_session_' . $this->tenderId;
        $currentPaymentId = session($sessionKey);

        if ($currentPaymentId) {
            return EmdPayment::query()
                ->where('id', $currentPaymentId)
                ->where('user_id', $this->user->id)
                ->where('tender_id', $this->tenderId)
                ->first();
        }

        // If no current session, check for any successful or failed payments
        // This allows showing completed payment status after session is cleared
        return EmdPayment::query()
            ->where('user_id', $this->user->id)
            ->where('tender_id', $this->tenderId)
            ->whereIn('status', ['authorized', 'captured', 'paid', 'failed'])
            ->latest('created_at')
            ->first();
    }

    public function render()
    {
        return view('livewire.front.tender-emd-payment', [
            'emdPayment' => $this->emdPayment
        ])->layout('layouts.front', [
            'title' => 'Tender EMD Payment'
        ]);
    }
}
