<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class Tenders extends Component
{
    use WithPagination;

    public $search = '';
    public $filter = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'filter' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Tender::query()
            ->with(['department:id,name'])
            ->tenderIsApprovedOrArchived();

        // For guests or non-company users, show only open tenders (or those without classification for backward compatibility)
        if (!auth()->check() || auth()->user()->role !== 'company') {
            $query->where(function($q) {
                $q->where('tender_classification', \App\Enums\TenderClassification::OPEN->value)
                  ->orWhereNull('tender_classification'); // Handle older records without classification
            });
        } 
        // For logged-in company users, show open tenders and selective tenders they have access to
        else {
            $user = auth()->user();
            $companyProfileId = optional($user->companyProfile)->id;
            
            $query->where(function($q) use ($companyProfileId) {
                // Include all open tenders and tenders without classification (backward compatibility)
                $q->where('tender_classification', \App\Enums\TenderClassification::OPEN->value)
                  ->orWhereNull('tender_classification');
                
                // Include selective tenders only if the company has access
                if ($companyProfileId) {
                    $q->orWhere(function($subQ) use ($companyProfileId) {
                        $subQ->where('tender_classification', \App\Enums\TenderClassification::SELECTIVE->value)
                             ->whereHas('companies', function($companiesQuery) use ($companyProfileId) {
                                 $companiesQuery->where('company_profile_id', $companyProfileId);
                             });
                    });
                }
            });
        }

        if ($this->search) {
            $query->where('tender_title', 'like', '%' . $this->search . '%');
        }

        if ($this->filter) {
            switch ($this->filter) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'last-24':
                    $query->where('created_at', '>=', Carbon::now()->subDay());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', Carbon::yesterday());
                    break;
                case 'this-week':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'last-7':
                    $query->where('created_at', '>=', Carbon::now()->subDays(7));
                    break;
                case 'this-month':
                    $query->whereBetween('created_at', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()]);
                    break;
                case 'last-30':
                    $query->where('created_at', '>=', Carbon::now()->subDays(30));
                    break;
                case 'last-90':
                    $query->where('created_at', '>=', Carbon::now()->subDays(90));
                    break;
                default:
                    break;
            }
        }

        return view('livewire.front.tenders', [
            'tenders' => $query->latest('id')->simplePaginate(10)->onEachSide(2),
        ])->layout('layouts.front', [
            'title' => 'Latest Tenders'
        ]);
    }
}