<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use App\Models\Bidding;
use Livewire\Component;
use App\Models\EmdPayment;
use Livewire\WithFileUploads;
use App\Models\DocumentPayment;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use App\Traits\InteractsWithBanner;
use Illuminate\Support\Facades\Log;
use App\Rules\CheckValidPhoneNumber;
use App\Notifications\SuccessfullBidSubmission;

class TenderApply extends Component
{
    use WithFileUploads;
    use InteractsWithBanner;

    public $tenderId;
    public $tenderNumber;
    public $tenderUin;
    public $tenderFee;

    public $bidprices = [];
    public $tenderdocuments = [];
    public $name;
    public $email;
    public $phone;

    protected $listeners = [
        'refreshTender' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        // Check if this is a selective tender and if the current company has access
        if ($tender->isSelectiveTender()) {
            // Check if the company has access to this tender
            if (!$tender->companyCanAccessTender(auth()->user())) {
                session()->flash('error', 'You do not have access to this selective tender');
                return redirect()->route('front.tenders');
            }
        }

        $tender->loadMissing('tenderitems');

        $this->tenderId = $tender->id;
        $this->tenderNumber = $tender->tender_number;
        $this->tenderUin = $tender->tender_uin;
        $this->tenderFee = $tender->tender_cost;

        // Get the authenticated user directly instead of using property accessor
        $user = auth()->user();

        $this->name = $user->name;
        $this->email = $user->email;
        $this->phone = $user->phone;

        $this->fill([
            'tenderdocuments' => collect($this->tender?->document_types)->map(fn ($item) => [
                'id' => $item['id'],
                'size' => $item['size'],
                'type' => $item['type'],
                'documentName' => $item['documentName'],
                'document' => null,
            ])->all(),
            'bidprices' => $this->tender?->tenderitems?->map(fn ($item) => [
                'id' => $item->id,
                'item_type' => $item->item_type,
                'quantity' => $item->quantity,
                'unit' => $item->unit,
                'estimated_price' => $item->estimated_price,
                'price' => 0
            ])->all(),
        ]);
    }

    public function save()
    {
        Log::info('Tender application save method called', [
            'tender_id' => $this->tenderId,
            'user_id' => $this->user->id ?? 'null',
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'bidprices_count' => count($this->bidprices ?? []),
            'tenderdocuments_count' => count($this->tenderdocuments ?? [])
        ]);

        // Check if user has paid tender fee
        if (!$this->hasPaidTenderFee()) {
            Log::error('User has not paid tender fee', ['user_id' => $this->user->id, 'tender_id' => $this->tenderId]);
            $this->notify('You must pay the tender fee before applying.', 'error');
            return;
        }

        // Check if user has already applied for this tender
        $existingBidding = Bidding::where('tender_id', $this->tenderId)
            ->where('user_id', $this->user->id)
            ->first();

        if ($existingBidding) {
            Log::error('User has already applied for this tender', ['user_id' => $this->user->id, 'tender_id' => $this->tenderId]);
            $this->notify('You have already applied for this tender.', 'error');
            return;
        }

        Log::info('Validation data check', [
            'inItems' => $this->inItems(),
            'inQuantities' => $this->inQuantities(),
            'inDocumentTypes' => $this->inDocumentTypes(),
            'tender_has_items' => $this->tender->tenderitems->isNotEmpty(),
            'tender_has_document_types' => !is_null($this->tender->document_types)
        ]);

        // Additional validation checks
        if (empty($this->inItems())) {
            Log::error('No tender items found for validation');
            $this->notify('This tender has no items configured. Please contact the administrator.', 'error');
            return;
        }

        if (empty($this->inDocumentTypes())) {
            Log::error('No document types found for validation');
            $this->notify('This tender has no document types configured. Please contact the administrator.', 'error');
            return;
        }

        $validated = $this->validate([
            'name' => ['required', 'string'],
            'email' => ['required', 'string', 'email'],
            'phone' => ['required', 'max:10', 'min:10', new CheckValidPhoneNumber],
            'bidprices' => ['required', 'array'],
            'bidprices.*.id' => ['required', Rule::in($this->inItems())],
            'bidprices.*.quantity' => ['required', Rule::in($this->inQuantities())],
            'bidprices.*.price' => ['required'],
            'tenderdocuments' => ['required', 'array'],
            'tenderdocuments.*.documentName' => ['required', Rule::in($this->inDocumentTypes())],
            'tenderdocuments.*.document' => ['required', 'mimes:pdf'],
        ], [], [
            'bidprices.*.value' => 'bidprice',
            'tenderdocuments.*.document' => 'tender document'
        ]);

        Log::info('Validation passed successfully', ['validated_data_keys' => array_keys($validated)]);

        try {
            return DB::transaction(function () use ($validated) {
                Log::info('Starting database transaction for tender application');

                $bidding = Bidding::create([
                    'tender_id' => $this->tenderId,
                    'user_id' => $this->user->id,
                    'company_name' => $validated['name'],
                    'company_email' => $validated['email'],
                    'company_phone' => $validated['phone']
                ]);

                Log::info('Bidding created successfully', ['bidding_id' => $bidding->id]);

                $emdPayment = EmdPayment::query()
                    ->where('tender_id', $this->tenderId)
                    ->where('user_id', $this->user->id)
                    ->first();

                if ($emdPayment) {
                    $emdPayment->update(['bidding_id' => $bidding->id]);
                }

                Log::info('Creating bidding prices', ['prices_count' => count($validated['bidprices'])]);

                $pricingData = collect($validated['bidprices'])->map(function ($item) {
                    $cleanPrice = str_replace(',', '', $item['price']);
                    $rate = floatval($cleanPrice);
                    $quantity = intval($item['quantity']);
                    $totalPrice = $rate * $quantity;

                    Log::info('Processing bidding price', [
                        'tenderitem_id' => $item['id'],
                        'original_price' => $item['price'],
                        'clean_price' => $cleanPrice,
                        'rate' => $rate,
                        'quantity' => $quantity,
                        'total_price' => $totalPrice
                    ]);

                    if ($rate <= 0) {
                        throw new \Exception('Invalid price for tender item: ' . $item['id']);
                    }

                    return [
                        'tenderitem_id' => $item['id'],
                        'bidding_price' => $totalPrice,
                        'quantity' => $quantity,
                        'rate' => $rate,
                    ];
                })->all();

                $bidding->pricings()->createMany($pricingData);
                Log::info('All bidding prices created successfully');

                Log::info('Creating bidding documents', ['documents_count' => count($validated['tenderdocuments'])]);

                $documentData = collect($validated['tenderdocuments'])->map(function ($item) {
                    Log::info('Processing document', [
                        'document_name' => $item['documentName'],
                        'file_original_name' => $item['document']->getClientOriginalName(),
                        'file_size' => $item['document']->getSize(),
                        'file_mime_type' => $item['document']->getMimeType()
                    ]);

                    try {
                        // Check if file is valid
                        if (!$item['document']->isValid()) {
                            throw new \Exception('Invalid file upload for document: ' . $item['documentName']);
                        }

                        // Store the file
                        $path = $item['document']->store('/', 'applied-documents');

                        if (!$path) {
                            throw new \Exception('Failed to store file for document: ' . $item['documentName']);
                        }

                        Log::info('Document stored successfully', ['path' => $path]);

                        return [
                            'document_type' => $item['documentName'],
                            'document_path' => $path,
                        ];
                    } catch (\Exception $e) {
                        Log::error('Error storing document', [
                            'document_name' => $item['documentName'],
                            'error' => $e->getMessage()
                        ]);
                        throw new \Exception('Failed to upload document "' . $item['documentName'] . '": ' . $e->getMessage());
                    }
                })->all();

                $bidding->biddingDocuments()->createMany($documentData);
                Log::info('All bidding documents created successfully');

                $this->banner('Tender applied successfully.');

                $this->user->notify(new SuccessfullBidSubmission($this->tenderUin));

                return redirect()->route('front.tenders.show', $this->tenderId);
            });
        } catch (\Exception $e) {
            Log::error('Tender application error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            Log::error('Request data: ', [
                'tender_id' => $this->tenderId,
                'user_id' => $this->user->id,
                'bidprices' => $this->bidprices,
                'tenderdocuments' => array_map(function($doc) {
                    return [
                        'documentName' => $doc['documentName'] ?? 'unknown',
                        'hasDocument' => isset($doc['document']) && $doc['document'] !== null
                    ];
                }, $this->tenderdocuments ?? [])
            ]);

            // Show user-friendly error message
            $this->notify('Something went wrong while submitting your application. Please try again or contact support if the problem persists.', 'error');
        }
    }

    protected function inItems()
    {
        if ($this->tender->tenderitems->isNotEmpty()) {
            return $this->tender->tenderitems->pluck('id')->all();
        } else {
            return [];
        }
    }

    protected function inQuantities()
    {
        if ($this->tender->tenderitems->isNotEmpty()) {
            return $this->tender->tenderitems->pluck('quantity')->all();
        } else {
            return [];
        }
    }

    protected function inDocumentTypes()
    {
        // if (count($this->tender->document_types)) {
        if (! is_null($this->tender->document_types)) {
            return collect($this->tender->document_types)->pluck('documentName')->all();
        } else {
            return [];
        }
    }

    public function getTenderProperty()
    {
        return Tender::with('tenderitems')->findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getDocumentPaymentProperty()
    {
        return DocumentPayment::query()
            ->where('user_id', $this->user->id)
            ->where('tender_id', $this->tenderId)
            ->latest('created_at')
            ->first();
    }

    public function hasPaidTenderFee()
    {
        return $this->documentPayment
            && ! is_null($this->documentPayment->status)
            && (
                $this->documentPayment->status->statusLabel() === 'Paid'
            ) ? true : false;
    }

    public function render()
    {
        return view('livewire.front.tender-apply')
            ->layout('layouts.front', [
                'title' => 'Tender Apply'
            ]);
    }
}
