<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use Livewire\Component;

class TenderDetails extends Component
{
    public $tender;

    public function mount(Tender $tender)
    {
        // if ($tender->last_date_of_submission < now()) {
        //     abort(404);
        // }

        // Check if this is a selective tender and if the current user is a company
        if ($tender->isSelectiveTender() && auth()->check() && auth()->user()->role === 'company') {
            // Check if the company has access to this tender
            if (!$tender->companyCanAccessTender(auth()->user())) {
                session()->flash('error', 'You do not have access to this selective tender');
                return redirect()->route('tenders');
            }
        }

        $tender->loadMissing(['user:id,name,email', 'department:id,name'])
            ->select([
                'id',
                'user_id',
                'department_id',
                'tender_category',
                'tender_type',
                'tender_title',
                'tender_number',
                'tender_uin',
                'tender_address',
                'tender_details',
                'tender_called_by',
                'tender_cost',
                'tender_emd',
                'tender_value',
                'document_types',
                'published_date',
                'last_date_of_submission',
                'last_date_of_document_sale',
                'last_date_for_clarification',
                'pre_bid_meeting_date',
                'pre_bid_meeting_venue',
                'bid_starting_date',
                'technicalbid_opening_date',
                'tender_classification',
            ]);
    
        $this->tender = $tender;
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.front.tender-details')
            ->layout('layouts.front', [
                'title' => 'Tender Details',
                'metaTitle' => $this->tender->tender_title,
                'metaDescription' => $this->tender->tender_title,
            ]);
    }
}
