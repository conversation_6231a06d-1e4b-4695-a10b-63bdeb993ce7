<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use Livewire\Component;
use Illuminate\Support\Str;
use App\Models\DocumentPayment;
use App\Enums\DocumentPaymentStatus;

class TenderDocumentPayment extends Component
{
    public $tenderId;
    public $tenderFee;
    public $tenderNumber;

    protected $listeners = [
        'refreshTenderDocumentpayment' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        // Check if this is a selective tender and if the current company has access
        if ($tender->isSelectiveTender()) {
            // Check if the company has access to this tender
            if (!$tender->companyCanAccessTender(auth()->user())) {
                session()->flash('error', 'You do not have access to this selective tender');
                return redirect()->route('front.tenders');
            }
        }

        $this->tenderId = $tender->id;
        $this->tenderFee = $tender->tender_cost;
        $this->tenderNumber = $tender->tender_number;
    }

    public function payTenderFee()
    {
        DocumentPayment::create([
            'tender_id' => $this->tenderId,
            'user_id' => $this->user->id,
            'payment_ref_no' => Str::generateRandomNumber(),
            'status' => DocumentPaymentStatus::PENDING,
            'amount' => $this->tenderFee
        ]);

        $this->notify('Tender document payment initiated.');

        $this->emit('refreshTenderDocumentpayment');
    }

    public function makeSuccessfulPaymentSimulation()
    {
        $this->documentPayment?->update([
            'status' => DocumentPaymentStatus::SUCCESS
        ]);

        $this->notify('Tender document payment paid.');

        $this->emit('refreshTenderDocumentpayment');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getDocumentPaymentProperty()
    {
        // First check if there's a current payment session
        $sessionKey = 'document_payment_session_' . $this->tenderId;
        $currentPaymentId = session($sessionKey);

        if ($currentPaymentId) {
            return DocumentPayment::query()
                ->where('id', $currentPaymentId)
                ->where('user_id', $this->user->id)
                ->where('tender_id', $this->tenderId)
                ->first();
        }

        // If no current session, check for any successful or failed payments
        // This allows showing completed payment status after session is cleared
        return DocumentPayment::query()
            ->where('user_id', $this->user->id)
            ->where('tender_id', $this->tenderId)
            ->whereIn('status', ['authorized', 'captured', 'paid', 'failed'])
            ->latest('created_at')
            ->first();
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.front.tender-document-payment', [
            'documentPaymentdata' => $this->documentPayment
        ])->layout('layouts.front', [
            'title' => 'Tender Document Payment'
        ]);
    }
}
