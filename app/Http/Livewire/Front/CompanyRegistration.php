<?php

namespace App\Http\Livewire\Front;

use App\Models\User;
use App\Notifications\CompanySuccessfullRegistration;
use Livewire\Component;
use App\Rules\CheckValidPhoneNumber;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class CompanyRegistration extends Component
{
    public $name;
    public $email;
    public $phone;
    public $password;
    public $password_confirmation;

    public function save()
    {
        $validated = $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => ['required', 'min:10', 'max:10', new CheckValidPhoneNumber, 'unique:users,phone'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'password' => Hash::make($validated['password']),
            'role' => User::ROLE_COMPANY
        ]);

        $user->notify(new CompanySuccessfullRegistration());

        $this->reset();

        $this->notify('Congratulations, your account has been successfully created.');

        $this->bannerMessage('Congratulations, your account has been successfully created.');
    }

    public function render()
    {
        return view('livewire.front.company-registration')
            ->layout('layouts.front', [
                'title' => 'Registration'
            ]);
    }
}
