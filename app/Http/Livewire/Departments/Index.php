<?php

namespace App\Http\Livewire\Departments;

use App\Models\User;
use Livewire\Component;
use App\Models\Department;

class Index extends Component
{
    protected $listeners = [
        'refreshDepartments' => '$refresh'
    ];

    public function render()
    {
        return view('livewire.departments.index', [
            'departments' => Department::withCount([
                'users as makerUsersCount' => function ($query) {
                    return $query->where('role', User::ROLE_MAKER);
                },
                'users as checkerUsersCount' => function ($query) {
                    return $query->where('role', User::ROLE_CHECKER);
                }
            ])->get()
        ]);
    }
}
