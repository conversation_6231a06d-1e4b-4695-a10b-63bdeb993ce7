<?php

namespace App\Http\Livewire\Departments;

use App\Models\Department;
use Livewire\Component;
use Livewire\WithFileUploads;

class Edit extends Component
{
    use WithFileUploads;

    public $departmentId;

    public $name;
    public $photo;
    public $departmentImage;

    public function mount(Department $department)
    {
        $this->departmentId = $department->id;
        $this->name = $department->name;
        $this->departmentImage = $department->photo_url;
    }

    public function update()
    {
        $validated = $this->validate([
            'name' => ['required', 'string', 'unique:departments']
        ]);

        $this->department->update([
            'name' => $validated['name']
        ]);

        $this->notify('Department photo updated.');
    }

    public function updateImage()
    {
        $validated = $this->validate([
            'photo' => ['required', 'image', 'max:2024']
        ]);
       
        $this->department->fill([
            'photo' => $validated['photo']->store('/', 'department-photos'),
        ])->save();

        $this->departmentImage = $this->department->photo_url;
      
        $this->notify('Department photo updated.');
    }

    public function deleteImage()
    {
        // TODO: delete from storage/spaces
        $this->department->fill([
            'photo' => null
        ])->save();

        $this->departmentImage = null;

        $this->notify('Department photo deleted.');
    }

    public function getDepartmentProperty()
    {
        return Department::findOrFail($this->departmentId);
    }

    public function render()
    {
        return view('livewire.departments.edit');
    }
}
