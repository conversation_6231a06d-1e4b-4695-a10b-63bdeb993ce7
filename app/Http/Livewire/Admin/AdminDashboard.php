<?php

namespace App\Http\Livewire\Admin;

use App\Models\Tender;
use Livewire\Component;
use App\Models\Department;

class AdminDashboard extends Component
{
    protected $listeners = [
        'refreshDepartments' => '$refresh'
    ];

    public function render()
    {
        return view('livewire.admin.dashboard', [
            'departments' => Department::withCount('tenders')->get()
        ]);
    }
}
