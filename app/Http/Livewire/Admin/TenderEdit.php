<?php

namespace App\Http\Livewire\Admin;

use App\Models\Tender;
use Livewire\Component;
use App\Enums\TenderType;
use App\Enums\TenderCategory;
use App\Enums\TenderStatus;

class TenderEdit extends Component
{
    public $tenderId;
    public $tenderStatus;
    public $tenderUpdatedAt;
    public $tender_category;
    public $tender_type;
    public $tender_title;
    public $tender_number;
    public $tender_address;
    public $tender_details;
    public $tender_called_by;
    public $tender_cost = 0;
    public $tender_emd;
    public $tender_value = 0;
    public $published_date;
    public $bid_starting_date;
    public $technicalbid_opening_date;
    public $last_date_of_submission;
    public $last_date_of_document_sale;
    public $last_date_for_clarification;
    public $pre_bid_meeting_date;
    public $pre_bid_meeting_venue;

    public $hasTenderDocumentTypes = false;
    public $hasDocuments = false;
    public $hasTenderItems = false;

    protected $listeners = [
        'refreshTenders'
    ];

    public function mount($tender)
    {
        $user = auth()->user();
        
        // For admin users, don't filter by department
        $query = Tender::query()->withExists(['documents', 'tenderitems']);
        
        // Only apply department filter for non-admin users
        if (!$user->isAdministrator() && !$user->isSuperAdministrator()) {
            $query->where('department_id', $user->department_id);
        }
        
        $tender = $query->findOrFail($tender);
        $this->hasTenderDocumentTypes = $tender->hasTenderDocumentTypes();
        $this->hasDocuments = $tender->documents_exists;
        $this->hasTenderItems = $tender->tenderitems_exists;
        $this->tenderStatus = $tender->tender_status;

        $this->tenderId = $tender->id;
        $this->tenderUpdatedAt = $tender->updated_at;
        $this->tender_category = $tender->tender_category;
        $this->tender_type = $tender->tender_type;
        $this->tender_title = $tender->tender_title;
        $this->tender_number = $tender->tender_number;
        $this->tender_address = $tender->tender_address;
        $this->tender_details = $tender->tender_details;
        $this->tender_called_by = $tender->tender_called_by;
        $this->tender_cost = $tender->tender_cost;
        $this->tender_emd = $tender->tender_emd;
        $this->tender_value = $tender->tender_value;
        $this->published_date = $tender->published_date;
        $this->bid_starting_date = $tender->bid_starting_date;
        $this->technicalbid_opening_date = $tender->technicalbid_opening_date;
        $this->last_date_of_submission = $tender->last_date_of_submission;
        $this->last_date_of_document_sale = $tender->last_date_of_document_sale;
        $this->last_date_for_clarification = $tender->last_date_for_clarification;
        $this->pre_bid_meeting_date = $tender->pre_bid_meeting_date;
        $this->pre_bid_meeting_venue = $tender->pre_bid_meeting_venue;
    }

    public function save()
    {
        $validated = $this->validate([
            'tender_category' => ['required'],
            'tender_type' => ['required'],
            'tender_title' => ['required'],
            'tender_number' => ['required'],
            'tender_address' => ['required'],
            'tender_details' => ['required'],
            'tender_called_by' => ['required'],
            'tender_cost' => ['required', 'regex:/^(?:\d+|\d{1,2},(?:\d{2},)*\d{3})(?:\.\d{2})?$/'],
            'tender_value' => ['required', 'regex:/^(?:\d+|\d{1,2},(?:\d{2},)*\d{3})(?:\.\d{2})?$/'],
            'tender_emd' => ['required'],

            'published_date' => ['required'],
            'bid_starting_date' => ['required'],
            'technicalbid_opening_date' => ['required'],

            'last_date_of_submission' => ['required'],
            'last_date_of_document_sale' => ['required'],
            'last_date_for_clarification' => ['required'],

            'pre_bid_meeting_date' => ['nullable'],
            'pre_bid_meeting_venue' => ['nullable'],
           // 'pre_bid_meeting_minutes' => ['nullable', 'mimes:pdf'],
        ]);
 
        $validated['tender_cost'] = str_replace(',', '', $validated['tender_cost']);
        $validated['tender_value'] = str_replace(',', '', $validated['tender_value']);

        $this->tender->fill($validated)->save();

        $this->emit('saved');
        $this->notify('Tender details updated.');
    }

    public function getShowTimelineProperty()
    {
        return $this->hasTenderDocumentTypes == false
            || $this->hasDocuments == false
            || $this->hasTenderItems == false;
    }

    public function tenderIsPublished()
    {
        return $this->tenderStatus === TenderStatus::PENDING->value;
    }

    public function refreshTenders()
    {
        $tender = $this->tender->loadExists(['documents', 'tenderitems']);

        $this->hasTenderDocumentTypes = $tender->hasTenderDocumentTypes();
        $this->hasDocuments = $tender->documents_exists;
        $this->hasTenderItems = $tender->tenderitems_exists;
        $this->tenderStatus = $tender->tender_status;
    }

    public function getTenderCategoriesProperty()
    {
        return TenderCategory::cases();
    }

    public function getTenderTypesProperty()
    {
        return TenderType::cases();
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.admin.tender-edit');
    }
}
