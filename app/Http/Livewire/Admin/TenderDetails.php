<?php

namespace App\Http\Livewire\Admin;

use App\Models\Tender;
use Livewire\Component;


class TenderDetails extends Component
{
    public $tender;
    
    protected $listeners = [
        'refreshTenders' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        $tender->loadExists(['documents', 'tenderitems']);
        $tender->load('result.images');
 
        $this->tender = $tender;
    }
    
    public function viewProgressImages()
    {
        logger()->info('ViewProgressImages clicked on tender: ' . $this->tender->id);
        
        // Try both event methods for compatibility
        $this->emit('openTenderProgressImagesModal', $this->tender->id);
        $this->dispatchBrowserEvent('open-progress-images', ['tenderId' => $this->tender->id]);
    }

    public function render()
    {
        return view('livewire.admin.tender-details');
    }
}
