<?php

namespace App\Http\Livewire\Admin;

use App\Models\Tender;
use Livewire\Component;

class TenderEdit extends Component
{
    public $tender;

    public function mount(Tender $tender)
    {
        // if ($tender->last_date_of_submission < now()) {
        //     abort(404);
        // }

        $tender->loadMissing(['user:id,name,email', 'department:id,name'])
            ->select([
                'id',
                'user_id',
                'department_id',
                'tender_category',
                'tender_type',
                'tender_title',
                'tender_number',
                'tender_uin',
                'tender_address',
                'tender_details',
                'tender_called_by',
                'tender_cost',
                'tender_emd',
                'tender_value',
                'document_types',
                'published_date',
                'last_date_of_submission',
                'last_date_of_document_sale',
                'last_date_for_clarification',
                'pre_bid_meeting_date',
                'pre_bid_meeting_venue',
                'bid_starting_date',
                'technicalbid_opening_date',
            ]);
        

    
        $this->tender = $tender;
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function saveChanges()
    {
        $this->validate([
            'tender.tender_title' => 'required|string|max:255',
            'tender.tender_number' => 'required|string|max:255',
            'tender.tender_cost' => 'required|numeric',
            'tender.tender_emd' => 'required|numeric',
            'tender.tender_value' => 'required|numeric',
            'tender.tender_category' => 'required|string|max:255',
            'tender.tender_type' => 'required|string|max:255',
            'tender.tender_uin' => 'required|string|max:255',
            'tender.tender_address' => 'required|string|max:255',
            'tender.tender_details' => 'required|string|max:255',
            'tender.tender_called_by' => 'required|string|max:255',
            'tender.document_types' => 'required|string|max:255',
            'tender.published_date' => 'required|date',
            'tender.last_date_of_submission' => 'required|date',
            'tender.last_date_of_document_sale' => 'required|date',
            'tender.last_date_for_clarification' => 'required|date',
            'tender.pre_bid_meeting_date' => 'required|date',
            'tender.pre_bid_meeting_venue' => 'required|string|max:255',
            'tender.bid_starting_date' => 'required|date',
            'tender.technicalbid_opening_date' => 'required|date'
            
        ]);

        $this->tender->save();

        session()->flash('message', 'Tender details updated successfully.');
        return redirect()->route('admin.tenders'); 
    }

    public function render()
    {
        return view('livewire.admin.tender-edit')
            ->layout('layouts.front', [
                'title' => 'Tender Details',
                'tender_title' => $this->tender->tender_title,
                'metaDescription' => $this->tender->tender_title,
                'tender_category' => $this->tender->tender_category,
                'tender_type' => $this->tender->tender_type,
                'tender_number' => $this->tender->tender_number,
                'tender_uin' => $this->tender->tender_uin,
                'tender_address' => $this->tender->tender_address,
                'tender_details' => $this->tender->tender_details,
                'tender_called_by' => $this->tender->tender_called_by,
                'tender_cost' => $this->tender->tender_cost,
                'tender_emd' => $this->tender->tender_emd,
                'tender_value' => $this->tender->tender_value,
                'document_types' => $this->tender->document_types,
                'last_date_of_submission' => $this->tender->last_date_of_submission,
                'last_date_of_document_sale' => $this->tender->last_date_of_document_sale,
                'last_date_for_clarification' => $this->tender->last_date_for_clarification,
                'pre_bid_meeting_date' => $this->tender->pre_bid_meeting_date,
                'pre_bid_meeting_venue' => $this->tender->pre_bid_meeting_venue,
                'bid_starting_date' => $this->tender->bid_starting_date,
                'technicalbid_opening_date' => $this->tender->technicalbid_opening_date,
            ]);
    }
}
