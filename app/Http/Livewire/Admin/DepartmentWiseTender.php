<?php

// app/Http/Livewire/Admin/DepartmentWiseTender.php

namespace App\Http\Livewire\Admin;

use Livewire\Component;
use App\Models\Department;
use Livewire\WithPagination;

class DepartmentWiseTender extends Component
{
    use WithPagination;

    public $department;

    protected $tenders;

    public function mount(Department $department)
    {
        $this->department = $department;
    }

    public function render()
    {
        return view('livewire.admin.department-tenders', [
            'tenders' => $this->department->tenders()->fastPaginate(10)->onEachSide(2)
        ]);
    }
}
