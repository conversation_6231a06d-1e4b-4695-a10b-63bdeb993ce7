<?php

namespace App\Http\Livewire\Admin;

use App\Models\Tender;
use Livewire\Component;
use App\Models\Department;
use Livewire\WithPagination;

class Tenders extends Component
{
    use WithPagination;

    public $search;
    public $department = 'all';
   
    protected $queryString = [
        'search' => ['except' => ''],
        'department' => ['except' => 'all']
    ];

    public function resetFilter()
    {
        $this->reset(['search', 'department']);
    }

    public function getDepartmentsProperty()
    {
        return Department::pluck('name', 'id')->all();
    }

    public function getUserProperty()
    {
        return auth()->user();
    }
    
    public function render()
    {
        return view('livewire.admin.tenders', [
            'tenders' => Tender::query()
                ->withCount('biddings')
                ->with(['user:id,name,email', 'department:id,name'])
                ->when($this->department != 'all', fn ($query) => $query->whereLike('department.name', $this->department))
                ->when($this->search != '', fn ($query) => $query->whereLike(['tender_title', 'tender_number'], $this->search))
                ->latest('id')
                ->fastPaginate(10)
                ->onEachSide(2),
        ]);
    }
}
