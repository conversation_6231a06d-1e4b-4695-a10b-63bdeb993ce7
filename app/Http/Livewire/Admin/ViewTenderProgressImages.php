<?php

namespace App\Http\Livewire\Admin;

use Livewire\Component;
use App\Models\TenderResult;
use App\Models\TenderResultImage;

class ViewTenderProgressImages extends Component
{
    public $show = false;
    public $tenderId;
    public $tenderResultId;
    public $images = [];
    public $selectedImage = null;
    
    protected $listeners = [
        'openTenderProgressImagesModal' => 'openTenderProgressImagesModal',
        'debugShowModal' => 'debugShowModal'
    ];
    
    public function mount()
    {
        logger()->info('ViewTenderProgressImages component mounted');
    }
    
    public function openTenderProgressImagesModal($tenderId)
    {
        try {
            logger()->info('Opening progress images modal for tender ID: ' . $tenderId);
            $this->tenderId = $tenderId;
            
            $tenderResult = TenderResult::where('tender_id', $tenderId)->first();
            
            if ($tenderResult) {
                logger()->info('Found tender result: ', [
                    'id' => $tenderResult->id, 
                    'tender_id' => $tenderResult->tender_id
                ]);
                
                $this->tenderResultId = $tenderResult->id;
                $this->loadImages();
                $this->show = true;
            } else {
                logger()->warning('No tender result found for tender ID: ' . $tenderId);
                $this->show = false;
                session()->flash('error', 'No tender result found for this tender.');
            }
        } catch (\Exception $e) {
            logger()->error('Error in openTenderProgressImagesModal: ' . $e->getMessage());
            logger()->error($e->getTraceAsString());
            $this->show = false;
            session()->flash('error', 'An error occurred while loading images: ' . $e->getMessage());
        }
    }
    
    public function loadImages()
    {
        try {
            logger()->info('Loading images for tender result ID: ' . $this->tenderResultId);
            
            $this->images = TenderResultImage::where('tender_result_id', $this->tenderResultId)
                ->orderBy('created_at', 'desc')
                ->get();
                
            logger()->info('Found ' . $this->images->count() . ' images');
                
            // If we have images, select the first one by default
            if ($this->images->count() > 0) {
                $this->selectedImage = $this->images->first()->id;
                logger()->info('Selected first image with ID: ' . $this->selectedImage);
            } else {
                $this->selectedImage = null;
                logger()->info('No images found, no image selected');
            }
        } catch (\Exception $e) {
            logger()->error('Error in loadImages: ' . $e->getMessage());
            logger()->error($e->getTraceAsString());
            $this->images = collect();
            $this->selectedImage = null;
        }
    }
    
    public function selectImage($imageId)
    {
        $this->selectedImage = $imageId;
    }
    
    public function debugShowModal()
    {
        logger()->info('Debug: directly showing modal');
        $this->show = true;
    }
    
    public function render()
    {
        $selectedImageDetails = null;
        if ($this->selectedImage) {
            $selectedImageDetails = $this->images->firstWhere('id', $this->selectedImage);
        }
        
        return view('livewire.admin.view-tender-progress-images', [
            'selectedImageDetails' => $selectedImageDetails
        ]);
    }
}
