<?php

namespace App\Http\Livewire\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search;
    public $role = 'all';

    protected $queryString = [
        'search' => ['except' => ''],
        'role' => ['except' => 'all'],
    ];

    protected $listeners = [
        'refreshData' => '$refresh'
    ];

    public function resetFilter()
    {
        $this->reset(['role', 'search']);
    }

    public function getRolesProperty()
    {
        return [
            'maker' => 'Maker',
            'checker' => 'Checker',
        ];
    }
    
    public function render()
    {
        return view('livewire.users.index', [
            'users' => User::query()
                ->with('department:id,name')
                ->when($this->role != 'all', fn ($query) => $query->where('role', $this->role))
                ->when($this->search != '', fn ($query) => $query->whereLike(['role', 'name', 'email'], $this->search))
                ->when($this->role == 'all', fn ($query) => $query->whereIn('role', [User::ROLE_MAKER, User::ROLE_CHECKER,User::ROLE_EVALUATOR]))
                ->latest('id')
                ->fastPaginate(10)
                ->onEachSide(2),

            'stats' => User::query()
                ->selectRaw("count(case when role = 'admin' then 1 end) as admin")
                ->selectRaw("count(case when role = 'maker' then 1 end) as maker")
                ->selectRaw("count(case when role = 'checker' then 1 end) as checker")
                ->first()
                ->setAppends([])
        ]);
    }
}
