<?php

namespace App\Http\Livewire\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Company extends Component
{
    use WithPagination;

    public $search;

    protected $queryString = [
        'search' => ['except' => '']
    ];

    protected $listeners = [
        'refreshData' => '$refresh'
    ];

    public function resetFilter()
    {
        $this->reset(['search']);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function render()
    {
        return view('livewire.users.company', [
            'users' => User::query()
                ->with('department:id,name', 'companyProfile')
                ->when($this->search != '', fn ($query) => $query->whereLike(['name', 'email'], $this->search))
                ->where('role', User::ROLE_COMPANY)
                ->latest('id')
                ->fastPaginate(10)
                ->onEachSide(2),
        ]);
    }
}
