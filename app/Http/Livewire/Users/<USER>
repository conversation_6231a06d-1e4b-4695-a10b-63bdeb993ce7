<?php

namespace App\Http\Livewire\Users;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;
use Illuminate\Validation\Rule;
use App\Rules\CheckValidPhoneNumber;

class Create extends Component
{
    public $name;
    public $email;
    public $password;
    public $role = "";
    public $gender = "male";
    public $phone;
    public $department;

    public function getRolesProperty()
    {
        return [
            [
                'label' => 'Maker',
                'value' => 'maker',
                'summary' => 'This user can create tender'
            ],
            [
                'label' => 'Checker',
                'value' => 'checker',
                'summary' => 'This user can verify/publish the tender'
            ],
            [
                'label' => 'Evaluator',
                'value' => 'evaluator',
                'summary' => 'This user can verify/publish the tender'
            ]
        ];
    }

    public function getDepartmentsProperty()
    {
        return Department::pluck('name', 'id');
    }

    public function save()
    {
        $validatedData = $this->validate([
            'name' => ['bail', 'required', 'string'],
            'department' => ['required', Rule::in(array_keys($this->departments->all()))],
            'gender' => ['required', 'string', Rule::in(['male', 'female', 'other'])],
            'role' => ['required', Rule::in(['maker', 'checker', 'evaluator'])],
            'phone' => ['required', 'min:10', 'max:10', new CheckValidPhoneNumber],
            'email' => ['required', 'email', 'unique:users'],
            'password' => ['nullable'],
        ]);

        User::create([
            'department_id' => $validatedData['department'],
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'gender' => $validatedData['gender'],
            'phone' => $validatedData['phone'],
            'role' => $validatedData['role'],
            'password' => $validatedData['password'] ? bcrypt($validatedData['password']) : bcrypt('secret'),
        ]);
 
        $this->reset('name', 'email', 'phone', 'password');
        $this->emit('saved');
        $this->bannerMessage('User saved.');
        $this->notify('User created');
    }

    public function render()
    {
        return view('livewire.users.create');
    }
}
