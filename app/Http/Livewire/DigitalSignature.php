<?php

namespace App\Http\Livewire;

use App\Models\Tender;
use Livewire\Component;

class DigitalSignature extends Component
{
    public function render()
    {
        $tender = Tender::first();
        $aoc = url('aoc.pdf');
        $certificate_path = $aoc;
        $type = pathinfo($certificate_path, PATHINFO_EXTENSION);
        $data = file_get_contents($certificate_path);
        $base64 = base64_encode($data);
        $img_base64 = public_path('valid.txt');
        $img_base64_handler = fopen($img_base64, 'r');
        $img_content = fread($img_base64_handler, filesize($img_base64));
        fclose($img_base64_handler);

        return view('livewire.digital-signature', [
            'tid' => $tender->id,
            'tender_title' => $tender->tender_title,
            'tender_number' => $tender->tender_number,
            'base64' => $base64,
            'img_content' => $img_content,
            'aoc' => $aoc,
        ]);
    }
}
