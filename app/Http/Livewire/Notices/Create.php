<?php

namespace App\Http\Livewire\Notices;

use App\Models\Notice;
use App\Traits\InteractsWithBanner;
use Livewire\Component;

class Create extends Component
{
    use InteractsWithBanner;

    public $title;
    public $link;

    public function save()
    {
        $validated = $this->validate([
            'title' => ['required', 'string'],
            'link' => ['nullable', 'url']
        ]);

        Notice::create([
            'title' => $validated['title'],
            'link' => $validated['link'],
            'published_at' => now()
        ]);

        $this->reset('title', 'link');
        $this->banner('Notice saved.');
        
        return redirect()->route('notices');
    }

    public function render()
    {
        return view('livewire.notices.create');
    }
}
