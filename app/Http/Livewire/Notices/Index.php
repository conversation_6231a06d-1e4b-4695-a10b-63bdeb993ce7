<?php

namespace App\Http\Livewire\Notices;

use App\Models\Notice;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search;
    public $role = 'all';

    protected $queryString = [
        'search' => ['except' => ''],
    ];

    protected $listeners = [
        'refreshData' => '$refresh'
    ];

    public function resetFilter()
    {
        $this->reset(['search']);
    }

    public function remove(Notice $notice)
    {
        $notice->delete();

        $this->notify('Notice deleted');

        $this->emit('refreshData');
    }

    public function render()
    {
        return view('livewire.notices.index', [
            'notices' => Notice::query()
                ->when($this->search != '', fn ($query) => $query->whereLike(['title'], $this->search))
                ->latest('id')
                ->fastPaginate(10)
                ->onEachSide(2),
        ]);
    }
}
