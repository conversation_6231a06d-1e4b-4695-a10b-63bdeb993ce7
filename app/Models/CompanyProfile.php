<?php

namespace App\Models;

use App\Enums\CompanyBusinessType;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyProfile extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'user_id',
        'gst_number',
        'type',
        'nchac_registration_no',
        'address',
        'meta'
    ];

    protected $casts = [
        'meta' => 'array',
        'type' => CompanyBusinessType::class
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
