<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DocumentReview extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'tender_id',
        'bidding_id',
        'user_id',
        'review',
        'status'
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function bidding()
    {
        return $this->belongsTo(Bidding::class);
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'accept' => 'success',
            'reject' => 'danger',
        };
    }

    public function getReviewStatusAttribute()
    {
        return match($this->status) {
            'accept' => 'Accepted',
            'reject' => 'Rejected',
        };
    }
}
