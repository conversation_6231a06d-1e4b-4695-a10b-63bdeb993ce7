<?php

namespace App\Models;

use App\Enums\EmdPaymentTypes;
use App\Enums\RazorpayPaymentStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmdPayment extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'user_id',
        'tender_id',
        'bidding_id',
        'payment_name',
        'payment_bank',
        'payment_type',
        'razorpay_order_id',
        'razorpay_payment_id',
        'payment_ref_no',
        'challan_or_dd_no',
        'photo',
        'amount',
        'status',
        'payment_at',
        'meta',
        'user_context',
    ];

    protected $casts = [
        'payment_type' => EmdPaymentTypes::class,
        'status' => RazorpayPaymentStatus::class,
        'payment_at' => 'datetime',
        'user_context' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bidding()
    {
        return $this->belongsTo(Bidding::class);
    }

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function getPhotoUrlAttribute()
    {
        return $this->photo
            ? Storage::disk('tender-emd')->temporaryUrl($this->photo, now()->addMinutes(5))
            : null;
    }
}
