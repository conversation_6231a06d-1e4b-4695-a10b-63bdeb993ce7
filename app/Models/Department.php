<?php

namespace App\Models;

use Snowflake\Snowflakes;
use Snowflake\SnowflakeCast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Department extends Model
{
    use HasFactory;
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'photo',
        'meta'
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    public function getPhotoUrlAttribute()
    {
        if (!$this->photo) {
            return null;
        }

        try {
            // Use S3 department-photos disk for all department photos
            return Storage::disk('department-photos')->temporaryUrl($this->photo, now()->addMinutes(5));
        } catch (\Exception $e) {
            logger()->error('Error generating S3 URL for department photo: ' . $this->photo . ': ' . $e->getMessage());

            // Fallback: try public disk for old records
            try {
                return Storage::disk('public')->url($this->photo);
            } catch (\Exception $fallbackException) {
                logger()->error('Fallback public disk also failed for department photo: ' . $this->photo . ': ' . $fallbackException->getMessage());
                return null;
            }
        }
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function tenders()
    {
        return $this->hasMany(Tender::class);
    }
}
