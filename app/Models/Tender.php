<?php

namespace App\Models;

use Carbon\Carbon;
use App\Enums\TenderStatus;
use App\Enums\TenderClassification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tender extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'user_id',
        'department_id',
        'tender_category',
        'tender_type',
        'tender_title',
        'tender_number',
        'tender_uin',
        'tender_address',
        'tender_details',
        'tender_called_by',
        'tender_cost',
        'tender_emd',
        'tender_value',

        'published_date',
        'last_date_of_submission',
        'last_date_of_document_sale',
        'last_date_for_clarification',

        'pre_bid_meeting_date',
        'pre_bid_meeting_venue',

        'document_types',
        'comparative_statement',

        'checker_id',
        'evaluator_id',
        'tender_status',
        'checker_remarks',
        'live_at',

        'tender_opening_date',
        'tender_opening_venue',
        'tender_opening_otp',
        'tender_opened_at',
        'tender_document_opened_at',

        'bid_starting_date',
        'technicalbid_opening_date',
        'pre_bid_meeting_minutes',
        'tender_technical_opening_otps',
        'tender_classification',
    ];

    protected $casts = [
        'document_types' => 'array',
        'published_date' => 'date',
        'last_date_of_submission' => 'datetime',
        'last_date_of_document_sale' => 'date',
        'last_date_for_clarification' => 'date',
        'pre_bid_meeting_date' => 'date',
        'live_at' => 'datetime',
        'tender_opening_date' => 'datetime',
        'tender_opened_at' => 'datetime',
        'tender_document_opened_at' => 'datetime',
        'bid_starting_date' => 'datetime',
        'technicalbid_opening_date' => 'datetime',
        'tender_status' => TenderStatus::class,
        'tender_classification' => TenderClassification::class,
        'tender_technical_opening_otps' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function tenderitems()
    {
        return $this->hasMany(Tenderitem::class);
    }

    public function documents()
    {
        return $this->hasMany(Upload::class);
    }

    public function biddings()
    {
        return $this->hasMany(Bidding::class);
    }

    public function companyBidding()
    {
        return $this->hasOne(Bidding::class)->where('user_id', auth()->id());
    }

    public function companyDocumentPayment()
    {
        return $this->hasOne(DocumentPayment::class)->where('user_id', auth()->id());
    }

    public function companyEmdPayment()
    {
        return $this->hasOne(EmdPayment::class)->where('user_id', auth()->id());
    }
    
    public function evaluators(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'evaluator_tender', 'tender_id', 'evaluator_id');
    }

    public function companyEmdPayments()
    {
        return $this->hasMany(EmdPayment::class);
    }

    public function acceptedBiddings()
    {
        return $this->hasMany(Bidding::class)->where('bidding_status', 'accepted');
    }

    public function result()
    {
        return $this->hasOne(TenderResult::class);
    }

    public function documentReviews()
    {
        return $this->hasMany(DocumentReview::class);
    }

    public function biddingPrices()
    {
        return $this->hasManyThrough(BiddingPrice::class, Tenderitem::class, 'tender_id', 'tenderitem_id');
    }

    public function biddingCompanies()
    {
        return $this->hasManyThrough(BiddingPrice::class, Tenderitem::class, 'tender_id', 'tenderitem_id')
            ->with('bidding:id,tender_id,company_name,company_email,company_phone');
    }

    // public function documentPayments()
    // {
    //     return $this->hasMany(DocumentPayment::class);
    // }

    public function hasTenderDocumentTypes(): bool
    {
        return !is_null($this->document_types);
    }

    public function scopeTenderIsApprovedOrArchived($query)
    {
        return $query->whereIn('tender_status', [TenderStatus::APPROVED->value, TenderStatus::ARCHIVED->value]);
    }

    public function scopeNotExpired($query)
    {
        return $query->where('last_date_of_submission', '>', Carbon::now());
    }

    public function showBidprices()
    {
        return $this->tender_opening_date <= now() && !is_null($this->tender_opened_at);
    }

    public function getIsTenderNewAttribute()
    {
        return $this->published_date >= Carbon::now()->subDays(25)->format('Y-m-d')
            ? true
            : false;
    }

    public function getPreBidMinutesDocumentPathAttribute()
    {
        return $this->pre_bid_meeting_minutes
            ? Storage::disk('minutes-documents')->temporaryUrl($this->pre_bid_meeting_minutes, now()->addMinutes(5))
            : null;
    }

    public function getComparativeStatementUrlAttribute()
    {
        return $this->comparative_statement
            ? Storage::disk('comparative-statements')->temporaryUrl($this->comparative_statement, now()->addMinutes(5))
            : null;
    }

    public function companies()
    {
        return $this->belongsToMany(CompanyProfile::class, 'company_profile_tender');
    }
    
    public function isSelectiveTender(): bool
    {
        // If tender_classification is null, treat it as open (for backward compatibility)
        if (is_null($this->tender_classification)) {
            return false;
        }
        return $this->tender_classification === TenderClassification::SELECTIVE;
    }
    
    public function isOpenTender(): bool
    {
        // If tender_classification is null, treat it as open (for backward compatibility)
        if (is_null($this->tender_classification)) {
            return true;
        }
        return $this->tender_classification === TenderClassification::OPEN;
    }
    
    public function companyCanAccessTender(User $user): bool
    {
        // Open tenders can be accessed by any company
        if ($this->isOpenTender()) {
            return true;
        }
        
        // For selective tenders, check if the company is in the list
        if ($this->isSelectiveTender()) {
            if ($user->companyProfile) {
                return $this->companies()->where('company_profile_id', $user->companyProfile->id)->exists();
            }
        }
        
        return false;
    }
}
