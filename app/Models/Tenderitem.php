<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tenderitem extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'tender_id',
        'item_type',
        'estimated_price',
        'quantity',
        'unit',
        'order',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function biddingPrices()
    {
        return $this->hasMany(BiddingPrice::class);
    }
}
