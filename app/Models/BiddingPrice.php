<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BiddingPrice extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'bidding_id',
        'tenderitem_id',
        'bidding_price',
        'quantity',
        'rate'
    ];

    public function tenderitem()
    {
        return $this->belongsTo(Tenderitem::class);
    }

    public function bidding()
    {
        return $this->belongsTo(Bidding::class)->where('bidding_status', 'accepted');
    }

    public function simplebidding()
    {
        return $this->belongsTo(Bidding::class, 'bidding_id');
    }
}
