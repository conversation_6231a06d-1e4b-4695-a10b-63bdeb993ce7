<?php

namespace App\Models;

use App\Enums\RazorpayPaymentStatus;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentPayment extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'tender_id',
        'bidding_id',
        'user_id',
        'payment_ref_no',
        'razorpay_order_id',
        'razorpay_payment_id',
        'razorpay_signature',
        'payment_at',
        'amount',
        'status',
        'payment_mode',
        'user_context',
    ];

    protected $casts = [
        'payment_at' => 'datetime',
        'status' => RazorpayPaymentStatus::class,
        'user_context' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }
}
