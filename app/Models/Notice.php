<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notice extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'title',
        'link',
        'tags',
        'published_at',
        'deactivated_at',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'deactivated_at' => 'datetime',       
    ];
}
