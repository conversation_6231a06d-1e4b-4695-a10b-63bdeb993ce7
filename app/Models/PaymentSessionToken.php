<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PaymentSessionToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'payment_type',
        'payment_id',
        'user_id',
        'session_id',
        'user_data',
        'expires_at',
    ];

    protected $casts = [
        'user_data' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * Generate a new session token for payment
     */
    public static function createForPayment($paymentType, $paymentId, $userId, $sessionId, $userData = [])
    {
        // Clean up expired tokens first
        static::where('expires_at', '<', now())->delete();
        
        // Clean up existing tokens for this payment
        static::where('payment_type', $paymentType)
              ->where('payment_id', $paymentId)
              ->delete();

        return static::create([
            'token' => Str::random(64),
            'payment_type' => $paymentType,
            'payment_id' => $paymentId,
            'user_id' => $userId,
            'session_id' => $sessionId,
            'user_data' => $userData,
            'expires_at' => now()->addHours(2), // 2 hour expiry
        ]);
    }

    /**
     * Find and validate a token
     */
    public static function findValidToken($token)
    {
        return static::where('token', $token)
                    ->where('expires_at', '>', now())
                    ->first();
    }

    /**
     * Clean up expired tokens
     */
    public static function cleanupExpired()
    {
        return static::where('expires_at', '<', now())->delete();
    }

    /**
     * Relationship to user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
