<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class TenderResult extends Model
{
    use HasFactory;
    use HasUlids;
    
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    
    protected $fillable = [
        'tender_id',
        'bidding_id',
        'user_id',
        'message',
        'aoc_document',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function bidding()
    {
        return $this->belongsTo(Bidding::class);
    }

    public function images()
    {
        return $this->hasMany(TenderResultImage::class);
    }

    public function getCompanyStatusAttribute()
    {
        return $this->bidding && $this->bidding->user_id === auth()->id() ? 'Accepted' : 'Rejected';
    }

    public function getAocDocumentUrlAttribute()
    {
        return $this->aoc_document
            ? Storage::disk('aoc-documents')->temporaryUrl($this->aoc_document, now()->addMinutes(5))
            : null;
    }
}