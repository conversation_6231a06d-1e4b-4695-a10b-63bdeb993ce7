<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BiddingDocument extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'bidding_id',
        'document_type',
        'document_path',
    ];

    protected $casts = [];

    public function getDocumentPathUrlAttribute()
    {
        return $this->document_path 
            // ? Storage::disk('public')->url($this->document_path)
            ? Storage::disk('applied-documents')->temporaryUrl($this->document_path, now()->addMinutes(5))
            : null;
    }
}
