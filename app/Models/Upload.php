<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Upload extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'tender_id',
        'title',
        'path',
        'size',
        'extension',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function getFilePathAttribute()
    {
        return $this->path 
            // ? Storage::disk('public')->url($this->path)
            ? Storage::disk('tender-documents')->temporaryUrl($this->path, now()->addMinutes(2))
            : null;
    }

    public function getSizeFormattedAttribute()
    {
        return $this->size 
            ? Str::bytesToHuman($this->size)
            : $this->size;
    }
}
