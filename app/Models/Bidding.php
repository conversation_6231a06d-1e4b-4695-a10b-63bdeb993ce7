<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bidding extends Model
{
    use HasFactory;
    use HasUlids;

    protected $fillable = [
        'tender_id',
        'user_id',
        'company_name',
        'company_email',
        'company_phone',
        'bidding_status',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function tender()
    {
        return $this->belongsTo(Tender::class);
    }

    public function pricings()
    {
        return $this->hasMany(BiddingPrice::class);
    }

    public function biddingDocuments()
    {
        return $this->hasMany(BiddingDocument::class);
    }

    public function emdPayment()
    {
        return $this->hasOne(EmdPayment::class);
    }

    public function documentReview()
    {
        return $this->hasOne(DocumentReview::class);
    }

    public function scopeAccepted($query)
    {
        return $query->where('bidding_status', 'accepted');
    }
}
