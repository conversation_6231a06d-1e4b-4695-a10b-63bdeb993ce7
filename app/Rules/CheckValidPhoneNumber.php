<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckValidPhoneNumber implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $firstDigitOfPhoneNumber = substr($value, 0, 1);

        return collect([7, 8, 6, 9])->contains($firstDigitOfPhoneNumber);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute is invalid.';
    }
}
