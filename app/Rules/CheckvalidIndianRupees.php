<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckvalidIndianRupees implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return preg_match('/^\d+(,\d{3})*(\.\d{1,2})?$/', $value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute must be a valid Indian rupee amount with comma-separated thousands and optional decimal.';
    }
}