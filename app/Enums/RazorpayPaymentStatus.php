<?php

namespace App\Enums;

use App\Traits\EnumToArray;

enum RazorpayPaymentStatus: string
{
    use EnumToArray;
    
    case CREATED = "created";
    case AUTHORIZED = "authorized";
    case CAPTURED = "captured";
    case REFUNDED = "refunded";
    case FAILED = "failed";
    case PAID = "paid";

	public function color(): string
    {
        return match($this) {
            self::CREATED => 'warning',
            self::AUTHORIZED,self::CAPTURED,self::PAID => 'success',
            self::REFUNDED => 'info',
            self::FAILED => 'danger',
            default => 'warning'
        };
    }

    public function statusLabel(): string
    {
        return match($this) {
            self::CREATED => 'Pending',
            self::AUTHORIZED,self::CAPTURED,self::PAID => 'Paid',
            self::REFUNDED => 'Refunded',
            self::FAILED => 'Failed',
            default => 'Pending'
        };
    }
}