<?php

namespace App\Enums;

use App\Traits\EnumToArray;

enum BiddingStatus: string
{
    use EnumToArray;
    
    case PENDING = "pending";
    case APPROVED = "approved";
    case CANCELLED = "cancelled";

    public function color()
    {
        return match($this) {
            TenderStatus::PENDING => 'warning',
            TenderStatus::APPROVED => 'success',
            TenderStatus::CANCELLED => 'danger',
            default => 'warning'
        };
    }
}
