<?php

namespace App\Enums;

use App\Traits\EnumToArray;

enum TenderStatus: string
{
    use EnumToArray;
    
    case PENDING = "pending";
    case APPROVED = "approved";
    case TECHNICAL = "technical";
    case ARCHIVED = "archived";
    case CANCELLED = "cancelled";

    public function color()
    {
        return match($this) {
            TenderStatus::PENDING => 'warning',
            TenderStatus::APPROVED => 'success',
            TenderStatus::TECHNICAL => 'blue',
            TenderStatus::ARCHIVED => 'gray',
            TenderStatus::CANCELLED => 'danger',
            default => 'info'
        };
    }
}
