<?php

namespace App\Traits;

use Illuminate\Support\Facades\Storage;

trait WithFileAttachment
{
    public $files = [];

    public function completeUpload(string $uploadedFileName, string $eventName)
    {
        foreach ($this->files as $file) {
            if ($file->getFilename() === $uploadedFileName) {
                $path = $file->store('/', $this->diskName);

                try {
                    // Generate temporary URL for S3 disks
                    $url = Storage::disk($this->diskName)->temporaryUrl($path, now()->addMinutes(5));
                } catch (\Exception $e) {
                    // Fallback to regular URL for non-S3 disks
                    $url = Storage::disk($this->diskName)->url($path);
                }

                $this->dispatchBrowserEvent($eventName, [
                    'url' => $url,
                    'href' => $url,
                ]);
            }
        }
    }

    public function removeUploadedFile(string $uploadedFileURL = null)
    {
        if ($uploadedFileURL == null) {
            return;
        }

        $file = new \SplFileInfo($uploadedFileURL);

        $exists = Storage::disk($this->diskName)->exists($file->getFilename());
        if ($exists) {
            Storage::disk($this->diskName)->delete($file->getFilename());

            return response()->json('Deleted', 200);
        }
    }
}
