<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/*
* Response::HTTP_INTERNAL_SERVER_ERROR = 500
* Response::HTTP_TOO_MANY_REQUESTS = 429
* Response::HTTP_UNPROCESSABLE_ENTITY = 422
* Response::HTTP_NOT_FOUND = 404
* Response::HTTP_FORBIDDEN = 403
* Response::HTTP_UNAUTHORIZED = 401
* Response::HTTP_BAD_REQUEST = 400
* Response::HTTP_NO_CONTENT = 204
* Response::HTTP_CREATED = 201
* Response::HTTP_OK = 200
*/

trait WithApiHelpers
{
    protected function respondWithSuccess($data, int $code = Response::HTTP_OK, string $message = ''): JsonResponse
    {
        return response()->json([
            'status' => $code,
            'message' => $message ?? Response::$statusTexts[$code],
            'data' => $data,
        ], $code);
    }

    protected function respondWithError(int $code, string $message = '', array $errors = []): JsonResponse
    {
        return response()->json([
            'status' => $code,
            'message' => $message ?? Response::$statusTexts[$code],
            'errors' => $errors
        ], $code);
    }

    protected function respondCreated(int $code = Response::HTTP_CREATED, string $message = 'Created'): JsonResponse
    {
        return response()->json([
            'status' => $code,
            'message' => $message ?? Response::$statusTexts[$code],
        ], $code);
    }

    protected function respondNotFound(int $code = Response::HTTP_NOT_FOUND, string $message = 'Not found'): JsonResponse
    {
        return response()->json([
            'status' => $code,
            'message' => $message ?? Response::$statusTexts[$code],
        ], $code);
    }
    
    protected function respondWithNoContent()
    {
        return response()->json(null, Response::HTTP_NO_CONTENT);
    }
}
