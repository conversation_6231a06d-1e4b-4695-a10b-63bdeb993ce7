<?php

namespace App\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

trait WithApiFileUpload
{
    protected function createFileObject($url)
    {
        $url = stripslashes($url);

        // First try S3 api-uploads disk
        if (Storage::disk('api-uploads')->exists($url)) {
            try {
                $tempPath = tempnam(sys_get_temp_dir(), 'api_upload_');
                $content = Storage::disk('api-uploads')->get($url);
                file_put_contents($tempPath, $content);

                $originalName = File::basename($url);
                $mimeType = File::mimeType($tempPath);

                return new UploadedFile(
                    $tempPath,
                    $originalName,
                    $mimeType,
                    error: true,
                    test: true
                );
            } catch (\Exception $e) {
                logger()->error('Failed to create file object from S3: ' . $e->getMessage());
            }
        }

        // Fallback to public disk for legacy files
        if (Storage::disk('public')->exists($url)) {
            $path = Storage::disk('public')->path($url);

            $originalName = File::basename($path);
            $mimeType = File::mimeType($path);

            return new UploadedFile(
                $path,
                $originalName,
                $mimeType,
                error: true,
                test: true
            );
        }

        return null;
    }
}
