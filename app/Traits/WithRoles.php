<?php

namespace App\Traits;

use App\Traits\WithPermissions;

trait WithRoles
{
    use WithPermissions;
    
    public function isSuperAdministrator()
    {
        return $this->role === 'super-admin';
    }

    public function isAdministrator()
    {
        return $this->role === 'admin';
    }

    public function isAdministratorOrSuper()
    {
        return $this->role === 'admin' || $this->role === 'super-admin';
    }

    public function isCompany()
    {
        return $this->role === 'company';
    }

    public function isMaker()
    {
        return $this->role === 'maker';
    }

    public function isChecker()
    {
        return $this->role === 'checker';
    }

    public function isMakerOrChecker()
    {
        return $this->role === 'maker' || $this->role === 'checker';
    }

    public function isNormalUser()
    {
        return $this->role === 'user';
    }
}
