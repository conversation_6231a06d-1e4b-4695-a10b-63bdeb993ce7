<?php

namespace App\Services;

use Aws\Sns\SnsClient;
use Illuminate\Support\Facades\Config;

class SmsService
{
    public $flowId;
    public $senderId;
    public $to;
    public $variables = [];

    protected $snsClient;

    public function __construct(string $flowId, string $senderId = null)
    {
        $this->flowId = $flowId;
        $this->senderId = $senderId;

        // Initialize the SNS client
        $this->snsClient = new SnsClient([
            'version' => 'latest',
            'region' => config('services.sns.region'),
            'credentials' => [
                'key' => config('services.sns.key'),
                'secret' => config('services.sns.secret'),
            ],
        ]);
    }

    public static function make(string $flowId, string $senderId = null)
    {
        return new static($flowId, $senderId);
    }

    public function to(string $mobile)
    {
        if (is_null($mobile)) {
            throw new \Exception("Mobile number is required");
        }

        $this->to = '+91' . $mobile;

        return $this;
    }

    public function addHeaders(array $headers = [])
    {
        // AWS SNS doesn't use headers in the same way, so we can ignore this method
        return $this;
    }

    public function addVariables(array $variables = [])
    {
        $this->variables = $variables;

        return $this;
    }

    private function message()
    {
        // Format your message with variables
        $message = "Message from " . $this->senderId . ": ";
        foreach ($this->variables as $key => $value) {
            $message .= "{$key}: {$value}, ";
        }

        return rtrim($message, ', ');
    }

    public function send()
    {
        try {
            $result = $this->snsClient->publish([
                'Message' => $this->message(),
                'PhoneNumber' => $this->to,
            ]);

            return $result;
        } catch (\Exception $e) {
            throw new \Exception("Failed to send SMS: " . $e->getMessage());
        }
    }
}