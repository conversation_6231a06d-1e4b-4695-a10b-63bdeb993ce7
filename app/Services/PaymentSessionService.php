<?php

namespace App\Services;

use App\Models\PaymentSessionToken;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class PaymentSessionService
{
    /**
     * Create a session token for payment and store it in the callback URL
     */
    public function createSessionToken($paymentType, $paymentId, $additionalData = [])
    {
        if (!Auth::check()) {
            Log::warning('Attempting to create session token without authenticated user');
            return null;
        }

        $user = Auth::user();
        $userData = array_merge([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
        ], $additionalData);

        $token = PaymentSessionToken::createForPayment(
            $paymentType,
            $paymentId,
            $user->id,
            Session::getId(),
            $userData
        );

        Log::info('Created payment session token', [
            'token' => $token->token,
            'payment_type' => $paymentType,
            'payment_id' => $paymentId,
            'user_id' => $user->id,
            'expires_at' => $token->expires_at
        ]);

        return $token;
    }

    /**
     * Recover session using token from request
     */
    public function recoverSession($request)
    {
        $token = $request->input('session_token') ?? $request->input('st');
        
        if (!$token) {
            Log::info('No session token found in request');
            return false;
        }

        $sessionToken = PaymentSessionToken::findValidToken($token);
        
        if (!$sessionToken) {
            Log::warning('Invalid or expired session token', ['token' => $token]);
            return false;
        }

        // Try to recover the user session
        $user = User::find($sessionToken->user_id);
        
        if (!$user) {
            Log::error('User not found for session token', [
                'token' => $token,
                'user_id' => $sessionToken->user_id
            ]);
            return false;
        }

        // Force session start if needed
        if (!Session::isStarted()) {
            Session::start();
        }

        // Log the user in
        Auth::login($user, true); // Remember the user
        
        // Regenerate session for security
        Session::regenerate();

        Log::info('Successfully recovered user session', [
            'token' => $token,
            'user_id' => $user->id,
            'old_session_id' => $sessionToken->session_id,
            'new_session_id' => Session::getId()
        ]);

        // Update the session token with new session ID
        $sessionToken->update(['session_id' => Session::getId()]);

        return true;
    }

    /**
     * Add session token to callback URL
     */
    public function addTokenToUrl($url, $token)
    {
        $separator = strpos($url, '?') !== false ? '&' : '?';
        return $url . $separator . 'st=' . $token;
    }

    /**
     * Clean up expired tokens (can be called from scheduled job)
     */
    public function cleanupExpiredTokens()
    {
        $deleted = PaymentSessionToken::cleanupExpired();
        Log::info('Cleaned up expired payment session tokens', ['count' => $deleted]);
        return $deleted;
    }

    /**
     * Get session token for a payment
     */
    public function getTokenForPayment($paymentType, $paymentId)
    {
        return PaymentSessionToken::where('payment_type', $paymentType)
                                 ->where('payment_id', $paymentId)
                                 ->where('expires_at', '>', now())
                                 ->first();
    }
}
