#!/bin/bash

# ETender Legacy Deployment Script
# This script builds a Docker image and deploys it to AWS using CloudFormation

# Exit on any error
set -e

# Configuration variables
ECR_REPOSITORY_URI="881490112356.dkr.ecr.ap-south-1.amazonaws.com/nchac/etender-laravel"
ECS_CLUSTER_NAME="etender-cluster"
VPC_ID="vpc-0adb6f6ede3ba08a6"
SUBNETS="subnet-09bf79ee94abed510,subnet-0c1df04797e279bf8"
STACK_NAME="etender-stack"
AWS_REGION="ap-south-1"
DB_NAME="etender"
DB_USERNAME="etender"
DB_PASSWORD=""

# Parse command line arguments
BRANCH_NAME="master"
IMAGE_TAG=$(git rev-parse --short HEAD)

# Get current deployment version and increment it
CURRENT_VERSION=$(aws cloudformation describe-stacks --profile nchac --stack-name $STACK_NAME --region $AWS_REGION --query "Stacks[0].Parameters[?ParameterKey=='DeploymentVersion'].ParameterValue" --output text 2>/dev/null || echo "v0")
if [[ $CURRENT_VERSION == "v0" ]]; then
  DEPLOYMENT_VERSION="v1"
elif [[ $CURRENT_VERSION =~ ^v([0-9]+)$ ]]; then
  NEXT_VERSION=$((${BASH_REMATCH[1]} + 1))
  DEPLOYMENT_VERSION="v$NEXT_VERSION"
else
  # Fallback to timestamp if we can't determine the version
  DEPLOYMENT_VERSION="v$(date +%Y%m%d%H%M%S)"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --branch)
      BRANCH_NAME="$2"
      shift 2
      ;;
    --image-tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --db-password)
      DB_PASSWORD="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [--branch BRANCH_NAME] [--image-tag IMAGE_TAG] [--db-password PASSWORD]"
      echo ""
      echo "Options:"
      echo "  --branch       The branch name for environment configuration (default: master)"
      echo "  --image-tag    The Docker image tag (default: git commit hash)"
      echo "  --db-password  The database password (required for first deployment)"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if DB password is provided for first deployment
if [[ -z "$DB_PASSWORD" ]]; then
  # Check if stack already exists
  if ! aws cloudformation describe-stacks --stack-name $STACK_NAME --region $AWS_REGION --profile nchac &>/dev/null; then
    echo "❌ Error: --db-password is required for first deployment"
    echo "Usage: $0 [--branch BRANCH_NAME] [--image-tag IMAGE_TAG] --db-password PASSWORD"
    exit 1
  fi
fi

echo "🚀 Starting deployment process..."
echo "Branch: $BRANCH_NAME"
echo "Image tag: $IMAGE_TAG"
echo "Deployment version: $DEPLOYMENT_VERSION (incremented from $CURRENT_VERSION)"

# Build Docker image
echo "📦 Building Docker image..."
docker build -t $ECR_REPOSITORY_URI:$IMAGE_TAG .

# Authenticate with AWS ECR
echo "🔑 Authenticating with AWS ECR..."
aws ecr get-login-password --region $AWS_REGION --profile nchac| docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

# Push Docker image to ECR
echo "⬆️ Pushing Docker image to ECR..."
docker push $ECR_REPOSITORY_URI:$IMAGE_TAG

# Prepare parameters
PARAMS="DeploymentVersion=$DEPLOYMENT_VERSION BranchName=$BRANCH_NAME VPCId=$VPC_ID Subnets=$SUBNETS ImageTag=$IMAGE_TAG ExistingECRRepositoryUri=$ECR_REPOSITORY_URI ExistingECSClusterName=$ECS_CLUSTER_NAME DBName=$DB_NAME DBUsername=$DB_USERNAME"

# Add DB password if provided
if [[ -n "$DB_PASSWORD" ]]; then
  PARAMS="$PARAMS DBPassword=$DB_PASSWORD"
fi

# Deploy to AWS using CloudFormation
echo "☁️ Deploying to AWS using CloudFormation..."
aws cloudformation deploy \
  --template-file laravel-deployment.yaml \
  --stack-name $STACK_NAME \
  --parameter-overrides $PARAMS \
  --capabilities CAPABILITY_IAM \
  --region $AWS_REGION \
  --profile nchac

# Get the CloudFormation stack outputs
echo "📋 Deployment details:"
aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query "Stacks[0].Outputs" \
  --region $AWS_REGION \
  --profile nchac

echo "✅ Deployment completed successfully!
  
Note: For the first deployment, you'll need to update your application's environment 
variables with the new RDS database endpoint shown in the outputs above."