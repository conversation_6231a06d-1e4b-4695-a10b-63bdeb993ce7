<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Users;
use App\Http\Controllers\Api\V1\LoginController;
use App\Http\Controllers\Api\V1\UploadController;
use App\Http\Controllers\Api\V1\DistrictController;
use App\Http\Controllers\Api\V1\DistrictBlockController;
use App\Http\Controllers\Api\V1\Notifications;
use App\Http\Controllers\Api\V1\Profiles;
use Illuminate\Support\Facades\DB;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->group(function () {
    Route::post('login', LoginController::class);

    // Uploads
    Route::post('uploads', UploadController::class);

    // Users
    Route::get('users', Users\IndexController::class);

    // District and Blocks
    Route::get('districts', DistrictController::class);
    Route::post('districts/blocks', DistrictBlockController::class);

    // Notifications
    Route::get('notifications', Notifications\IndexController::class)->middleware('auth:sanctum');

    // Profiles
    Route::post('profiles/change-password', Profiles\ChangePasswordController::class)->middleware('auth:sanctum');

    Route::post('logout', Profiles\LogoutController::class)->middleware('auth:sanctum');
});
// In routes/api.php
Route::get('/health', function () {
    try {
        // Test database connection with a simple query
        $result = DB::select('SELECT 1 as connection_test');

        if (isset($result[0]->connection_test) && $result[0]->connection_test == 1) {
            return response()->json([
                'status' => 'ok',
                'message' => 'Application is healthy',
                'timestamp' => now()->toIso8601String(),
                'environment' => config('app.env')
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Database returned unexpected result'
            ], 503);
        }
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ], 503);
    }
});