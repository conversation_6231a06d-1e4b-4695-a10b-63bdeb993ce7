<?php

use App\Http\Livewire\Admin;
use App\Http\Livewire\Front;
use App\Http\Livewire\Users;
use App\Http\Livewire\Company;
use App\Http\Livewire\Notices;
use App\Http\Livewire\Tenders;
use App\Http\Livewire\Departments;
use App\Http\Livewire\Notifications;
use Illuminate\Support\Facades\Route;
use App\Http\Livewire\DigitalSignature;
use App\Http\Middleware\VerifyCsrfToken;
use App\Http\Middleware\CheckValidTender;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Pages\HomeController;
use App\Http\Livewire\Front\CompanyRegistration;
use App\Http\Controllers\RazorpayWebhookController;
use App\Http\Controllers\RazorpayEmdPaymentWebhookController;
use App\Http\Controllers\TenderEmdPaymentController;
use App\Http\Controllers\TinymceImageUploadController;
use App\Http\Controllers\TenderDocumentPaymentController;
use App\Http\Controllers\RazorpayVerificationController;
use App\Http\Livewire\Tenders\OpenTechnicalDocument;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('which-php', function () {
// 	return phpinfo();
// });

// Route::get('/log', function () {
//     Log::error('This is a critical message Sent from Laravel App');
//     return 'ok';
// });

// Welcome page
// Route::get('/', fn () => to_route('dashboard'))->middleware(['auth']);
Route::get('/', HomeController::class)->name('pages.welcome');
Route::view('about', 'pages.about')->name('pages.about');
Route::view('contact', 'pages.contact')->name('pages.contact');
Route::get('privacy-policy', Front\PrivacyPolicy::class)->name('privacy');
Route::get('data-management-policy', Front\DataManagementPolicy::class)->name('data-management-policy');
Route::view('licence', 'pages.licence')->name('licence');

// Dashboard
Route::view('dashboard', 'dashboard')->middleware(['auth'])->name('dashboard');

// Users
Route::get('users', Users\Index::class)->middleware(['auth'])->name('users');
Route::get('users/create', Users\Create::class)->middleware(['auth'])->name('users.create');
Route::get('users/{user}/edit', Users\Edit::class)->middleware(['auth'])->name('users.edit');

// Users as Company
Route::get('companies', Users\Company::class)->middleware(['auth'])->name('companies');

// Tenders
Route::get('tenders', Tenders\Index::class)->middleware(['auth', 'role:maker,checker'])->name('tenders');
Route::get('tenders/create', Tenders\Create::class)->middleware(['auth', 'role:maker,admin,super-admin'])->name('tenders.create');
Route::get('tenders/{tender}/edit', Tenders\Edit::class)->middleware(['auth', 'role:maker,checker'])->name('tenders.edit');
Route::get('tenders/{tender}/show', Tenders\Show::class)->middleware(['auth'])->name('tenders.show');
Route::get('tenders/{tender}/biddings', Tenders\Bidding::class)->middleware(['auth'])->name('tenders.biddings');
Route::get('tenders/{tender}/sign', Tenders\Result::class)->middleware(['auth'])->name('tenders.sign');
Route::get('tenders/{tender}/comparativestatement', Tenders\ComparativeStatement::class)->middleware(['auth'])->name('tenders.comparative-statement');


Route::get('tenders/open/technical/bid/{tender}', Tenders\OpenTechnicalDocument::class)
    ->middleware(['auth'])
    ->name('tenders.open-technical-document');


// Admin Tenders List
Route::get('admin/tenders', Admin\Tenders::class)->middleware(['auth', 'role:admin'])->name('admin.tenders');
Route::get('admin/tenders/{tender}/details', Admin\TenderDetails::class)
    ->middleware(['auth', 'role:admin'])
    ->name('admin.tenders.show')
    ->middleware(CheckValidTender::class);
Route::get('admin/tenders/{tender}/edit', Admin\TenderEdit::class)
    ->middleware(['auth'])
    ->name('admin.tenders.edit')
    ->middleware(CheckValidTender::class);
Route::get('admin/dashboard', Admin\AdminDashboard::class)
    ->middleware(['auth', 'role:admin'])
    ->name('admin.dashboard');
Route::get('admin/dashboard/tenders/{department}', Admin\DepartmentWiseTender::class)
    ->middleware(['auth', 'role:admin'])
    ->name('admin.dashboard.tenders');

// Public Tenders
Route::get('tenders/all', Front\Tenders::class)->name('front.tenders');
Route::get('tenders/{tender}/details', Front\TenderDetails::class)
    ->name('front.tenders.show')
    ->middleware([CheckValidTender::class, \App\Http\Middleware\CheckSelectiveTenderAccess::class]);
Route::get('tenders/{tender}/apply', Front\TenderApply::class)
    ->name('front.tenders.apply')
    ->middleware(['auth', 'can:view-bidding,tender', \App\Http\Middleware\CheckSelectiveTenderAccess::class]);

Route::get('tenders/{tender}/documentpayment', Front\TenderDocumentPayment::class)
    ->name('front.tenders.documentpayment')
    ->middleware(['auth', \App\Http\Middleware\CheckSelectiveTenderAccess::class]);

Route::get('tenders/{tender}/documentpayment/rp', [TenderDocumentPaymentController::class, 'process'])
    ->name('front.tenders.documentpayment-rp')
    ->middleware(['auth', \App\Http\Middleware\CheckSelectiveTenderAccess::class]);

// Updated callback route to match the name used in your Blade view
// No middleware to ensure it works even if session is lost
Route::match(['get', 'post'], 'tenders/documentpayment/rp/callback', [TenderDocumentPaymentController::class, 'callback'])
    ->name('front.tenders.documentpayment-rp-callback')
    ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);

Route::get('tenders/{tender}/emd-payment', Front\TenderEmdPayment::class)
    ->name('front.tenders.emdpayment')
    ->middleware(['auth', 'can:view-emdpayment,tender', \App\Http\Middleware\CheckSelectiveTenderAccess::class]);

Route::post('tenders/{tender}/emdpayment/rp', [TenderEmdPaymentController::class, 'process'])
    ->name('front.tenders.emdpayment-rp')
    ->middleware(['auth', \App\Http\Middleware\CheckSelectiveTenderAccess::class]);

// No middleware to ensure it works even if session is lost
Route::match(['get', 'post'], 'tenders/emdpayment/rp/callback', [TenderEmdPaymentController::class, 'callback'])
    ->name('front.tenders.emdpayment-rp-callback')
    ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);

// Profile
Route::get('profile', ProfileController::class)->middleware(['auth'])->name('profile');
Route::view('profile/settings', 'profile-settings')->middleware(['auth'])->name('profile.settings');

// Departments
Route::get('departments', Departments\Index::class)->middleware(['auth'])->name('departments');
Route::get('departments/{department}/edit', Departments\Edit::class)->middleware(['auth'])->name('departments.edit');

// Company Biddings
Route::get('biddings', Company\Biddings::class)->name('company.biddings')->middleware('auth');

// Notices
Route::get('notices', Notices\Index::class)->name('notices')->middleware('auth');
Route::get('notices/create', Notices\Create::class)->name('notices.create')->middleware('auth');

// Developers
// Route::view('developers', 'developers.index')->name('developers');
// Route::view('developers/components', 'developers.components')->name('developers.components');
// Route::view('developers/api', 'developers.api')->name('developers.api');

// Notifications
Route::get('notifications', Notifications\Index::class)->middleware(['auth'])->name('notifications');

// Tinymce Image Upload Endpoint
Route::post('tinymce/upload', TinymceImageUploadController::class)->name('tinymce.upload');

// Razorpay Webhooks
Route::post('webhooks/rp', [RazorpayWebhookController::class, 'handle'])->withoutMiddleware([VerifyCsrfToken::class]);
Route::post('webhooks/rp/emd', [RazorpayEmdPaymentWebhookController::class, 'handle'])->withoutMiddleware([VerifyCsrfToken::class]);

// Company Registration
Route::get('company/register', CompanyRegistration::class)
    ->middleware('guest')
    ->name('company.registration');

// Digital Signature
Route::get('digital-signature', DigitalSignature::class)
    ->middleware('auth')
    ->name('digital-signature');

// Payment Success/Failure Pages - REMOVED
// Users are now redirected directly to relevant pages with flash messages
// This eliminates redundant payment status display across multiple pages

// Razorpay Verification - Auth required for security
Route::get('razorpay/verify/{orderId?}', [RazorpayVerificationController::class, 'verifyOrder'])
    ->name('razorpay.verify')
    ->middleware('auth');

// Debug endpoint for production payment issues
Route::get('debug/payment/{orderId}', function($orderId) {
    if (!auth()->check()) {
        return response()->json(['error' => 'Authentication required'], 401);
    }

    $documentPayment = \App\Models\DocumentPayment::where('razorpay_order_id', $orderId)->first();
    $emdPayment = \App\Models\EmdPayment::where('razorpay_order_id', $orderId)->first();

    $debugInfo = [
        'order_id' => $orderId,
        'timestamp' => now()->toISOString(),
        'environment' => app()->environment(),
        'user_id' => auth()->id(),
        'document_payment' => $documentPayment ? [
            'id' => $documentPayment->id,
            'status' => $documentPayment->status,
            'payment_ref_no' => $documentPayment->payment_ref_no,
            'razorpay_payment_id' => $documentPayment->razorpay_payment_id,
            'amount' => $documentPayment->amount,
            'payment_at' => $documentPayment->payment_at,
            'created_at' => $documentPayment->created_at,
            'updated_at' => $documentPayment->updated_at,
        ] : null,
        'emd_payment' => $emdPayment ? [
            'id' => $emdPayment->id,
            'status' => $emdPayment->status,
            'payment_ref_no' => $emdPayment->payment_ref_no,
            'razorpay_payment_id' => $emdPayment->razorpay_payment_id,
            'amount' => $emdPayment->amount,
            'payment_at' => $emdPayment->payment_at,
            'created_at' => $emdPayment->created_at,
            'updated_at' => $emdPayment->updated_at,
        ] : null,
    ];

    return response()->json($debugInfo);
})->middleware('auth');

// In routes/web.php or routes/api.php
Route::get('/health', function () {
    return response()->json(['status' => 'ok'], 200);
});


Route::get('/db-debug', function () {
    $config = config('database.connections.mysql');
    // Remove password for security
    $safeConfig = $config;
    $safeConfig['password'] = 'REDACTED';

    return [
        'connection_vars' => $safeConfig,
        'env_vars' => [
            'DB_HOST' => env('DB_HOST'),
            'DB_PORT' => env('DB_PORT'),
            'DB_DATABASE' => env('DB_DATABASE'),
            'DB_USERNAME' => env('DB_USERNAME'),
            'DB_CONNECTION' => env('DB_CONNECTION'),
            // Don't expose actual password
            'DB_PASSWORD_SET' => !empty(env('DB_PASSWORD')),
        ],
        'can_connect' => (function() {
            try {
                DB::connection()->getPdo();
                return true;
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        })(),
        'hostname_resolution' => gethostbyname(env('DB_HOST')),
    ];
});

require __DIR__ . '/auth.php';
