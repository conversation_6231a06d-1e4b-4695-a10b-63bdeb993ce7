AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for RDS database and related resources for ETender application.

Parameters:
  VPCId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be deployed.

  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: List of subnet IDs for database deployment.

  DBName:
    Type: String
    Default: etender
    Description: Name for the RDS database.

  DBUsername:
    Type: String
    Default: etender
    Description: Username for the RDS database.

  DBPassword:
    Type: String
    NoEcho: true
    Description: Password for the RDS database. Must be at least 8 characters.

  EnvironmentName:
    Type: String
    Default: prod
    Description: Environment name (e.g., dev, staging, prod)
    AllowedValues:
      - dev
      - staging
      - prod

Resources:
  # DB Subnet Group for RDS
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS instance
      SubnetIds: !Ref Subnets

  # Security Group for RDS
  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for RDS instance
      VpcId: !Ref VPCId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0

  # RDS Instance (t4g.micro for cost optimization)
  Database:
    Type: AWS::RDS::DBInstance
    DependsOn: RDSSecurityGroup
    Properties:
      DBName: !Ref DBName
      Engine: mysql
      EngineVersion: "8.0"
      MasterUsername: !Ref DBUsername
      MasterUserPassword: !Ref DBPassword
      DBInstanceClass: db.t4g.micro
      AllocatedStorage: 20
      MaxAllocatedStorage: 100
      StorageType: gp3
      StorageEncrypted: true
      MultiAZ: false
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - !GetAtt RDSSecurityGroup.GroupId
      BackupRetentionPeriod: 7
      DeletionProtection: false
      PubliclyAccessible: true
      EnablePerformanceInsights: false
      MonitoringInterval: 0
      # Add tags to identify this as part of the environment
      Tags:
        - Key: Environment
          Value: !Ref EnvironmentName
    DeletionPolicy: Snapshot

Outputs:
  DatabaseSecurityGroupId:
    Description: Security Group ID for the database
    Value: !GetAtt RDSSecurityGroup.GroupId
    Export:
      Name: !Sub "${AWS::StackName}-DBSecurityGroupId"

  DatabaseEndpoint:
    Description: Endpoint of the RDS database
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub "${AWS::StackName}-DBEndpoint"

  DatabasePort:
    Description: Port of the RDS database
    Value: !GetAtt Database.Endpoint.Port
    Export:
      Name: !Sub "${AWS::StackName}-DBPort"