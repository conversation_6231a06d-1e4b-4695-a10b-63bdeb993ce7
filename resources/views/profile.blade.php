<x-app-layout title="Profile">
    <x-section-centered>
        <x-card overflow-hidden card-classes="mb-8">
            <div class="w-full h-36 text-slate-200/50 inset-x-0 top-0 absolute" style="background-image: linear-gradient(currentColor 1px, transparent 1px), linear-gradient(to right, currentColor 1px, transparent 1px); background-size: 40px 40px;"></div>
            <div class="w-full h-36 inset-x-0 top-0 absolute bg-gradient-to-tl from-white"></div>

            <x-media class="mb-6 relative overflow-hidden py-4">
                <x-slot name="mediaObject">
                    <div class="h-24 w-24 rounded-lg bg-gray-100 ring-4 ring-white relative">
                        <img src="{{ $user->photo_url }}" alt="user photo" class="object-fit w-auto h-24 rounded-lg">
                    </div>
                </x-slot>
                <x-slot name="mediaBody">
                    <div class="flex flex-col md:flex-row md:items-center relative">
                        <div class="flex-1">
                            <x-heading size="lg" class="mb-1">{{ $user->name }}</x-heading>
                            <p>Role: {{ Str::title($user->role) }}</p>
                            <p class="text-xs text-gray-500">
                                Last login: @date($user->last_online_at)
                            </p>
                        </div>
                        <div class="mt-4 md:mt-0 md:pr-2">
                            <x-button class="leading-4" color="white" href="{{ route('profile.settings') }}" tag="a"><x-icon-setting class="w-4 h-4 mr-1 -ml-1"/>Settings</x-button>
                        </div>
                    </div>
                </x-slot>
            </x-media>
        
            <x-description-list>
                <x-description-list.item>
                    <x-slot name="title">Email</x-slot>
                    <x-slot name="description">{{ $user->email }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">Phone</x-slot>
                    <x-slot name="description">{{ $user->phone }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">GST Number</x-slot>
                    <x-slot name="description">{{ data_get($user, 'companyProfile.gst_number') }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">NCHAC Registration Number</x-slot>
                    <x-slot name="description">{{ data_get($user, 'companyProfile.nchac_registration_no') }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">Business Entity Type</x-slot>
                    <x-slot name="description">{{ data_get($user, 'companyProfile.type') }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">Address</x-slot>
                    <x-slot name="description">{{ data_get($user, 'companyProfile.address') }}</x-slot>
                </x-description-list.item>
            </x-description-list>
        </x-card>
 
    </x-section-centered>
</x-app-layout>
