@auth
	<x-sidebar>
		<x-slot name="logo">
			<x-application-logo class="!text-gray-100 text-xl" />
		</x-slot>

		<a href="{{ route('pages.welcome') }}" class="flex items-center py-2 text-slate-200 mb-1 rounded-lg px-2 py-1 block hover:bg-slate-700 transition duration-200 ease-in-out">
			<x-icon-arrow-up-left class="mr-4 text-slate-400 w-5 h-5"/>Back to website
		</a>

		<x-nav-item to="{{ route('dashboard') }}" class="flex items-center py-2 text-slate-200">
			<x-icon-home class="mr-4 text-slate-400 w-5 h-5"/>Home
		</x-nav-item>

		@if(auth()->user()->isMaker() or auth()->user()->isChecker())
		<x-nav-item to="{{ route('tenders') }}" class="flex items-center py-2 text-slate-200">
			<x-icon-file class="mr-4 text-slate-400 w-5 h-5"/>Tenders
		</x-nav-item>
		@endif

		@can('admin')
			<x-nav-item to="{{ route('users') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-user class="mr-4 text-slate-400 w-5 h-5"/>Users
			</x-nav-item>

			<x-nav-item to="{{ route('departments') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-building class="mr-4 text-slate-400 w-5 h-5"/>Departments
			</x-nav-item>

			<x-nav-item to="{{ route('companies') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-briefcase class="mr-4 text-slate-400 w-5 h-5"/>Companies
			</x-nav-item>

			<x-nav-item to="{{ route('notices') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-info-circle class="mr-4 text-slate-400 w-5 h-5"/>Notices
			</x-nav-item>

			<x-nav-item to="{{ route('admin.tenders') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-document class="mr-4 text-slate-400 w-5 h-5"/>Tenders
			</x-nav-item>
			<x-nav-item to="{{ route('admin.dashboard') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-document class="mr-4 text-slate-400 w-5 h-5"/>Dashboard
			</x-nav-item>
		@endcan


		@can('company')
			<x-nav-item to="{{ route('company.biddings') }}" class="flex items-center py-2 text-slate-200">
				<x-icon-building class="mr-4 text-slate-400 w-5 h-5"/>Tenders
			</x-nav-item>
		@endcan
 
		{{-- <x-nav-item to="{{ route('notifications') }}" class="flex items-center py-2 text-slate-200 justify-between">
			<div class="flex items-center"><x-icon-notification class="mr-4 text-slate-400 w-5 h-5"/>Notifications</div>
			@cannot('super')
				<livewire:notifications.count />
			@endcannot
		</x-nav-item> --}}

		<div class="my-3"></div>

		<x-slot name="footer">
			<div class="px-4 py-4">
				{{-- <x-nav-item to="{{ route('developers') }}" class="flex items-center py-2 text-slate-200">
					<x-icon-setting2 class="mr-4 text-slate-400 w-5 h-5"/>Developers
				</x-nav-item>
				<x-nav-item to="#" class="flex items-center py-2 text-slate-200">
					<x-icon-flag class="mr-4 text-slate-400 w-5 h-5"/>Help
				</x-nav-item>
				<x-nav-item to="#" class="flex items-center py-2 text-slate-200">
					<x-icon-lifebuoy class="mr-4 text-slate-400 w-5 h-5"/>Support
				</x-nav-item> --}}

				<p class="text-sm text-slate-400/75 pl-2">{{ config('app.name') }} &copy; {{ date('Y') }}</p>
				<p class="text-sm text-slate-400/75 pl-2">Powered by Bordoichila.</p>
			</div>
		</x-slot>
	</x-sidebar>
@endauth
