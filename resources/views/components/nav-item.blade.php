@php
// $navlinkActive = \Illuminate\Support\Str::startsWith(request()->url(), $to) ? 'bg-slate-700 text-slate-200' : 'text-slate-300';

$navlinkActive = (request()->url() === $to) ? 'bg-slate-700 text-slate-200 shadow' : 'text-slate-300';
@endphp

<a 
	href="{{ $to }}" 
	{{ $attributes->merge([
		'class' => 'overflow-hidden relative mb-1 rounded-md px-3 py-1 block hover:bg-slate-700 transition duration-200 ease-in-out '. $navlinkActive
	]) }}>
	{{ $slot }}

	@if(request()->url() === $to)
		<span class="w-1 h-5 rounded-r-full absolute left-0 bg-cyan-600"></span>
	@endif
</a>
