@props([
	'id' => null,
	'label' => null,
	'name' => null,
	'hint' => null,
	'appendBefore' => null,
	'appendAfter' => null,
	'withErrorMessage' => true,
	'errorMessageFor' => null,
	'optional' => false,
	'noMargin' => false,
	'readonly' => false,
	'invert' => false
])
 
<div class="{{ $noMargin ? 'mb-0' : 'mb-5' }}">	
    @if($label)
		<x-label class="mb-1" for="{{ $name }}" :optional="$optional">{{ $label }}</x-label>
    @endif

	@isset($hint)
		<x-text-hint class="mb-1">{{ $hint }}</x-text-hint>
	@endisset

    <div class="relative" x-data="{ togglePassword: false }" x-cloak>
        <input
			id="{{ $id ?? $name }}" 
			autocomplete="off"
			x-ref="input"
        	:type="togglePassword === true ? 'text': 'password'"	 
            name="{{ $name }}"
 
			{{ $attributes->class([
				'form-input transition duration-150 ease-in-out px-3 py-2 block w-full text-gray-700 font-sans rounded-md text-left focus:outline-none focus:border-indigo-500 focus:ring-indigo-500 border-b sm:text-sm placeholder-gray-400 disabled:bg-gray-200' => true,
				'bg-gray-100 border-transparent border-b-gray-400' => $invert,
				'bg-white border-gray-300 shadow-sm' => !$invert,
				'border-red-300' => $errors->has($name) && $withErrorMessage,
				'bg-gray-200' => $readonly
			])->merge() }}
		/>	

		<div class="absolute top-0 right-0 text-sm font-medium cursor-pointer inline-flex items-center justify-center w-10 h-full" x-on:click.prevent="togglePassword = !togglePassword">
			<svg x-show="togglePassword" class="w-5 h-5 stroke-current text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
			</svg>

			<svg x-show="!togglePassword" class="w-5 h-5 stroke-current text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
			</svg>
		</div>
    </div>
	
	@if($withErrorMessage)
		<x-input-error for="{{ $errorMessageFor ?? $name }}" class="mt-2" />
	@endif
</div>
