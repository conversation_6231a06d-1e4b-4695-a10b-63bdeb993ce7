@props([
    'noPadding' => false,
    'overflowHidden' => false,
    'formAction' => false,
    'shadow' => 'default',
    'iconColor' => 'text-indigo-400',
    'iconBgColor' => 'bg-indigo-50',
    'tag' => 'div'
])

@php
    $shadowClass = [
        'small' => 'shadow-md',
        'default' => 'shadow',
        'medium' => 'shadow-md',
        'large' => 'shadow-lg',
    ][$shadow];

    $cardPadding = $noPadding ? 'p-0' : 'p-5 md:pr-16 relative leading-5 flex-1';
@endphp

<div class="bg-white hover:shadow-md transition-all duration-300 rounded-lg flex flex-col h-full relative overflow-hidden {{ $shadowClass }}">
    <{{ $tag }} {{ 
		$attributes->merge([
			'class' => $cardPadding
		]) 
	}}>
        {{ $slot }}
    </{{ $tag }}>

    @if(isset($footer))
        <div {{ $footer->attributes->merge(['class' => 'px-5 py-2 bg-gray-50 rounded-b-lg']) }}>
            {{ $footer }}
        </div>
    @endif

    @isset($icon)
        <div class="absolute rounded-full transform rotate-12 flex items-center justify-center -top-5 -right-4 h-24 w-24 {{ $iconBgColor }} {{ $iconColor }}">
       		{{ $icon }}
        </div>
    @endisset
</div>
