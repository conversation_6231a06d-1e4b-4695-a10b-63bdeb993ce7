@aware([
    'tableCondensed' => false,
	'tableBorderedFull' => false,
	'tableStickyFirstColumn' => false,
	'tableStickyLastColumn' => false
])

<td {{ $attributes->class([
	'px-6',
	'py-3' => ! $tableCondensed,
	'py-1.5' => $tableCondensed,
	'border border-b-0 border-gray-200 first-of-type:border-l-0 last-of-type:border-r-0' => $tableBorderedFull,
    'border-t border-gray-100' => ! $tableBorderedFull,
	'first:sticky first:left-0 first:z-20 first:bg-white first:text-left first:shadow' => $tableStickyFirstColumn,
	'last:sticky last:right-0 last:z-20 last:bg-white last:shadow' => $tableStickyLastColumn
])->merge() }}>
	{{ $slot }}
</td>
