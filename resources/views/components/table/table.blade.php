@props([
	'withShadow' => true,
	'rounded' => true,
	'tableBordered' => false,
	'tableCondensed' => false,
	'tableBorderedFull' => false,
	'tableStickyFirstColumn' => false,
	'tableStickyLastColumn' => false
])

<div
    @class([
        'relative overflow-hidden',
        'bg-white shadow' => $withShadow,
		'rounded-lg' => $rounded
    ])
>
	<div 
		@class([
			'overflow-x-auto overflow-y-auto scrollbar relative',
			'shadow-sm rounded-lg' => $withShadow,
			'rounded-lg' => $rounded,
			'border-t' => ! $withShadow,
			'border' => $tableBordered,
			// 'border-t-0' => $tableBorderedFull
		])>
		<table
			@class([
                'border-collapse min-w-full table-datatable',
                'bg-white' => $withShadow
            ])
		>
			{{ $slot }}
		</table>
	</div>
</div>
