@aware([
    'tableCondensed' => false,
	'tableBorderedFull' => false,
	'tableStickyFirstColumn' => false,
	'tableStickyLastColumn' => false
])

<th {{ $attributes->class([
	'whitespace-nowrap border-b border-gray-200 px-6 bg-gray-50 text-left text-xs leading-4 font-semibold text-gray-500 uppercase tracking-wider',
	'py-3' => ! $tableCondensed,
	'py-1.5' => $tableCondensed,
	'border border-t-0 first-of-type:border-l-0 last-of-type:border-r-0' => $tableBorderedFull,
	'first:sticky first:left-0 first:z-20 first:text-left first:shadow' => $tableStickyFirstColumn,
	'last:sticky last:right-0 last:z-20 last:shadow' => $tableStickyLastColumn
])->merge() }}>
	{{ $slot }}
</th>
