<div>
    <script>
        // Additional event listener for direct browser events
        document.addEventListener('open-progress-images', (event) => {
            console.log('Received open-progress-images event with data:', event.detail);
            // Forward to Livewire
            Livewire.emit('openTenderProgressImagesModal', event.detail.tenderId);
        });
    </script>
    
    <!-- Simple modal without x-dialog-modal component -->
    <div id="tender-progress-images-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" 
         style="{{ $show ? '' : 'display: none;' }}"
         x-data="{}"
         @keydown.escape.window="$wire.set('show', false)">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-screen overflow-hidden flex flex-col">
            <!-- Header -->
            <div class="bg-gray-100 px-6 py-4 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Tender Progress Images</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" wire:click="$set('show', false)">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <!-- Content -->
            <div class="p-6 flex-1 overflow-auto">
                @if(count($images) === 0)
                    <div class="p-4 text-center">
                        <p class="text-gray-500">No progress images have been uploaded for this tender yet.</p>
                    </div>
                @else
                    <p class="mb-4">Found {{ count($images) }} image(s)</p>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        @foreach($images as $image)
                            <div class="border rounded-lg p-2">
                                <img src="{{ $image->file_path }}" alt="{{ $image->title }}" class="w-full h-40 object-cover rounded">
                                <div class="mt-2">
                                    <p class="text-sm font-medium truncate">{{ $image->title }}</p>
                                    <p class="text-xs text-gray-500">{{ $image->created_at->format('M d, Y') }}</p>
                                    
                                    @if($image->has_gps_data)
                                        <p class="text-xs text-green-600 mt-1">Has location data</p>
                                        <a 
                                            href="https://maps.google.com/?q={{ $image->latitude }},{{ $image->longitude }}" 
                                            target="_blank"
                                            class="text-xs text-blue-600 hover:underline"
                                        >
                                            View on map
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
            
            <!-- Footer -->
            <div class="bg-gray-100 px-6 py-4 flex justify-end">
                <button 
                    type="button" 
                    class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" 
                    wire:click="$set('show', false)"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
