<div>
    <x-slot name="title">All Departments</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                Departments
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        @if($departments->isNotEmpty())
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($departments as $department)
                    <a href="{{ route('admin.dashboard.tenders', ['department' => $department->id]) }}" class="block">
                        <x-card no-padding overflow-hidden>
                            <div class="bg-gray-50 h-16 px-4 border-b border-gray-100 flex items-center justify-end">
                                <!-- Add any content here if needed -->
                            </div>
                            <div class="-mt-10 w-24 h-24 border rounded-full relative bg-white overflow-hidden mx-auto">
                                @if($department->photo_url)
                                    <img src="{{ $department->photo_url }}" alt="department-logo" loading="lazy" class="object-cover h-full w-full absolute">
                                @endif
                            </div>
                            <div class="p-5">
                                <x-heading size="xl" class="mb-2">{{ $department->name }}</x-heading>

                                <div class="flex">
                                    <div class="flex-1">
                                        Tenders: <strong>{{ $department->tenders_count }}</strong>
                                    </div>
                                </div>
                            </div>
                        </x-card>
                    </a>
                @endforeach
            </div>
        @else 
            <x-card-empty />
        @endif

        <livewire:departments.create />
    </x-section-centered>
</div>
