<div>
    <x-slot name="title">Tenders for {{ $department->name ?? '' }}</x-slot>

    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                All Tender Lists
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        @if ( !$tenders || $tenders->isEmpty())
            <x-card-empty />
        @else
            <x-card no-padding>
                <x-table.table>
                    <thead>
                        <tr>
                            <x-table.thead>Tender Title</x-table.thead>
                            <x-table.thead>Status</x-table.thead>
                            <x-table.thead>Published Date</x-table.thead>
                            <x-table.thead>Bid Starting Date</x-table.thead>
                            <x-table.thead>Tender Opening Date</x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($tenders as $tender)
                            <tr>
                                <x-table.tdata>{{ $tender->tender_title ?? '' }}</x-table.tdata>
                                <x-table.tdata>{{ $tender->tender_status ?? '' }}</x-table.tdata>
                                <x-table.tdata>{{ isset($tender->published_date) ? date('Y-m-d', strtotime($tender->published_date)) : '' }}</x-table.tdata>
                                <x-table.tdata>{{ isset($tender->bid_starting_date) ? date('Y-m-d', strtotime($tender->bid_starting_date)) : '' }}</x-table.tdata>
                                <x-table.tdata>{{ isset($tender->tender_opening_date) ? date('Y-m-d', strtotime($tender->tender_opening_date)) : '' }}</x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card>
            <div class="mt-5">{{ $tenders->links() }}</div> 
        @endif
    </x-section-centered>
    @once
        @push('alpinejs-scripts')
            <script defer src="https://unpkg.com/@alpinejs/collapse@3.9.0/dist/cdn.min.js"></script>
        @endpush
    @endonce
</div>
