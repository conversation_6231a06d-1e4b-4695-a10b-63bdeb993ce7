<div>
    <x-slot name="title">Tender</x-slot>
    
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('admin.tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item href="{{ route('admin.tenders.show', $tenderId) }}">Tender Details</x-breadcrumb-item>
                    <x-breadcrumb-item>Edit Tender</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>
{{-- 
            <x-slot name="action">
                <x-button tag="a" href="#add-review" with-icon icon="add">Add Review</x-button>
            </x-slot> --}}
        </x-navbar-top-transparent>
    </x-slot>
 
    <x-section-centered>
        @if ($this->showTimeline)
            <x-alertbox class="mb-4">
                <x-heading class="mb-2" size="lg">Tendering Creation Process Steps</x-heading>
                <div class="flex flex-col md:items-center md:flex-row space-y-1 md:space-y-0 md:space-x-3">

                    <div class="flex space-x-2 items-center">
                        <div class="shrink-0 p-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                            </svg>                      
                        </div>
                        <div class="text-gray-800">Tender Basic Details</div>
                    </div>

                    <div class="w-1 ml-3 md:ml-0 h-6 md:w-8 md:h-1 bg-gray-100 rounded-full"></div>

                    <div class="flex space-x-2 items-center">
                        <div class="shrink-0 p-1">
                            @if ($hasDocuments)
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>          
                            @else 
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-orange-500">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                            </svg>     
                            @endif
                        </div>
                        <div class="text-gray-800">Tender Documents</div>
                    </div>

                    <div class="w-1 ml-3 md:ml-0 h-6 md:w-8 md:h-1 bg-gray-100 rounded-full"></div>
                    <div class="flex space-x-2 items-center">
                        <div class="shrink-0 p-1">
                            @if ($hasTenderDocumentTypes)
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                            </svg>          
                            @else 
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-orange-500">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                            </svg>     
                            @endif                                      
                        </div>
                        <div class="text-gray-800">Tender Document to be Uploaded by Bidder</div>
                    </div>

                    <div class="w-1 ml-3 md:ml-0 h-6 md:w-8 md:h-1 bg-gray-100 rounded-full"></div>
                    <div class="flex space-x-2 items-center">
                        <div class="shrink-0 p-1">
                            @if ($hasTenderItems)
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>          
                            @else 
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-orange-500">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                            </svg>     
                            @endif                                          
                        </div>
                        <div class="text-gray-800">Tender BOQ</div>
                    </div>
                </div>
            </x-alertbox>
        @endif

        @if (! $this->showTimeline && $this->tenderIsPublished())
            <x-alert class="mb-4" variant="success" :close="false">
                The tendering creation process has been completed, and the <strong>checker</strong> user can now review it.
            </x-alert>
        @endif

        <x-card form-action="save" card-classes="mb-8">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Basic Details</x-slot>
                <x-slot name="description">
                    Add category, tender work, department, and so on.

                    <div class="mt-1">Last updated on: @dateWithTime($tenderUpdatedAt)</div>
                </x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <x-select label="Category" name="tender_category" wire:model.defer="tender_category">
                        <option value="">Select a category</option>
                        @foreach ($this->tenderCategories as $tenderCategory)
                            <option value="{{ $tenderCategory->value }}">{{ $tenderCategory->name }}</option>
                        @endforeach
                    </x-select>
                    
                    <x-select label="Tender Type" name="tender_type" wire:model.defer="tender_type">
                        <option value="">Select a type</option>
                        @foreach ($this->tenderTypes as $tenderType)
                            <option value="{{ $tenderType->value }}">{{ $tenderType->name }}</option>
                        @endforeach
                    </x-select>
                </div>
                
                <x-textarea
                    label="Title of Work" 
                    name="tender_title"
                    wire:model.defer="tender_title"
                />

                <x-input
                    label="Tender Called By" 
                    name="tender_called_by"
                    wire:model.defer="tender_called_by"
                />

                <x-input
                    label="Tender Number" 
                    name="tender_number"
                    wire:model.defer="tender_number"
                />

                <x-quilljs-editor
                    toolbar-type="minimal"
                    label="Description of work" 
                    name="tender_details"
                    :initial-value="$tender_details"
                    wire:model="tender_details"
                    placeholder="Description of work..."
                />

                <x-textarea
                    no-margin
                    label="Location of work"
                    name="tender_address"
                    wire:model.defer="tender_address"
                    placeholder="Address..."
                />
            </x-card-form>

            <x-section-border />

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Finance Details</x-slot>
                <x-slot name="description">Add finance details of the tender.</x-slot>
             
                <x-money
                    class="w-60"
                    label="Tender Cost" 
                    name="tender_cost"
                    wire:model.defer="tender_cost"
                />
                
                <x-money
                    class="w-60"
                    label="Estimated value of work" 
                    name="tender_value"
                    wire:model.defer="tender_value"
                />
                
                <x-textarea
                    rows="3"
                    no-margin
                    label="Tender EMD Details" 
                    name="tender_emd"
                    wire:model.defer="tender_emd"
                />
            </x-card-form>

            <x-section-border />

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Dates</x-slot>
                <x-slot name="description">Add important dates of the tender.</x-slot>

                <div>
                    <x-flatpicker
                        label="Published Date"
                        name="published_date"
                        wire:model.defer="published_date"
                    />

                    <x-flatpicker
                        label="Bid Start Date"
                        name="bid_starting_date"
                        wire:model.defer="bid_starting_date"
                    />

                    <x-flatpicker
                        label="Technical Bid Opening Date"
                        name="technicalbid_opening_date"
                        wire:model.defer="technicalbid_opening_date"
                    />

                    <x-flatpicker
                        :options="[
                            'enableTime' => true,
                        ]"
                        label="Last Date of Submission"
                        name="last_date_of_submission"
                        wire:model.defer="last_date_of_submission"
                    />

                    <x-flatpicker
                        label="Last Date of Document Sale"
                        name="last_date_of_document_sale"
                        wire:model.defer="last_date_of_document_sale"
                    />

                    <x-flatpicker
                        no-margin
                        label="Last Date for Clarification"
                        name="last_date_for_clarification"
                        wire:model.defer="last_date_for_clarification"
                    />
                </div>
            </x-card-form>

            <x-section-border />

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Pre Bid Date & Venue</x-slot>
                <x-slot name="description">Add pre bid date & venue of the tender.</x-slot>

                <x-textarea
                    optional
                    label="Pre Bid Meeting Venue"
                    name="pre_bid_meeting_venue"
                    wire:model.defer="pre_bid_meeting_venue"
                    placeholder="Address..."
                />
 
                <x-flatpicker
                    optional
                    label="Pre Bid Meeting Date"
                    name="pre_bid_meeting_date"
                    wire:model.defer="pre_bid_meeting_date"
                />
            </x-card-form>

            <x-slot name="footer" class="flex justify-end items-center">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button
                    with-spinner
                    wire:target="save"
                >Save Tender</x-button>
            </x-slot>
        </x-card>

        <div class="mb-8">
            <livewire:tenders.pre-bid-minutes-upload :tenderId="$tenderId" />
        </div>

        <div class="mb-8">
            <livewire:tenders.document-uploads :tenderId="$tenderId" />
        </div>

        <div class="mb-8">
            <livewire:tenders.document-types :tenderId="$tenderId" />
        </div>

        <div class="mb-8">
            <livewire:tenders.items :tenderId="$tenderId" />
        </div>
    </x-section-centered>
</div>
