<div>
    <x-slot name="title">All Tenders</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                Tenders
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        <x-card card-classes="mb-6">
            <div x-data="{ showFilter: false }" x-cloak>
                <div class="flex space-x-3">
                    <div class="flex-1">
                        <x-input-search 
                            no-margin
                            label="Search" 
                            name="search" 
                            wire:model.debounce.600ms="search" 
                            placeholder="Search by tender title / number"
                        />
                    </div>
                
                    <div class="pt-6">
                        <div class="space-x-2 items-center">
                            <x-button type="button" color="white" x-on:click="showFilter = !showFilter"><x-icon-filter class="w-4 h-4 mr-1 -ml-1" />Filter</x-button>
                            <x-button type="button" color="white" wire:click="resetFilter" wire:target="resetFilter" with-spinner><x-icon-refresh class="w-4 h-4 mr-1 -ml-1" />Reset all</x-button>
                        </div>
                    </div>
                </div>
                
                <div x-show="showFilter" x-collapse class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-x-4 sm:px-0">
                    <x-select 
                        label="Department" 
                        id="department" 
                        name="department" 
                        wire:model="department"
                    >
                        @if(count($this->departments) > 0)
                            <option value="all" selected>All</option>
                            @foreach ($this->departments as $departmentKey => $departmentName)
                                <option value="{{ $departmentName }}">{{ $departmentName }}</option>
                            @endforeach
                        @else
                            <option value="" disabled>No roles</option>
                        @endif
                    </x-select>
                </div>
            </div>
        </x-card>

        @if($tenders->isNotEmpty())
            <x-card no-padding>
                <x-table.table table-striped>
                    <thead>
                        <tr>
                            <x-table.thead>Tender Title</x-table.thead>
                            <x-table.thead>Department / Called By</x-table.thead>
                            <x-table.thead>Type / Category</x-table.thead>
                            <x-table.thead>Tender Number</x-table.thead>
                            <x-table.thead>Status</x-table.thead>
                            <x-table.thead>A/F</x-table.thead>
                            <x-table.thead>Published Date</x-table.thead>
                        </tr>
                    </thead>
                    <tbody wire:loading.class="opacity-25 base-spinner">
                        @foreach($tenders as $tender)
                            <tr class="even:bg-gray-50">
                                <x-table.tdata>
                                    <x-link class="inline-block w-96 whitespace-wrap leading-5" href="{{ route('admin.tenders.show', $tender->id) }}">{{ $tender->tender_title }}</x-link>
                                    <p class="mt-1 text-gray-500 text-sm leading-none">Added by: {{ $tender?->user?->name }} / {{ $tender?->user?->email }}</p>
                                </x-table.tdata>
                                <x-table.tdata>
                                    <p class="text-sm">{{ $tender?->department?->name }}</p> 
                                    <p class="text-sm">{{ $tender->tender_called_by }}</p>
                                </x-table.tdata>
                                <x-table.tdata>
                                    <p class="text-gray-500 text-sm leading-none">{{ $tender->tender_category }} / {{ $tender->tender_type }}</p>
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $tender->tender_number }}
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    <x-badge variant="{{ $tender->tender_status->color() }}">{{ $tender->tender_status }}</x-badge>
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    {{ $tender->biddings_count }}
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    @dateWithTime($tender->published_date)
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card>

            <div class="mt-5">{{ $tenders->links() }}</div>
        @else 
            <x-card-empty />
        @endif
    </x-section-centered>

    @once
        @push('alpinejs-scripts')
            <script defer src="https://unpkg.com/@alpinejs/collapse@3.9.0/dist/cdn.min.js"></script>
        @endpush
    @endonce
</div>
