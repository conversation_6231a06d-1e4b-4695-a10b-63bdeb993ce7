<div>
    <x-slot name="title">Companies</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                All company lists
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>    
        @if($users->isNotEmpty())
            <x-card no-padding>
                <div class="p-4 border-b border-gray-200">
                    <x-input-search 
                        no-margin 
                        name="search" 
                        wire:model.debounce.600ms="search" 
                        placeholder="Search by name / email"
                    />
                </div>
                <x-table.table>
                    <thead>
                        <tr>
                            <x-table.thead>Company Name</x-table.thead>
                            <x-table.thead>Company Type</x-table.thead>
                            <x-table.thead>NCHAC Reg. Number</x-table.thead>
                            <x-table.thead>GST Number</x-table.thead>
                            <x-table.thead>Created at</x-table.thead>
                            <x-table.thead>Last Online</x-table.thead>
                            {{-- <x-table.thead></x-table.thead> --}}
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <x-table.tdata>
                                    <x-media>
                                        <x-slot name="mediaObject">
                                            <div class="rounded w-10 h-10 bg-slate-100">
                                                <img src="{{ $user->photo_url }}" alt="avatar" class="rounded object-fit h-10" loading="lazy">
                                            </div>
                                        </x-slot>
                                        <x-slot name="mediaBody">
                                            {{ Str::title($user->name) }}
                                            <p class="text-gray-500 text-sm leading-none">{{ $user->email }}</p>
                                            <p class="text-gray-500 text-sm leading-6">{{ $user?->companyProfile?->address }}</p>
                                        </x-slot>
                                    </x-media>
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $user?->companyProfile?->type }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $user?->companyProfile?->nchac_registration_no }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $user?->companyProfile?->gst_number }}
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    @date($user->created_at)
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    @date($user->last_online_at)
                                </x-table.tdata>
                                {{-- <x-table.tdata class="w-20">
                                    <x-button-actions 
                                        :permissions="$user->user_permissions"
                                        show="{{ route('users.edit', $user->id) }}" 
                                        edit="{{ route('users.edit', $user->id) }}"
                                        delete="$wire.emitTo(
                                            'users.delete',
                                            'showDeleteModal',
                                            '{{ $user->id }}',
                                            'Confirm Deletion',
                                            'Are you sure you want to remove the user: <br> <strong>{{ $user->name }}</strong>?'
                                        )"
                                    />
                                </x-table.tdata> --}}
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card>

            <div class="mt-5">{{ $users->links() }}</div>
        @else 
            <x-card-empty />
        @endif
    </x-section-centered>
</div>
