<div>
    <x-slot name="title">Users</x-slot>
    
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('users') }}">All users</x-breadcrumb-item>
                    <x-breadcrumb-item>Create New User</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>
 
    <x-section-centered>
        <x-card form-action="save">
            <x-card-form class="shadow-none" no-padding>
                <x-slot name="title">Basic Details</x-slot>
                <x-slot name="description">Add name, department, with role maker or checker and so on.</x-slot>
 
                <div class="md:w-2/3">
                    <x-input
                        label="Name" 
                        name="name"
                        wire:model.defer="name"
                    />

                    <x-select label="Department" name="department" wire:model.defer="department">
                        <option value="">Select a department</option>
                        @foreach ($this->departments as $departmentKey => $departmentValue)
                            <option value="{{ $departmentKey }}">{{ $departmentValue }}</option>
                        @endforeach
                    </x-select>
                
                    <x-radio-group 
                        label="Select a role"
                        name="role"
                        wire:model.defer="role"
                        default-value="{{ $this->roles[0]['value'] ?? '' }}"
                        :options="$this->roles"
                    />
    
                    <x-radio-pill 
                        label="Gender"
                        name="gender"
                        wire:model.defer="gender"
                        class="md:grid-cols-4"
                        default-value="male"
                        :options="[
                            [
                                'label' => 'Male',
                                'value' => 'male'
                            ],
                            [
                                'label' => 'Female',
                                'value' => 'female'
                            ],
                            [
                                'label' => 'Other',
                                'value' => 'other'
                            ]
                        ]"
                    />
    
                    <x-input-number
                        no-margin
                        input-mode="numeric"
                        label="Phone" 
                        id="phone" 
                        name="phone" 
                        wire:model.defer="phone"
                        maxlength="10"
                        minlength="10"
                        hint="Please enter a valid 10 digit mobile number"
                    />
                </div>
            </x-card-form>

            <x-section-border />

            <x-card-form class="shadow-none" no-padding>
                <x-slot name="title">Login Details</x-slot>
                <x-slot name="description">Login credentials for the user</x-slot>

                <div class="md:w-2/3">

                    <x-input
                        type="email"
                        label="Email" 
                        name="email"
                        wire:model.defer="email" 
                    />
    
                    <x-input-password
                        label="Password" 
                        id="password" 
                        name="password" 
                        wire:model.defer="password"
                        optional
                        hint="If no password is given default password 'secret' will be generated"
                    />
                </div>
            </x-card-form>
       
            <x-slot name="footer" class="text-right">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button
                    color="black"
                    with-spinner
                    wire:target="save"
                >Save User</x-button>
            </x-slot>
        </x-card>
       
    </x-section-centered>
</div>
