<div>
    <x-slot name="title">All users</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                Users
            </x-slot>

            <x-slot name="action">
                <x-button tag="a" href="{{ route('users.create') }}" with-icon icon="add">New User</x-button>    
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        @if($stats)
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                @foreach(collect($stats) as $key => $value)
                    <div>
                        <x-card-stats>
                            <x-slot name="title">{{ Str::headline($key) }} Users</x-slot>
                            <x-heading>{{ $value }}</x-heading>
                        </x-card-stats>
                    </div>
                @endforeach
            </div>
        @endif
    
        <x-card card-classes="mb-6">
            <div x-data="{ showFilter: false }" x-cloak>
                <div class="flex space-x-3">
                    <div class="flex-1">
                        <x-input-search 
                            no-margin
                            label="Search" 
                            name="search" 
                            wire:model.debounce.600ms="search" 
                            placeholder="Search by name / email"
                        />
                    </div>
                
                    <div class="pt-6">
                        <div class="space-x-2 items-center">
                            <x-button type="button" color="white" x-on:click="showFilter = !showFilter"><x-icon-filter class="w-4 h-4 mr-1 -ml-1" />Filter</x-button>
                            <x-button type="button" color="white" wire:click="resetFilter" wire:target="resetFilter" with-spinner><x-icon-refresh class="w-4 h-4 mr-1 -ml-1" />Reset all</x-button>
                        </div>
                    </div>
                </div>

                <div x-show="showFilter" x-collapse class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-x-4 sm:px-0">
                    <x-select 
                        label="Role" 
                        id="role" 
                        name="role" 
                        wire:model="role"
                    >
                        @if(count($this->roles) > 0)
                            <option value="all" selected>All</option>
                            @foreach ($this->roles as $roleKey => $roleName)
                                <option value="{{ $roleKey }}">{{ $roleName }}</option>
                            @endforeach
                        @else
                            <option value="" disabled>No roles</option>
                        @endif
                    </x-select>
                </div>
            </div>
        </x-card>

        @if($users->isNotEmpty())
            <x-card no-padding>
                <x-table.table>
                    <thead>
                        <tr>
                            <x-table.thead>Name</x-table.thead>
                            <x-table.thead>Department</x-table.thead>
                            <x-table.thead>Role</x-table.thead>
                            <x-table.thead>Created at</x-table.thead>
                            <x-table.thead>Last Online</x-table.thead>
                            <x-table.thead></x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <x-table.tdata>
                                    <x-media>
                                        <x-slot name="mediaObject">
                                            <div class="rounded w-10 h-10 bg-slate-100">
                                                <img src="{{ $user->photo_url }}" alt="avatar" class="rounded object-fit h-10" loading="lazy">
                                            </div>
                                        </x-slot>
                                        <x-slot name="mediaBody">
                                            <x-link href="{{ route('users.edit', $user->id) }}">{{ Str::title($user->name) }}</x-link>
                                            <p class="text-gray-500 text-sm leading-none">{{ $user->email }}</p>
                                        </x-slot>
                                    </x-media>
                                </x-table.tdata>
                                <x-table.tdata class="w-64">
                                    {{ $user?->department?->name }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    <x-badge variant="{{ $user->role_color }}">{{ $user->role }}</x-badge>
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    @date($user->created_at)
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    @date($user->last_online_at)
                                </x-table.tdata>
                                <x-table.tdata class="w-20">
                                    <x-button-actions 
                                        :permissions="$user->user_permissions"
                                        show="{{ route('users.edit', $user->id) }}" 
                                        edit="{{ route('users.edit', $user->id) }}"
                                        delete="$wire.emitTo(
                                            'users.delete',
                                            'showDeleteModal',
                                            '{{ $user->id }}',
                                            'Confirm Deletion',
                                            'Are you sure you want to remove the user: <br> <strong>{{ $user->name }}</strong>?'
                                        )"
                                    />
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card>

            <div class="mt-5">{{ $users->links() }}</div>
        @else 
            <x-card-empty />
        @endif

        @can('delete', App\Models\User::class)
            <livewire:users.delete />
        @endcan
    </x-section-centered>

    @once
        @push('alpinejs-scripts')
            <script defer src="https://unpkg.com/@alpinejs/collapse@3.9.0/dist/cdn.min.js"></script>
        @endpush
    @endonce
</div>
