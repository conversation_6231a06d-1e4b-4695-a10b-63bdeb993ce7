<div>
    <x-dialog-modal wire:model="show" id="document-review-modal" form-action="save">

        <x-slot name="title">Review Tender Document</x-slot>

        <x-slot name="content">
            <div class="mb-5">
                <x-table.table :with-shadow="false" table-bordered table-condensed>
                    <tbody>
                        <tr>
                            <x-table.tdata class="border-t-0">Tender Ref. No.</x-table.tdata>
                            <x-table.tdata class="border-t-0"><strong>{{ $tenderNumber }}</strong></x-table.tdata>
                        </tr>
                        <tr>
                            <x-table.tdata class="border-t-0">Tender ID.</x-table.tdata>
                            <x-table.tdata class="border-t-0"><strong>{{ $tenderUin }}</strong></x-table.tdata>
                        </tr>
                        <tr>
                            <x-table.tdata>Company Name</x-table.tdata>
                            <x-table.tdata><strong>{{ $companyName }}</strong></x-table.tdata>
                        </tr>
                    </tbody>
                </x-table.table>
            </div>
           
            <x-textarea-simple
                label="Review"
                name="review"
                wire:model.defer="review"
            />

            <div class="w-1/2">
                <x-select label="Status" id="status" name="status" wire:model.defer="status">
                    <option value="">Select status</option>
                    <option value="accept">Accept</option>
                    <option value="reject">Reject</option>
                </x-select>
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-button color="white" class="mr-2" type="button" wire:click.prevent="closeModal()">Cancel</x-button>
            <x-button type="submit" with-spinner wire:target="save">Add Review</x-button>
        </x-slot>      
    </x-dialog-modal>
</div>
