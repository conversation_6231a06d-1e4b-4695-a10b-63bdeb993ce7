<div>
    <x-card>
        <x-card-form :with-shadow="false" class="overflow-hidden" no-padding>
            <x-slot name="title">Tender Document to be Uploaded by <PERSON><PERSON><PERSON></x-slot>
            <x-slot name="description">Add the document types required for the tendering process.</x-slot>
 
            @if ($tenderDocumentTypes)
                <div class="mb-4">
                    <x-table.table table-bordered :with-shadow="false">
                        <thead>
                            <tr>
                                <x-table.thead class="py-1.5">Document Type</x-table.thead>
                                <x-table.thead class="py-1.5"></x-table.thead>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tenderDocumentTypes as $tenderDocumentType)
                                <tr>
                                    <x-table.tdata>
                                        {{ $tenderDocumentType['documentName'] }}
                                        <div class="text-sm text-gray-500">{{ $tenderDocumentType['type'] }} &bull; {{ $tenderDocumentType['size'] }}</div>
                                    </x-table.tdata>
                                    <x-table.tdata class="w-1/5">
                                        <a href="#" 
                                            onclick="confirm('Are you sure you want to remove the item?') || event.stopImmediatePropagation()"
                                            wire:click.prevent="deleteDocumentType('{{ $tenderDocumentType["id"] }}')"
                                            class="text-red-600 hover:underline font-medium">Delete</a>
                                    </x-table.tdata>
                                </tr>
                            @endforeach
                        </tbody>
                    </x-table.table>
                </div>
            @endif
        </x-card-form>

        @if (! $tenderDocumentTypes)
            <x-card-empty class="shadow-none">No tender document types added yet.</x-card-empty>
        @endif

        <x-slot name="footer">
            <form wire:submit.prevent="addDocument">    
                <div class="md:grid md:grid-cols-8 md:gap-4">
                    <div class="md:col-span-5">
                        <x-input 
                            label="Document Type"
                            name="documentName"
                            wire:model.defer="documentName"
                            placeholder="eg. PAN Card / Trade Licence / CA Certificate / ISO Certificate"
                        />
                    </div>
    
                    <x-select 
                        label="File Type"
                        name="type"
                        wire:model.defer="type"
                    >
                        <option value="application/pdf">PDF</option>
                    </x-select>
    
                    <x-select 
                        label="Max File Size"
                        name="size"
                        wire:model.defer="size"
                    >
                        <option value="2MB">2MB</option>
                        <option value="5MB">5MB</option>
                        <option value="8MB">8MB</option>
                    </x-select>
     
                    <div>
                        <x-label class="mb-1 hidden md:block">&nbsp;</x-label>
                        <x-button type="submit" color="white" class="w-full text-indigo-600" with-spinner wire:target="addDocument" with-icon icon="add">Add</x-button>
                    </div>
                </div>
            </form>
        </x-slot>
    </x-card>
</div>
