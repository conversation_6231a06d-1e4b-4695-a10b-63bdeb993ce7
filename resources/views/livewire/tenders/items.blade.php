<div>
    <x-card>
        <x-heading size="lg" class="mb-2 font-bold">Tender BOQ</x-heading>
        @if ($items->isNotEmpty())
            <div class="mb-5 overflow-x-auto">
                <x-table.table table-bordered :with-shadow="false">
                    <thead>
                        <tr>
                            <x-table.thead class="py-1.5">Item/Work Description</x-table.thead>
                            <x-table.thead class="py-1.5 hidden md:table-cell">Est. price</x-table.thead>
                            <x-table.thead class="py-1.5 hidden md:table-cell">Quantity</x-table.thead>
                            <x-table.thead class="py-1.5 hidden md:table-cell">Unit</x-table.thead>
                            <x-table.thead class="py-1.5"></x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($items as $item)
                            <tr>
                                <x-table.tdata>
                                    {{ $item->item_type }}
                                    <div class="text-sm text-gray-500 flex space-x-2 md:hidden">
                                        <span>Quantity: {{ $item->quantity }}</span>
                                        <span>Unit: {{ $item->unit }}</span>
                                        <span>Est. Price: {{ $item->estimated_price }}</span>
                                    </div>
                                </x-table.tdata>
                                <x-table.tdata class="hidden md:table-cell">
                                    {{ $item->estimated_price }}
                                </x-table.tdata>
                                <x-table.tdata class="hidden md:table-cell">
                                    {{ $item->quantity }}
                                </x-table.tdata>
                                <x-table.tdata class="hidden md:table-cell">
                                    {{ $item->unit }}
                                </x-table.tdata>
                                <x-table.tdata class="w-1/12">
                                    <a href="#" 
                                        onclick="confirm('Are you sure you want to remove the item?') || event.stopImmediatePropagation()"
                                        wire:click.prevent="deleteItemType('{{ $item->id }}')"
                                        class="text-red-600 hover:underline font-medium">Delete</a>
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </div>
        @else
            <x-card-empty class="shadow-none">No tender items added yet.</x-card-empty>
        @endif

        <x-slot name="footer">
            <form wire:submit.prevent="addItem">    
                <div class="md:grid md:grid-cols-8 md:gap-4">
                    <div class="md:col-span-4">
                        <x-input 
                            label="Item/Work Description"
                            name="item_type"
                            wire:model.defer="item_type"
                        />
                    </div>
    
                    <x-input 
                        label="Estimated Price"
                        name="estimated_price"
                        wire:model.defer="estimated_price"
                    />

                    <x-input 
                        label="Quantity"
                        name="quantity"
                        wire:model.defer="quantity"
                    />
    
                    <x-select 
                        label="Unit"
                        name="unit"
                        wire:model.defer="unit"
                    >
                        <option value="nos">Nos</option>
                        <option value="jobs">Jobs</option>
                    </x-select>
    
                    <div>
                        <x-label class="mb-1 hidden md:block">&nbsp;</x-label>
                        <x-button type="submit" color="white" class="w-full text-indigo-600" with-spinner wire:target="addItem" with-icon icon="add">Add item</x-button>
                    </div>
                </div>
            </form>
        </x-slot>
    </x-card>
</div>
