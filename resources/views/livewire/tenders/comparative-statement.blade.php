<div>
    @if (!is_null($this->tender->comparative_statement))
        <x-card card-classes="mt-6">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Comparative Statement</x-slot>

                <div class="text-right">

                    <x-button tag="a" target="_blank" href="{{ $this->tender->comparative_statement_url }}">Download
                    </x-button>
                </div>

            </x-card-form>
        </x-card>
    @else
        <x-card form-action="save" card-classes="mt-6">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Upload Comparative Statement</x-slot>
                <x-slot name="description"></x-slot>


                <x-filepond label="Comparative Statement" id="comparativeStatement" name="comparativeStatement"
                    wire:model.defer="comparativeStatement" accept-files="application/pdf" max-file-size="8MB" />
            </x-card-form>


            <x-slot name="footer" class="flex items-center justify-end">
                <x-button type="submit" with-spinner>Save</x-button>
            </x-slot>
        </x-card>
    @endif
</div>
