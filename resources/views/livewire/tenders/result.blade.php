<div>
    <x-slot name="title">Add Tender Signature</x-slot>

    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item>Sign</x-breadcrumb-item>
                </x-breadcrumb>
    </x-slot>
    </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        <x-card form-action="save">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Basic Details</x-slot>
                <x-slot name="description">
                    For Windows:
                    <ol class="list-decimal list-inside">
                        <li>Open the start menu</li>
                        <li>Type windows+R or open "Run"</li>
                        <li>Execute the following command:
                            <span class="block p-1 rounded bg-gray-50 font-mono text-pink-600 text-xs">chrome.exe
                                --user-data-dir="C://Chrome dev session"
                                --disable-web-security</span>
                        </li>
                    </ol>

                </x-slot>

                <x-select label="Company" name="company" wire:model.defer="company">
                    <option value="" selected>Select a company</option>
                    @foreach ($this->companies as $companyKey => $companyValue)
                        <option value="{{ $companyKey }}">{{ $companyValue }}</option>
                    @endforeach
                </x-select>

                <x-textarea-simple optional rows="4" label="Message" name="message" wire:model.defer="message" />

                <x-filepond label="Upload AOC Document in PDF Format" id="aocDocument" name="aocDocument"
                    wire:model.defer="aocDocument" accept-files="application/pdf" max-file-size="8MB" />

                <div class="card">
                    <div class="card-header bg-info">
                        <h3 class="card-title">Sign Certificate</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body p-10">
                        <form id="pdfForm">
                            <input type="hidden" id="signingReason" name="signingReason" value="Dhtender"
                                maxlength="20" />
                            <input type="hidden" id="signingLocation" name="signingLocation" value="Dima Hasao"
                                maxlength="20" />
                            <input type="hidden" id="stampingX" name="stampingX" maxlength="20" value="410" />
                            <input type="hidden" id="stampingY" name="stampingY" maxlength="20" value="400" />
                            <input type="hidden" id="scale" name="scale" maxlength="20" value="0.15f" />
                            <input type="hidden" id="pdfData" name="pdfData" value="<?= $base64 ?>" />
                            <textarea id="imageData" cols="60" rows="8" readonly="readonly" style="display:none;"><?php echo $img_content; ?></textarea>
                            <input type="hidden" id="tsaURL" name="tsaURL" value="" maxlength="100" />
                            <input type="hidden" id="timeServerURL" name="timeServerURL"
                                value="https://rurban.gov.in/dscapi/getServerTime" class="form-control" />
                            <div class="row clearfix">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="hidden" id="kid" value="<?= $tid ?>" />
                                        {{-- <input type="text" name="fName"
                                                value="{{ $tender_number }}"
                                                class="form-control" disabled /> --}}
                                    </div>
                                </div>
                                {{-- <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="path">Document Path <span class="text-danger">*</span></label>
                                        <input type="text" id="kpath" name="doc_path" value="<?= $aoc ?>"
                                            class="form-control" disabled />
                                    </div>
                                </div> --}}

                                {{-- <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mob">Tender Title <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" name="mobile"
                                                value="<?= $tender_title ?>"
                                                class="form-control" disabled />
                                        </div>
                                    </div> --}}
                                {{-- <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Verification Response:</label>
                                        <textarea id="verificationResponse" cols="60" rows="8" disabled class="form-control"></textarea>
                                    </div>
                                </div> --}}
                            </div>
                    </div>
                    <div class="card-footer">
                        <input id="signPdf" type="button" value="Sign PDF " class="btn btn-success">&nbsp;
                        <?php echo '<a href="' . $aoc . '" download class="btn btn-info"><span class="fa fa-download"></span> Download</a>'; ?>&nbsp;
                        <a href="dashboard" class="btn btn-warning text-white"><i class="fa fa-times"></i> Cancel</a>

                        &nbsp;<a id="downloadDiv" href='#' type="application/pdf"
                            download="<?= $tid . '.pdf' ?>"></a>
                        &nbsp;<input id="verifyPdfBtn" type="button" value="Verify Pdf" class="btn btn-danger"><br />
                    </div>
                    </form>
                    <div class="col-md-4" style="display: none;">
                        <div class="well-md">
                            <label for="signedPdfData">Signed Pdf (Base64):</label> <br />
                            <textarea id="signedPdfData" cols="60" rows="8"></textarea>
                            <textarea id="sdfsdPdfData" cols="60" rows="8"></textarea>
                            <br /> <label>Encryption Key:</label>
                            <textarea id="lblEncryptedKey" cols="60" rows="4" disabled></textarea>
                            <br />
                        </div>
                    </div>
                </div>
                <div id="panel" class="panel"></div>

            </x-card-form>

            <x-slot name="footer" class="flex justify-end items-center">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button with-spinner wire:target="save">Save Tender</x-button>
            </x-slot>
        </x-card>

    </x-section-centered>


    @push('scripts-footer')
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
        <script src="{{ asset('js/dsc/dscapi-conf.js') }}"></script>
        <script src="{{ asset('js/dsc/dsc-signer_nchac.js') }}"></script>

        <script type="text/javascript">
            function myFunction() {
                var x = document.getElementById("tsaurls").value;
                if (x != 0) {
                    document.getElementById("tsaURL").value = x;
                } else {
                    document.getElementById("tsaURL").value = "";
                }
            }
            $(document).ready(function() {
                $('#verifyPdfBtn').hide();
                var initConfig = {
                    "preSignCallback": function() {
                        return true;
                    },
                    "postSignCallback": function(alias, sign, key) {
                        $('#signedPdfData').val(sign);
                        $('#lblEncryptedKey').val(key);

                        // Implement signed pdf upload and pdf Download here
                        var requestData = {
                            action: "DECRYPT",
                            en_sig: sign,
                            ek: key
                        };
                        $.ajax({
                                url: dscapibaseurl + "/pdfsignature",
                                type: "post",
                                dataType: "json",
                                contentType: 'application/json',
                                data: JSON.stringify(requestData),
                                async: false
                            })
                            .done(
                                function(data) {
                                    if (data.status_cd == 1) {
                                        //get data.data -> decode base64 -> get json->check status == SUCCESS
                                        //get data.data.sig -> add pdf header and append to link
                                        var jsonData = JSON.parse(atob(data.data));
                                        console.log(jsonData);
                                        if (jsonData.status === "SUCCESS") {
                                            $('#verifyPdfBtn').show();
                                            //Set Class to download link
                                            $('#downloadDiv').addClass('btn btn-info');
                                            // var dlnk = document.getElementById('downloadDiv');
                                            //get pdf data
                                            var pdfData = jsonData.sig;
                                            $('#sdfsdPdfData').val(pdfData);
                                            // alert(pdfData);
                                            var dlnk = document.getElementById('downloadDiv');
                                            var downloadLink = 'data:application/pdf;base64,' + pdfData;
                                            dlnk.href = 'data:application/pdf;base64,' + pdfData;
                                            // console.log(pdfData);
                                            @this.set('signedAocDocument', pdfData);
                                            $("#downloadDiv").text("Download Signed PDF File");
                                            $('#signPdf').hide();
                                            khasnu();
                                        }
                                    } else {
                                        if (data.error.error_cd == 1002) {
                                            alert(data.error.message);
                                            return false;
                                        } else {
                                            alert("Decryption Failed for Signed PDF File");
                                            return false;
                                        }
                                    }
                                }).fail(
                                function(jqXHR, textStatus,
                                    errorThrown) {
                                    alert(textStatus);
                                });
                    },
                    signType: 'pdf',
                    mode: 'stamping'
                };
                dscSigner.configure(initConfig);
                $('#signPdf').click(function() {
                    var data = $("#pdfData").val();
                    if (data != null || data != '') {
                        dscSigner.sign(data);
                    }
                });
                $('#verifyPdfBtn')
                    .click(function() {
                        var signedPdfData = $('#signedPdfData').val();
                        var key = $('#lblEncryptedKey').val();
                        // Implement Verify here
                        var requestData = {
                            action: "VERIFY",
                            en_sig: signedPdfData,
                            ek: key
                        };
                        $.ajax({
                                url: dscapibaseurl + "/pdfsignature",
                                type: "post",
                                dataType: "json",
                                contentType: 'application/json',
                                data: JSON.stringify(requestData),
                                async: false
                            })
                            .done(
                                function(data) {
                                    if (data.status_cd == 1) {
                                        //get pdfSignatureVerificationResponse
                                        $(
                                            '#verificationResponse').val(atob(data.data));
                                    } else {
                                        alert("Verification Failed");
                                    }

                                })
                            .fail(
                                function(
                                    jqXHR,
                                    textStatus,
                                    errorThrown) {
                                    alert(textStatus);
                                });
                    });

            });

            function khasnu() {
                var id = document.getElementById("kid").value;
                var basefd = document.getElementById("downloadDiv").href;
                //alert(basefd);
                $.ajax({
                    url: 'upload.php',
                    type: 'post',
                    data: {
                        basefd: basefd,
                        id: id
                    },
                    success: function(res) {
                        alert(res);
                        if (res != 0) {
                            alert("AOC successfully signed");
                        } else {
                            alert('File not uploaded');
                        }
                    }
                });
            }
        </script>
    @endpush
</div>
