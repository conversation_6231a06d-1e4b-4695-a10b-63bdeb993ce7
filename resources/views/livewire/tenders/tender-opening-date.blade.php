<div>
    <x-dialog-modal wire:model="show" id="tender-opening-modal" form-action="save">

        <x-slot name="title">Bid Opening Date &amp; Time</x-slot>

        <x-slot name="content">
            <x-flatpicker
                label="Date & Time"
                name="tenderOpeningDate"
                wire:model="tenderOpeningDate"
                :options="[
                    'enableTime' => true,
                    'dateFormat' => 'Y-m-d h:i K',
                    'altFormat' => 'd-m-Y h:i K',
                ]"
            />
    
            <x-input
                optional
                label="Venue"
                name="venue"
                wire:model.defer="venue"
            />
        </x-slot>

        <x-slot name="footer">
            <x-button color="white" class="mr-2" type="button" wire:click.prevent="closeModal()">Cancel</x-button>
            <x-button type="submit" with-spinner wire:target="save">Save</x-button>
        </x-slot>      
    </x-dialog-modal>
</div>
