<div>
    <x-slot name="title">Tender</x-slot>

    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item href="{{ route('tenders.show', $tenderId) }}">Tender: {{ $tenderNumber }}
                    </x-breadcrumb-item>
                    <x-breadcrumb-item>Biddings</x-breadcrumb-item>
                </x-breadcrumb>
    </x-slot>
    </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        @if ($tender->tender_document_opened_at)
            <x-card card-classes="mb-6" no-padding overflow-hidden>
                <x-slot name="header">
                    <x-heading size="xl">Tender Documents</x-heading>
                </x-slot>

                <x-slot name="subheading">
                    <p class="text-gray-600">Documents submitted by the companies for the technical specification.</p>
                </x-slot>

                <x-slot name="action" class="pt-3 text-sm">
                    @if ($tender->document_reviews_exists && $tender->bidding_prices_exists && is_null($tender->tender_opened_at))
                        <x-link href="#" x-data="{}"
                            x-on:click.prevent="livewire.emitTo('tenders.tender-opening-date', 'show')">
                            <x-icon-add class="w-5 h-5 mr-1" />Add Tender Opening Date
                        </x-link>
                    @endif
                </x-slot>

                @if ($biddings->isNotEmpty())
                    <x-table.table :with-shadow="false" :rounded="false" table-sticky-first-column>
                        <thead>
                            <tr>
                                <x-table.thead>Company</x-table.thead>
                                <x-table.thead>Documents</x-table.thead>
                                <x-table.thead>Emd Challan</x-table.thead>
                                <x-table.thead>Status</x-table.thead>
                                <x-table.thead>Review</x-table.thead>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($biddings as $bidding)
                                <tr>
                                    <x-table.tdata>
                                        <x-heading size="md">{{ $bidding->company_name }}</x-heading>
                                        <p class="text-sm">{{ $bidding->company_email }}</p>
                                        <p class="text-sm">{{ $bidding->company_phone }}</p>
                                    </x-table.tdata>

                                    <x-table.tdata>
                                        @if ($bidding->biddingDocuments->isNotEmpty())
                                            @foreach ($bidding->biddingDocuments as $document)
                                                <div>
                                                    <x-link class="text-sm" href="{{ $document->document_path_url }}">
                                                        <x-icon-download class="w-4 h-4 mr-1" />
                                                        {{ $document->document_type }}
                                                    </x-link>
                                                </div>
                                            @endforeach
                                        @else
                                            <p class="text-sm">No documents uploaded</p>
                                        @endif
                                    </x-table.tdata>

                                    <x-table.tdata>
                                        @if ($bidding->emdPayment && $bidding->emdPayment->photo_url)
                                            <x-link class="text-sm" download
                                                href="{{ $bidding->emdPayment->photo_url }}">
                                                <x-icon-download class="w-4 h-4 mr-1" />Download
                                            </x-link>
                                        @endif
                                    </x-table.tdata>

                                    <x-table.tdata>
                                        @if ($bidding->documentReview)
                                            <x-badge variant="{{ $bidding?->documentReview?->status_color }}">
                                                {{ $bidding?->documentReview?->review_status }}</x-badge>
                                        @else
                                            N/A
                                        @endif
                                    </x-table.tdata>

                                    <x-table.tdata>
                                        @if ($bidding->documentReview)
                                            {{ $bidding?->documentReview?->review }}
                                        @else
                                            <x-button color="white" type="button" class="py-1"
                                                x-data="{}"
                                                x-on:click.prevent="window.livewire.emitTo('tenders.document-review', 'show', '{{ $bidding->id }}', '{{ $bidding->company_name }}')">
                                                Review</x-button>
                                        @endif
                                    </x-table.tdata>
                                </tr>
                            @endforeach
                        </tbody>
                    </x-table.table>
                @else
                    <x-card-empty class="shadow-none">
                        <p class="text-gray-600">No tender documents uploaded yet.</p>
                    </x-card-empty>
                @endif
            </x-card>
        @else
            <x-card card-classes="text-center">
                <div class="py-5">
                    <p class="mb-3 max-w-sm mx-auto">To open the tender document, click the button below. The concerned
                        official will receive an OTP.</p>
                    <x-button type="button" x-data="{}"
                        x-on:click.prevent="livewire.emitTo('tenders.open-technical-document', 'show')">Open Technical
                        Document</x-button>
                </div>
            </x-card>
        @endif

        @if ($tender->tender_opened_at && $tender->tender_document_opened_at)
            <x-card no-padding>
                <x-slot name="header">
                    <x-heading size="xl">Financial Bidding</x-heading>
                    <p class="text-gray-500 text-sm mb-2 md:mb-0">Opened on:
                        {{ $tender->tender_opened_at->format('d-m-Y H:i a') }}</p>

                    <x-slot name="action" class="space-x-2 items-center">
                        @if (!$tender->result_exists)
                            <x-button tag="a" href="{{ route('tenders.sign', $tenderId) }}" with-icon
                                icon="add" color="white" class="text-cyan-600">Add Sign</x-button>
                        @endif


                        <x-button type="button" x-data="{}" x-on:click.prevent="$wire.download()" with-icon
                            icon="download" color="white" class="text-cyan-600">Download CSV</x-button>
                    </x-slot>
                </x-slot>

                @if (count($biddingHeaders))
                    <x-table.table :with-shadow="false" table-bordered-full :rounded="false">
                        <thead>
                            <tr>
                                @foreach ($biddingHeaders as $biddingHeader)
                                    <x-table.thead>
                                        <x-heading size="md">{{ $biddingHeader }}</x-heading>
                                    </x-table.thead>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($this->acceptedTenderBiddings as $biddings)
                                <tr>
                                    @foreach ($biddings as $bidding)
                                        <x-table.tdata>
                                            {{ $bidding }}
                                        </x-table.tdata>
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </x-table.table>
                @else
                    <div class="p-4 text-center">No biddings yet.</div>
                @endif
            </x-card>
        @else
            @if ($tender->tender_opening_date)
                <x-card card-classes="text-center mt-6">
                    @if ($this->isTenderOpeningDateToday())
                        <div class="py-5">
                            <p class="mb-3 max-w-sm mx-auto">To open the tender, click the button below. The concerned
                                official will receive an OTP.</p>
                            <x-button type="button" x-data="{}"
                                x-on:click.prevent="livewire.emitTo('tenders.open-tender', 'showOpenTenderModal')">Open
                                Financial Document</x-button>
                        </div>
                    @else
                        <x-card-empty class="shadow-none" icon="file-lock">
                            <p>The opening of the bids is scheduled for:</p>
                            <x-heading>
                                <span x-data="{ date: new Date($el.innerText) }" x-text="date.toLocaleString('en-IN')"
                                    x-cloak>{{ $tender->tender_opening_date }}</span>
                            </x-heading>
                        </x-card-empty>
                    @endif
                </x-card>
            @else
                <x-card>
                    No tender opening date found or given.
                </x-card>
            @endif
        @endif

        @if ($tender->result_exists)
            <x-card card-classes="mt-6" no-padding overflow-hidden>
                <x-slot name="header">
                    <x-heading size="xl">Tender Result</x-heading>
                </x-slot>

                <x-table.table :with-shadow="false" :rounded="false">
                    <thead>
                        <tr>
                            <x-table.thead>Company Details</x-table.thead>
                            <x-table.thead>AOC Document</x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <x-table.tdata>
                                <x-heading size="md">{{ $tender?->result?->bidding?->company_name }}</x-heading>
                                {{ $tender?->result?->bidding?->company_email }} <br>
                                {{ $tender?->result?->bidding?->company_phone }}
                            </x-table.tdata>
                            <x-table.tdata>
                                <x-link href="{{ $tender?->result?->aoc_document_url }}">Download</x-link>
                            </x-table.tdata>
                        </tr>
                    </tbody>
                </x-table.table>
            </x-card>
        @endif

        <livewire:tenders.tender-opening-date :tender-id="$tender->id" />
        <livewire:tenders.document-review :tender-id="$tender->id" :tender-number="$tender->tender_number" :tender-uin="$tender->tender_uin" />
        <livewire:tenders.comparative-statement :tender-id="$tender->id" />

        @if (is_null($tender->tender_opened_at))
            <livewire:tenders.open-tender :tender-id="$tender->id" />
        @endif

        @if (is_null($tender->tender_document_opened_at))
            <livewire:tenders.open-technical-document :tender-id="$tender->id" />
        @endif
    </x-section-centered>
</div>
