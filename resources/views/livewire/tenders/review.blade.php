<div>
    <x-card id="add-review">
        <x-card-form :with-shadow="false" no-padding>
            <x-slot name="title">Tender Document Review by Checker</x-slot>
            <x-slot name="description">Examine and publish tender documents.</x-slot>
            
            @if(count($tenderReview) > 0)
                @if($tenderReview['tender_status'] == 'approved')
                    <x-alert variant="success" :close="false">Approved</x-alert>
                @endif

                @if($tenderReview['tender_status'] == 'cancelled')
                    <x-alert variant="error" :close="false">Cancelled</x-alert>
                @endif

                <x-label class="block mt-4 mb-1">Review:</x-label>
                <div class="text-sm">
                    {!! $tenderReview['checker_remarks'] !!}
                </div>
            @endif
        </x-card-form>
        
        <x-slot name="footer">
            <form wire:submit.prevent="saveReview">
                <x-textarea 
                    label="Remarks / Review"
                    name="review"
                    wire:model.defer="review"
                />

                <x-select label="Status" name="status" wire:model.defer="status">
                    <option value="">Select a status</option>
                    <option value="approved">Approved</option>
                    <option value="cancelled">Cancelled</option>
                </x-select>

                <x-button type="submit" with-spinner wire:target="saveReview">Save Review</x-button>
            </form>
        </x-slot>
    </x-card>

    @push('styles')
    <style>
        .prose br {
            display: block; /* makes it have a width */
            content: ""; /* clears default height */
            margin-top: 16px; /* change this to whatever height you want it */
        }
    </style>
    @endpush
</div>
