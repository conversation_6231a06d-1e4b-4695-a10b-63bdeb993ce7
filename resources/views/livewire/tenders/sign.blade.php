<div>
    <x-slot name="title">Sign</x-slot>

    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item>Sign</x-breadcrumb-item>
                </x-breadcrumb>
    </x-slot>
    </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        <x-card form-action="save">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Basic Details</x-slot>
                <x-slot name="description">Add category, tender work, department, and so on.</x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <x-select label="Company" name="company" wire:model.defer="company">
                        <option value="" selected>Select a company</option>
                        @foreach ($this->companies as $companyKey => $companyValue)
                            <option value="{{ $companyKey }}">{{ $companyValue }}</option>
                        @endforeach
                    </x-select>

                    <x-textarea-simple optional rows="4" label="Message" name="message" wire:model.defer="message" />

                    <x-filepond label="Upload AOC Document in PDF Format" id="aocDocument" name="aocDocument"
                        wire:model.defer="aocDocument" accept-files="application/pdf" max-file-size="8MB" />
                </div>

            </x-card-form>

            <x-slot name="footer" class="flex justify-end items-center">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button with-spinner wire:target="save">Save Tender</x-button>
            </x-slot>
        </x-card>

    </x-section-centered>
</div>
