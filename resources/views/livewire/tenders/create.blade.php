<div
    x-data="{
        classificationType: @entangle('tender_classification').defer,
        isSelective() {
            return this.classificationType === 'selective';
        }
    }"
>
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item>Create New Tender</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        <x-card form-action="save">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Basic Details</x-slot>
                <x-slot name="description">Add category, tender work, department, and so on.</x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <x-select label="Category" name="tender_category" wire:model.defer="tender_category">
                        <option value="">Select a category</option>
                        @foreach ($this->tenderCategories as $tenderCategory)
                            <option value="{{ $tenderCategory->value }}">{{ $tenderCategory->name }}</option>
                        @endforeach
                    </x-select>

                    <x-select label="Tender Type" name="tender_type" wire:model.defer="tender_type">
                        <option value="">Select a type</option>
                        @foreach ($this->tenderTypes as $tenderType)
                            <option value="{{ $tenderType->value }}">{{ $tenderType->name }}</option>
                        @endforeach
                    </x-select>

                    <x-select label="Tender Classification" name="tender_classification" 
                              x-model="classificationType">
                        <option value="">Select a Tender classification</option>
                        <option value="open">Open</option>
                        <option value="selective">Selective</option>
                    </x-select>
                </div>

                <x-textarea
                        label="Title of Work"
                        name="tender_title"
                        wire:model.defer="tender_title"
                />

                <x-input
                        label="Tender Called By"
                        name="tender_called_by"
                        wire:model.defer="tender_called_by"
                />

                <x-input
                        label="Tender Number"
                        name="tender_number"
                        wire:model.defer="tender_number"
                />

                <x-quilljs-editor
                        toolbar-type="minimal"
                        label="Description of work"
                        name="tender_details"
                        wire:model.defer="tender_details"
                        placeholder="Description of work..."
                />

                <x-textarea
                        no-margin
                        label="Location of work"
                        name="tender_address"
                        wire:model.defer="tender_address"
                        placeholder="Address..."
                />
            </x-card-form>

            <x-section-border />

            <div x-show="isSelective()">
                <x-card-form :with-shadow="false" no-padding>
                    <x-slot name="title">Company Details</x-slot>
                    <x-slot name="description">Select companies that will be invited to participate in this tender.</x-slot>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">
                            For selective tenders, you must choose specific companies that will have access to this tender.
                        </p>
                    </div>

                    <x-select
                            label="Select Companies"
                            name="company_details"
                            wire:model.defer="company_details"
                            multiple
                    >
                        @foreach($available_companies as $company)
                            <option value="{{$company['id']}}">{{$company['name']}}</option>
                        @endforeach
                    </x-select>

                    @error('company_details')
                    <div class="mt-1 text-red-600 text-sm">{{ $message }}</div>
                    @enderror
                </x-card-form>

                <x-section-border />
            </div>

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Finance Details</x-slot>
                <x-slot name="description">Add finance details of the tender.</x-slot>

                <x-money
                        class="w-60"
                        label="Tender Cost"
                        name="tender_cost"
                        wire:model.defer="tender_cost"
                />

                <x-money
                        class="w-60"
                        label="Estimated value of work"
                        name="tender_value"
                        wire:model.defer="tender_value"
                />

                <x-textarea
                        rows="3"
                        no-margin
                        label="Tender EMD Details"
                        name="tender_emd"
                        wire:model.defer="tender_emd"
                />
            </x-card-form>

            <x-section-border/>

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Dates</x-slot>
                <x-slot name="description">Add important dates of the tender.</x-slot>

                <div>
                    <div wire:ignore>
                        <x-flatpicker
                                label="Published Date"
                                name="published_date"
                                wire:model.defer="published_date"
                        />
                    </div>

                    <div wire:ignore>
                        <x-flatpicker
                                label="Bid Start Date"
                                name="bid_starting_date"
                                wire:model.defer="bid_starting_date"
                        />
                    </div>

                    <div wire:ignore>
                        <x-flatpicker
                                label="Technical Bid Opening Date"
                                name="technicalbid_opening_date"
                                wire:model.defer="technicalbid_opening_date"
                        />
                    </div>

                    <div wire:ignore>
                        <x-flatpicker
                                :options="[
                                'enableTime' => true,
                            ]"
                                label="Last Date of Submission"
                                name="last_date_of_submission"
                                wire:model.defer="last_date_of_submission"
                        />
                    </div>

                    <div wire:ignore>
                        <x-flatpicker
                                label="Last Date of Document Sale"
                                name="last_date_of_document_sale"
                                wire:model.defer="last_date_of_document_sale"
                        />
                    </div>

                    <div wire:ignore>
                        <x-flatpicker
                                no-margin
                                label="Last Date for Clarification"
                                name="last_date_for_clarification"
                                wire:model.defer="last_date_for_clarification"
                        />
                    </div>
                </div>
            </x-card-form>

            <x-section-border/>

            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Tender Pre Bid Date & Venue</x-slot>
                <x-slot name="description">Add pre bid date & venue of the tender.</x-slot>

                <x-textarea
                        optional
                        label="Pre Bid Meeting Venue"
                        name="pre_bid_meeting_venue"
                        wire:model.defer="pre_bid_meeting_venue"
                        placeholder="Address..."
                />

                <div wire:ignore>
                    <x-flatpicker
                            optional
                            label="Pre Bid Meeting Date"
                            name="pre_bid_meeting_date"
                            wire:model.defer="pre_bid_meeting_date"
                    />
                </div>
            </x-card-form>

            <x-slot name="footer" class="flex justify-end items-center">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button
                        with-spinner
                        wire:target="save"
                >Save Tender
                </x-button>
            </x-slot>
        </x-card>
    </x-section-centered>
</div>