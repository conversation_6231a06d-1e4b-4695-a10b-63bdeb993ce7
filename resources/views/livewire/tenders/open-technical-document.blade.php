<div>
    <x-dialog-modal wire:model="show" id="tender-document-open-modal" form-action="verifyOtp">

        <x-slot name="title">Open Tender Document</x-slot>

        <x-slot name="content">
            @foreach ($evaluators as $evaluator)
                <div>
                    <label>{{ $evaluator['name'] }}</label>
                    <x-input-number
                        input-mode="numeric" 
                        label="Tender OTP"
                        hint="Please enter the OTP sent to {{ $evaluator['name'] }}"
                        name="otps.{{ $evaluator['id'] }}"
                        maxlength="6"
                        wire:model.defer="otps.{{ $evaluator['id'] }}"
                        value="{{ $otps[$evaluator['id']] ?? '' }}" 
                        minlength="6"
                    />
                </div>
            @endforeach
        </x-slot>

        <x-slot name="footer">
            <x-button color="white" id="closeButton" class="mr-2" type='button' with-spinner wire:click="closeModal">Close</x-button>
            <x-button type="submit" id="verifyButton" with-spinner wire:target="verifyOtp">Verify OTP</x-button>
        </x-slot>      
    </x-dialog-modal>
</div>