<div>
    <x-slot name="title">Tender</x-slot>
    
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('tenders') }}">All tenders</x-breadcrumb-item>
                    <x-breadcrumb-item>Tender details</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>

            <x-slot name="action">
                @if ($tender->last_date_of_submission <= now())
                    <x-button class="mr-2" color="white" tag="a" href="{{ route('tenders.biddings', $tender->id) }}" with-icon icon="eye">View Bidding</x-button>
                @endif
                
                @can('checker')
                    @if ($tender -> technicalbid_opening_date <= now() && $tender -> tender_status -> value != 'technical')
                        <x-button class="mr-2" color="white" tag="a" href="{{ route('tenders.open-technical-document', $tender->id) }}" with-icon icon="eye" >Open for Technical Bidding </x-button>
                    @endif
                    <x-button color="white" class="mr-2" tag="a" href="#add-review" with-icon icon="add">Add Review</x-button>
                @endcan
                
                <x-button tag="a" href="{{ route('tenders.edit', $tender->id) }}" with-icon icon="edit">Edit details</x-button>
                {{-- <x-button class="ml-2 pl-0 pr-2" color="white" tag="a" href="#add-review" with-icon icon="dots-vertical"></x-button> --}}
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>
 
    <x-section-centered>
        <x-card overflow-hidden>
            {{-- <div class="shadow absolute -left-[84px] top-[3px] w-[200px] transform -rotate-45 bg-red-600 text-center text-white font-semibold leading-6">New</div> --}}

            <div class="flex space-x-2 items-center">
                {{-- <x-badge variant="success" class="mb-2">New</x-badge> --}}
                <x-badge variant="{{ $tender->tender_status->color() }}" class="mb-2">{{ $tender->tender_status->value }}</x-badge>
            </div>
    
            <div class="md:grid md:grid-cols-4 md:gap-10">
                <div class="col-span-full">
                    <x-heading>{{ $tender->tender_title }}</x-heading>

                    <div class="my-2 text-gray-600">
                        {{ $tender->tender_called_by }} <br>
                        {{ $tender->tender_address }}
                    </div>

                    <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mt-4 space-y-3 md:space-y-0">
                        <div class="flex-1">
                            <div class="text-gray-500 text-sm">Reference No.</div>
                            <x-heading size="md">{{ $tender->tender_number }}</x-heading>
                        </div>
                        <div class="flex-1">
                            <div class="text-gray-500 text-sm">Published Date</div>
                            <x-heading size="md">@dateWithTime($tender->published_date)</x-heading>
                        </div>
                        <div class="flex-1">
                            <div class="text-gray-500 text-sm">Last Date of Submission</div>
                            <x-heading size="md">@dateWithTime($tender->last_date_of_submission)</x-heading>
                        </div>
                    </div>
                </div>
                {{-- <div class="text-center mt-4">
                    <div class="px-4 py-6 w-full bg-gray-100 rounded-md">
                        <x-icon-file-lock class="mx-auto w-10 h-10 text-cyan-600" />
                        <p class="text-xs text-gray-500 mt-2">Tender bidding will be viewable only after the tender period has ended.</p>
                       
                        <x-button color="black" tag="a" href="{{ route('front.tenders.documentpayment', $tender->id) }}" class="w-full"><x-icon-rupee class="w-4 h-4 mr-1 text-gray-100" />Pay Tender Fee</x-button>
                        <p class="text-xs text-gray-500 mt-2">Please pay the Tender Fee to download the documents associated with this tender.</p>
                    </div>
                </div> --}}
            </div>
        </x-card>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-5"> 
            <x-card card-classes="md:col-span-2 mt-4">
                <x-description-list size="xs">
                    <x-slot name="heading">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                            </svg>  
                            <span class="text-gray-700">Basic Details</span>
                        </div>
                    </x-slot>
    
                    {{-- <x-description-list.item>
                        <x-slot name="title">Tender Title</x-slot>
                        <x-slot name="description">{{ $tender->tender_title }}</x-slot>
                    </x-description-list.item> --}}
    
                    {{-- <x-description-list.item>
                        <x-slot name="title">Tender Description</x-slot>
                        <x-slot name="description">
                            <div class="prose prose-sm">
                                {!! $tender->tender_details !!}
                            </div>
                        </x-slot>
                    </x-description-list.item> --}}
                    
                    <x-description-list.item>
                        <x-slot name="title">Organization Chain</x-slot>
                        <x-slot name="description">{{ $tender?->department?->name }}</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">Tender Reference No.</x-slot>
                        <x-slot name="description">{{ $tender->tender_number }}</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">Tender ID</x-slot>
                        <x-slot name="description">{{ $tender->uin ?? 'na' }}</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">Tender Type</x-slot>
                        <x-slot name="description">{{ $tender->tender_type }}</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">Tender Category</x-slot>
                        <x-slot name="description">{{ Str::title($tender->tender_category) }}</x-slot>
                    </x-description-list.item>
    
                </x-description-list>
            </x-card>
            
            <x-card card-classes="mt-4">
                <x-description-list size="xs" grid="one-by-two">
                    <x-slot name="heading">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                                <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM9 7.5A.75.75 0 009 9h1.5c.98 0 1.813.626 2.122 1.5H9A.75.75 0 009 12h3.622a2.251 2.251 0 01-2.122 1.5H9a.75.75 0 00-.53 1.28l3 3a.75.75 0 101.06-1.06L10.8 14.988A3.752 3.752 0 0014.175 12H15a.75.75 0 000-1.5h-.825A3.733 3.733 0 0013.5 9H15a.75.75 0 000-1.5H9z" clip-rule="evenodd" />
                            </svg>    
                            <span class="text-gray-700">Financial Details</span>
                        </div>
                    </x-slot>
                    
                    <x-description-list.item>
                        <x-slot name="title">Tender Fee</x-slot>
                        <x-slot name="description">@inr($tender->tender_cost)</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">EMD Amount</x-slot>
                        <x-slot name="description">{{ $tender->tender_emd }}</x-slot>
                    </x-description-list.item>
    
                    <x-description-list.item>
                        <x-slot name="title">Tender Value</x-slot>
                        <x-slot name="description">@inr($tender->tender_value)</x-slot>
                    </x-description-list.item>
                </x-description-list>
            </x-card>
        </div>

        <x-card card-classes="mt-4" no-padding overflow-hidden>
            <div class="flex items-center space-x-2 px-4 py-2.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                    <path fill-rule="evenodd" d="M7.5 5.25a3 3 0 013-3h3a3 3 0 013 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0112 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 017.5 5.455V5.25zm7.5 0v.09a49.488 49.488 0 00-6 0v-.09a1.5 1.5 0 011.5-1.5h3a1.5 1.5 0 011.5 1.5zm-3 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                    <path d="M3 18.4v-2.796a4.3 4.3 0 00.713.31A26.226 26.226 0 0012 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 01-6.477-.427C4.047 21.128 3 19.852 3 18.4z" />
                </svg>                                                                
                <x-heading size="lg" class="text-gray-700">Work Details</x-heading>
            </div>

            @if ($tender->tenderitems->isNotEmpty())
                <x-table.table table-condensed :with-shadow="false" :rounded="false">
                    <thead>
                        <tr>
                            <x-table.thead class="py-1.5">Item/Work Description</x-table.thead>
                            <x-table.thead class="py-1.5">Quantity</x-table.thead>
                            <x-table.thead class="py-1.5">Unit</x-table.thead>
                            <x-table.thead class="py-1.5">Estimated Price</x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($tender->tenderitems as $item)
                            <tr>
                                <x-table.tdata>
                                    {{ $item->item_type }}
                                    <div class="text-sm text-gray-500 flex space-x-2 md:hidden">
                                        <span>Quantity: {{ $item->quantity }}</span>
                                        <span>Unit: {{ $item->unit }}</span>
                                    </div>
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $item->quantity }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    {{ $item->unit }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    @inr($item->estimated_price)
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            @endif
        </x-card>

        @if ($tender->pre_bid_meeting_venue && $tender->pre_bid_meeting_date)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                <x-card card-classes="mt-4">
                    <x-description-list size="xs" grid="one-by-two">
                        <x-slot name="heading">
                            <div class="flex items-center space-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                                    <path d="M12.75 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM7.5 15.75a.75.75 0 100-********* 0 000 1.5zM8.25 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM9.75 15.75a.75.75 0 100-********* 0 000 1.5zM10.5 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 15.75a.75.75 0 100-********* 0 000 1.5zM12.75 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM14.25 15.75a.75.75 0 100-********* 0 000 1.5zM15 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 15.75a.75.75 0 100-********* 0 000 1.5zM15 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 13.5a.75.75 0 100-********* 0 000 1.5z" />
                                    <path fill-rule="evenodd" d="M6.75 2.25A.75.75 0 017.5 3v1.5h9V3A.75.75 0 0118 3v1.5h.75a3 3 0 013 3v11.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V7.5a3 3 0 013-3H6V3a.75.75 0 01.75-.75zm13.5 9a1.5 1.5 0 00-1.5-1.5H5.25a1.5 1.5 0 00-1.5 1.5v7.5a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5v-7.5z" clip-rule="evenodd" />
                                </svg>                                  
                                <span class="text-gray-700">Critical Dates</span>
                            </div>
                        </x-slot>
                        
                        <x-description-list.item>
                            <x-slot name="title">Published Date</x-slot>
                            <x-slot name="description">{{ $tender->published_date->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>

                        <x-description-list.item>
                            <x-slot name="title">Bid Starting Date</x-slot>
                            <x-slot name="description">{{ $tender->bid_starting_date?->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>

                        <x-description-list.item>
                            <x-slot name="title">Technical Bid Opening Date</x-slot>
                            <x-slot name="description">{{ $tender->technicalbid_opening_date?->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>
        
                        <x-description-list.item>
                            <x-slot name="title">Last Date of Document Sale</x-slot>
                            <x-slot name="description">{{ $tender->last_date_of_document_sale->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>
        
                        <x-description-list.item>
                            <x-slot name="title">Last Date for Clarification</x-slot>
                            <x-slot name="description">{{ $tender->last_date_for_clarification->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>
        
                        <x-description-list.item>
                            <x-slot name="title">Last Date of Submission</x-slot>
                            <x-slot name="description">{{ $tender->last_date_of_submission->format('d-M-Y h:i a') }}</x-slot>
                        </x-description-list.item>
                    </x-description-list>
                </x-card>
            
                <x-card card-classes="mt-4">
                    <x-description-list size="xs">
                        <x-slot name="heading">
                            <div class="flex items-center space-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                                    <path fill-rule="evenodd" d="M11.54 22.351l.07.04.028.016a.76.76 0 00.723 0l.028-.015.071-.041a16.975 16.975 0 001.144-.742 19.58 19.58 0 002.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 00-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 002.682 2.282 16.975 16.975 0 001.145.742zM12 13.5a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                                </svg>                                                                
                                <span class="text-gray-700">Pre Bid Meeting Date & Venue</span>
                            </div>
                        </x-slot>
                        
                        <x-description-list.item>
                            <x-slot name="title">Address</x-slot>
                            <x-slot name="description">{{ $tender->pre_bid_meeting_venue }}</x-slot>
                        </x-description-list.item>
        
                        <x-description-list.item>
                            <x-slot name="title">Date</x-slot>
                            <x-slot name="description">{{ $tender->pre_bid_meeting_date?->format('d-M-Y') }}</x-slot>
                        </x-description-list.item>

                        @if (!is_null($tender->pre_bid_minutes_document_path))
                            <x-description-list.item>
                                <x-slot name="title">Pre Bid Minutes</x-slot>
                                <x-slot name="description">
                                    <x-link href="{{ $tender->pre_bid_minutes_document_path }}">Download</x-link>
                                </x-slot>
                            </x-description-list.item>
                        @endif
                    </x-description-list>
                </x-card>
            </div>
        @else
            <x-card card-classes="mt-4">
                <x-description-list size="xs" grid="one-by-two">
                    <x-slot name="heading">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                                <path d="M12.75 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM7.5 15.75a.75.75 0 100-********* 0 000 1.5zM8.25 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM9.75 15.75a.75.75 0 100-********* 0 000 1.5zM10.5 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 15.75a.75.75 0 100-********* 0 000 1.5zM12.75 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM14.25 15.75a.75.75 0 100-********* 0 000 1.5zM15 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 15.75a.75.75 0 100-********* 0 000 1.5zM15 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 13.5a.75.75 0 100-********* 0 000 1.5z" />
                                <path fill-rule="evenodd" d="M6.75 2.25A.75.75 0 017.5 3v1.5h9V3A.75.75 0 0118 3v1.5h.75a3 3 0 013 3v11.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V7.5a3 3 0 013-3H6V3a.75.75 0 01.75-.75zm13.5 9a1.5 1.5 0 00-1.5-1.5H5.25a1.5 1.5 0 00-1.5 1.5v7.5a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5v-7.5z" clip-rule="evenodd" />
                            </svg>                                  
                            <span class="text-gray-700">Critical Dates</span>
                        </div>
                    </x-slot>
                    
                    <x-description-list.item>
                        <x-slot name="title">Published Date</x-slot>
                        <x-slot name="description">{{ $tender->published_date->format('d-M-Y') }}</x-slot>
                    </x-description-list.item>

                    <x-description-list.item>
                        <x-slot name="title">Bid Starting Date</x-slot>
                        <x-slot name="description">{{ $tender->bid_starting_date?->format('d-M-Y') }}</x-slot>
                    </x-description-list.item>

                    <x-description-list.item>
                        <x-slot name="title">Technical Bid Opening Date</x-slot>
                        <x-slot name="description">{{ $tender->technicalbid_opening_date?->format('d-M-Y') }}</x-slot>
                    </x-description-list.item>

                    <x-description-list.item>
                        <x-slot name="title">Last Date of Document Sale</x-slot>
                        <x-slot name="description">{{ $tender->last_date_of_document_sale->format('d-M-Y') }}</x-slot>
                    </x-description-list.item>

                    <x-description-list.item>
                        <x-slot name="title">Last Date for Clarification</x-slot>
                        <x-slot name="description">{{ $tender->last_date_for_clarification->format('d-M-Y') }}</x-slot>
                    </x-description-list.item>

                    <x-description-list.item>
                        <x-slot name="title">Last Date of Submission</x-slot>
                        <x-slot name="description">{{ $tender->last_date_of_submission->format('d-M-Y h:i a') }}</x-slot>
                    </x-description-list.item>
                </x-description-list>
            </x-card>
        @endif

        <x-card card-classes="mt-4">
            <x-description-list size="xs">
                <x-slot name="heading">
                    <div class="flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                            <path fill-rule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625zM7.5 15a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5A.75.75 0 017.5 15zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H8.25z" clip-rule="evenodd" />
                            <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z" />
                        </svg>                                                               
                        <span class="text-gray-700">Tender Documents</span>
                    </div>
                </x-slot>
                
                @if ($tender->documents->isNotEmpty())
                    @foreach($tender->documents as $tenderDocument)
                        <x-description-list.item>
                            <x-slot name="title">{{ $tenderDocument->title }}</x-slot>
                            <x-slot name="description">
                                <x-link href="{{ $tenderDocument->file_path }}">Download PDF ({{ $tenderDocument->size_formatted }})</x-link>
                            </x-slot>
                        </x-description-list.item>
                    @endforeach
                @endif
            </x-description-list>
        </x-card>

        <x-card card-classes="mt-4">
            <x-description-list size="xs">
                <x-slot name="heading">
                    <div class="flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-cyan-700">
                            <path d="M11.584 2.376a.75.75 0 01.832 0l9 6a.75.75 0 11-.832 1.248L12 3.901 3.416 9.624a.75.75 0 01-.832-1.248l9-6z" />
                            <path fill-rule="evenodd" d="M20.25 10.332v9.918H21a.75.75 0 010 1.5H3a.75.75 0 010-1.5h.75v-9.918a.75.75 0 01.634-.74A49.109 49.109 0 0112 9c2.59 0 5.134.202 7.616.592a.75.75 0 01.634.74zm-7.5 2.418a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75zm3-.75a.75.75 0 01.75.75v6.75a.75.75 0 01-1.5 0v-6.75a.75.75 0 01.75-.75zM9 12.75a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75z" clip-rule="evenodd" />
                            <path d="M12 7.875a1.125 1.125 0 100-2.25 1.125 1.125 0 000 2.25z" />
                        </svg>                                                                                            
                        <span class="text-gray-700">Tender Inviting Authority</span>
                    </div>
                </x-slot>
                
                <x-description-list.item>
                    <x-slot name="title">Name</x-slot>
                    <x-slot name="description">{{ $tender->tender_called_by }}</x-slot>
                </x-description-list.item>

                <x-description-list.item>
                    <x-slot name="title">Address</x-slot>
                    <x-slot name="description">{{ $tender->tender_address }}</x-slot>
                </x-description-list.item>
            </x-description-list>
        </x-card>

        @can('checker')
        <div class="mt-4">
            <livewire:tenders.review :tenderId="$tender->id" />
        </div>
        @endcan
       
    </x-section-centered-wide>
</div>
