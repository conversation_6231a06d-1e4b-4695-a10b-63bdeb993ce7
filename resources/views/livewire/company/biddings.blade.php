<div>
    <x-slot name="title">Tenders</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                List of Tenders Applied
            </x-slot>

            <x-slot name="action">
                <x-button target="_blank" color="white" tag="a" class="text-cyan-600" href="{{ route('front.tenders') }}">View latest tenders<x-icon-arrow-up-left class="w-5 h-5 rotate-90" /></x-button>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        <x-card card-classes="mb-6"> 
            <x-input-search 
                no-margin
                label="Search" 
                name="search" 
                wire:model.debounce.600ms="search" 
                placeholder="Search by tender title / number"
            />
        </x-card>

        @if($biddings->isNotEmpty())
            @foreach($biddings as $bidding)
                <x-card card-classes="mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div>
                            <div><strong class="text-sm">Tender Number</strong></div>
                            <p>{{ $bidding->tender->tender_number }}</p>
                        </div>
                        <div>
                            <div><strong class="text-sm">Applied On</strong></div>
                            <p>@dateWithTime($bidding->created_at)</p>
                        </div>
                    </div>

                    <x-link variant="black" class="hover:text-cyan-600 inline-block font-semibold text-lg leading-6" href="{{ route('front.tenders.show', $bidding->tender->id) }}">{{ $bidding->tender->tender_title }}</x-link>
                    
                    <x-slot name="footer">
                        <div class="flex items-center space-x-2">
                            <x-button color="white" href="#" x-data="{}" x-on:click.prevent="livewire.emitTo('company.tender-payment', 'openTenderPaymentSlideover', '{{ $bidding?->tender?->id }}')">Tender Payment</x-button>
                            <x-button color="white" href="#" x-data="{}" x-on:click.prevent="livewire.emitTo('company.tender-bidding', 'openTenderBiddingSlideover', '{{ $bidding->id }}')">Biddings</x-button>
                            <x-button color="white" href="#" x-data="{}" x-on:click.prevent="livewire.emitTo('company.emd-payment-details', 'openTenderEmdPaymentSlideover', '{{ $bidding?->tender?->id }}')">EMD Details</x-button>
                            <x-button color="white" href="#" x-data="{}" x-on:click.prevent="livewire.emitTo('company.bid-details', 'openBidDetailsSlideover', '{{ $bidding?->tender?->id }}')">Bid Details</x-button>
                            <x-button color="white" href="#" x-data="{}" x-on:click.prevent="livewire.emitTo('company.upload-tender-images', 'openTenderImageUploadModal', '{{ $bidding?->tender?->id }}')">Upload Images</x-button>
                            
                            {{-- <x-button color="white" href="#">Tender Result</x-button> --}}
                        </div>
                    </x-slot>
                    
                </x-card>
            @endforeach
  
            <div class="mt-5">{{ $biddings->links() }}</div>
        @else 
            <x-card-empty />
        @endif
    </x-section-centered>

    <livewire:company.tender-payment />
    <livewire:company.tender-bidding />
    <livewire:company.emd-payment-details />
    <livewire:company.bid-details />
    <livewire:company.upload-tender-images />
</div>
