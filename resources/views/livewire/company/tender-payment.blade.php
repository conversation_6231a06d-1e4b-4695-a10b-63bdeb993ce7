<div>
    <x-slideovers wire:model="openSlideover">
        <x-slot name="header">Tender Document Payment Details</x-slot>
        
        <div class="py-2 px-6">
            @if ($hasFinishedTenderDocumentPayment)
                <x-heading size="md" class="mb-2">
                    For Tender No. <br>
                    {{ $tenderNumber }}
                </x-heading>

                <x-description-list>
                    <x-description-list.item>
                        <x-slot name="title">Payment ID</x-slot>
                        <x-slot name="description" class="font-medium">{{ $documentPaymentRazorpayPaymentId }}</x-slot>
                    </x-description-list.item>
                    <x-description-list.item>
                        <x-slot name="title">Amount</x-slot>
                        <x-slot name="description" class="font-medium">@inr($documentPaymentAmount)</x-slot>
                    </x-description-list.item>
                    <x-description-list.item>
                        <x-slot name="title">Status</x-slot>
                        <x-slot name="description" class="font-medium">{{ $documentPaymentStatus }}</x-slot>
                    </x-description-list.item>
                    <x-description-list.item>
                        <x-slot name="title">Payment Mode</x-slot>
                        <x-slot name="description" class="font-medium">{{ $documentPaymentMode }}</x-slot>
                    </x-description-list.item>
                    <x-description-list.item>
                        <x-slot name="title">Payment At</x-slot>
                        <x-slot name="description" class="font-medium">{{ $documentPaymentAt }}</x-slot>
                    </x-description-list.item>
                </x-description-list>
            @else
                <p>No tender payment details found.</p>
            @endif
        </div>
    </x-slideovers>
</div>
