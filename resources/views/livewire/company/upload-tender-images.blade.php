<div>
    <x-dialog-modal wire:model="show" id="tender-image-upload-modal" form-action="uploadImages">

        <x-slot name="title">Upload Tender Progress Images</x-slot>

        <x-slot name="content">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Upload Images with GPS Location Data
                    </label>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    Please ensure your device allows location access to upload GPS-tagged images.
                                </p>
                            </div>
                        </div>
                    </div>
                    <input type="file" wire:model="images" multiple class="block w-full text-sm text-gray-500
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-semibold
                        file:bg-blue-50 file:text-blue-700
                        hover:file:bg-blue-100">
                    @error('images.*') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
                
                <div id="image-preview-container" class="grid grid-cols-2 gap-4 mt-4 hidden">
                    <!-- Image previews will be inserted here -->
                </div>
                
                <div id="metadata-container" class="hidden">
                    <!-- Hidden container for metadata -->
                </div>
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-button color="white" class="mr-2" type="button" wire:click.prevent="show = false">Close</x-button>
            <x-button type="submit" with-spinner wire:target="uploadImages">Upload Images</x-button>
        </x-slot>
    </x-dialog-modal>
    
    <script>
        // Event listeners for debugging upload process
        window.addEventListener('images-uploaded-successfully', (event) => {
            console.log('Images uploaded successfully!');
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            const modalObserver = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'style' && document.getElementById('tender-image-upload-modal').style.display !== 'none') {
                        initImageUpload();
                    }
                });
            });
            
            const modal = document.getElementById('tender-image-upload-modal');
            if (modal) {
                modalObserver.observe(modal, { attributes: true });
            }
            
            function initImageUpload() {
                const fileInput = document.querySelector('input[type="file"]');
                const previewContainer = document.getElementById('image-preview-container');
                const metadataContainer = document.getElementById('metadata-container');
                
                if (!fileInput) return;
                
                fileInput.addEventListener('change', async function() {
                    previewContainer.innerHTML = '';
                    previewContainer.classList.remove('hidden');
                    
                    if (this.files.length === 0) {
                        previewContainer.classList.add('hidden');
                        return;
                    }
                    
                    const metadata = [];
                    
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];
                        const reader = new FileReader();
                        
                        reader.onload = async function(e) {
                            // Create image preview
                            const imgPreview = document.createElement('div');
                            imgPreview.className = 'border rounded-md p-2';
                            
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'w-full h-32 object-cover rounded';
                            
                            const infoDiv = document.createElement('div');
                            infoDiv.className = 'mt-2 text-xs';
                            
                            const nameSpan = document.createElement('p');
                            nameSpan.className = 'font-medium truncate';
                            nameSpan.textContent = file.name;
                            
                            const locationDiv = document.createElement('div');
                            locationDiv.className = 'mt-1 flex items-start';
                            locationDiv.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span id="location-${i}" class="text-gray-500">Extracting location data...</span>
                            `;
                            
                            infoDiv.appendChild(nameSpan);
                            infoDiv.appendChild(locationDiv);
                            
                            imgPreview.appendChild(img);
                            imgPreview.appendChild(infoDiv);
                            previewContainer.appendChild(imgPreview);
                            
                            // Extract GPS data from image
                            try {
                                const imageMetadata = await extractImageGPS(file);
                                metadata[i] = imageMetadata;
                                
                                // Update location text
                                const locationSpan = document.getElementById(`location-${i}`);
                                if (locationSpan) {
                                    if (imageMetadata.latitude && imageMetadata.longitude) {
                                        locationSpan.textContent = imageMetadata.location || `${imageMetadata.latitude.toFixed(6)}, ${imageMetadata.longitude.toFixed(6)}`;
                                        locationSpan.className = 'text-green-600';
                                    } else {
                                        locationSpan.textContent = 'No GPS data found';
                                        locationSpan.className = 'text-red-500';
                                    }
                                }
                            } catch (error) {
                                console.error('Error extracting GPS data:', error);
                                const locationSpan = document.getElementById(`location-${i}`);
                                if (locationSpan) {
                                    locationSpan.textContent = 'Failed to extract location data';
                                    locationSpan.className = 'text-red-500';
                                }
                            }
                            
                            // Send metadata to Livewire component when all files are processed
                            if (Object.keys(metadata).length === fileInput.files.length) {
                                @this.call('receiveImageMetadata', metadata);
                            }
                        };
                        
                        reader.readAsDataURL(file);
                    }
                });
                
                async function extractImageGPS(file) {
                    return new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const view = new DataView(e.target.result);
                            const metadata = {
                                latitude: null,
                                longitude: null,
                                timestamp: null,
                                location: null
                            };
                            
                            try {
                                if (view.getUint16(0, false) != 0xFFD8) {
                                    resolve(metadata); // Not a JPEG
                                    return;
                                }
                                
                                let offset = 2;
                                let length = view.byteLength;
                                let marker;
                                
                                while (offset < length) {
                                    marker = view.getUint16(offset, false);
                                    offset += 2;
                                    
                                    // EXIF data marker
                                    if (marker === 0xFFE1) {
                                        if (view.getUint32(offset + 2, false) != 0x45786966) {
                                            break; // Not EXIF
                                        }
                                        
                                        const little = view.getUint16(offset + 8, false) === 0x4949;
                                        offset += 8;
                                        
                                        const tags = view.getUint16(offset, little);
                                        offset += 2;
                                        
                                        for (let i = 0; i < tags; i++) {
                                            const tag = view.getUint16(offset + (i * 12), little);
                                            
                                            // GPS Info
                                            if (tag === 0x8825) {
                                                const gpsOffset = view.getUint32(offset + (i * 12) + 8, little);
                                                const gpsInfo = processGPSInfo(view, offset + gpsOffset, little);
                                                
                                                if (gpsInfo) {
                                                    metadata.latitude = gpsInfo.latitude;
                                                    metadata.longitude = gpsInfo.longitude;
                                                    metadata.timestamp = gpsInfo.timestamp;
                                                }
                                                break;
                                            }
                                        }
                                    } else if ((marker & 0xFF00) !== 0xFF00) {
                                        break;
                                    } else {
                                        offset += view.getUint16(offset, false);
                                    }
                                }
                                
                                // If we have GPS coords, try to get a location name
                                if (metadata.latitude && metadata.longitude && navigator.geolocation) {
                                    // This would normally use a reverse geocoding service
                                    // For now, we'll just use the coordinates
                                    metadata.location = `${metadata.latitude.toFixed(6)}, ${metadata.longitude.toFixed(6)}`;
                                }
                            } catch (error) {
                                console.error('Error parsing EXIF data:', error);
                            }
                            
                            resolve(metadata);
                        };
                        
                        reader.readAsArrayBuffer(file);
                    });
                }
                
                function processGPSInfo(dataView, startOffset, littleEndian) {
                    const tagCount = dataView.getUint16(startOffset, littleEndian);
                    const gpsData = {};
                    
                    for (let i = 0; i < tagCount; i++) {
                        const tagOffset = startOffset + 2 + (i * 12);
                        const tag = dataView.getUint16(tagOffset, littleEndian);
                        
                        // GPS Latitude Ref (N/S)
                        if (tag === 1) {
                            const valueOffset = dataView.getUint32(tagOffset + 8, littleEndian) + startOffset;
                            gpsData.latitudeRef = String.fromCharCode(dataView.getUint8(valueOffset));
                        }
                        // GPS Latitude
                        else if (tag === 2) {
                            const valueOffset = dataView.getUint32(tagOffset + 8, littleEndian) + startOffset;
                            gpsData.latitude = getCoordinate(dataView, valueOffset, littleEndian);
                        }
                        // GPS Longitude Ref (E/W)
                        else if (tag === 3) {
                            const valueOffset = dataView.getUint32(tagOffset + 8, littleEndian) + startOffset;
                            gpsData.longitudeRef = String.fromCharCode(dataView.getUint8(valueOffset));
                        }
                        // GPS Longitude
                        else if (tag === 4) {
                            const valueOffset = dataView.getUint32(tagOffset + 8, littleEndian) + startOffset;
                            gpsData.longitude = getCoordinate(dataView, valueOffset, littleEndian);
                        }
                        // GPS Timestamp
                        else if (tag === 7 || tag === 29) {
                            const valueOffset = dataView.getUint32(tagOffset + 8, littleEndian) + startOffset;
                            const hour = getRational(dataView, valueOffset, littleEndian);
                            const minute = getRational(dataView, valueOffset + 8, littleEndian);
                            const second = getRational(dataView, valueOffset + 16, littleEndian);
                            
                            // Use current date with GPS time
                            const now = new Date();
                            gpsData.timestamp = new Date(
                                now.getFullYear(),
                                now.getMonth(),
                                now.getDate(),
                                hour,
                                minute,
                                second
                            ).toISOString();
                        }
                    }
                    
                    if (gpsData.latitude && gpsData.longitude) {
                        // Apply N/S and E/W references
                        if (gpsData.latitudeRef === 'S') gpsData.latitude = -gpsData.latitude;
                        if (gpsData.longitudeRef === 'W') gpsData.longitude = -gpsData.longitude;
                        
                        return {
                            latitude: gpsData.latitude,
                            longitude: gpsData.longitude,
                            timestamp: gpsData.timestamp || new Date().toISOString()
                        };
                    }
                    
                    return null;
                }
                
                function getCoordinate(dataView, offset, littleEndian) {
                    const degrees = getRational(dataView, offset, littleEndian);
                    const minutes = getRational(dataView, offset + 8, littleEndian);
                    const seconds = getRational(dataView, offset + 16, littleEndian);
                    
                    return degrees + (minutes / 60) + (seconds / 3600);
                }
                
                function getRational(dataView, offset, littleEndian) {
                    const numerator = dataView.getUint32(offset, littleEndian);
                    const denominator = dataView.getUint32(offset + 4, littleEndian);
                    return denominator === 0 ? 0 : numerator / denominator;
                }
            }
        });
    </script>
</div>