<div>
    <x-slideovers wire:model="openSlideover">
        <x-slot name="header">Tender Bidding Details</x-slot>
        
        <div class="py-2 px-6">
            <x-heading size="md" class="mb-2">
                For Tender No. <br>
                {{ $tenderNumber }}
            </x-heading>

            @if(count($biddingPrices))
                <x-table.table :rounded="false" :with-shadow="false" table-condensed>
                    <thead>
                        <tr>
                            <x-table.thead>Item</x-table.thead>
                            <x-table.thead>Price</x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($biddingPrices as $biddingPrice)
                            <tr>
                                <x-table.tdata>
                                    {{ $biddingPrice['item_name'] }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    <strong>@inr($biddingPrice['price'])</strong>
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            @endif

            {{-- @json($biddingPrices) --}}
         
        </div>
    </x-slideovers>
</div>
