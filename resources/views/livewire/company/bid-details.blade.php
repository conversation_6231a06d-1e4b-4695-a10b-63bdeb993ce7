<div>
    <x-slideovers wire:model="openSlideover">
        <x-slot name="header">Bid Details</x-slot>
        
        <div class="py-4 px-6">
            @if (count($timelines))
                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            @if ($timelines['documentPaymentStatus'] == 'Purchased')
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>

                    <x-slot name="title">Tender Purchased</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['documentPaymentDate'] ?? 'n/a' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['documentPaymentStatus'] }}, {{ $timelines['documentPaymentMode'] ?? 'n/a' }}</span>
                </x-timeline> 

                @if ($timelines['preBidMeetingDate'])
                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </x-slot>
                    
                    <x-slot name="title">Pre-bid Meeting</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['preBidMeetingDate'] ?? '' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['preBidMeetingVenue'] ?? '' }}</span> <br>
                    @if (! is_null($timelines['preBidMeetingMinutes']))
                        <x-link href="{{ $timelines['preBidMeetingMinutes'] ?? '' }}">Document Download</x-link>
                    @endif
                </x-timeline> 
                @endif

                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            @if ($timelines['emdPaymentStatus'] == 'paid')
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>
                    
                    <x-slot name="title">EMD Payment</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['emdPaymentDate'] ?? '' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['emdPaymentStatus'] ?? '' }}</span>
                </x-timeline> 
                
                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            @if (!is_null($timelines['bidSubmissionDate']))
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>

                    <x-slot name="title">Bid Submission</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['bidSubmissionDate'] ?? '' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['bidSubmissionDate'] ? 'Submitted' : 'Not yet' }}</span>
                </x-timeline>

                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                             @if ($timelines['technicalBidStatus'] === 'accepted')
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>

                    <x-slot name="title">Technical Bid</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['technicalBidDate'] ?? '' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['technicalBidStatus'] ?? '' }}</span>
                </x-timeline>

                <x-timeline :last="false">
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            @if ($timelines['financialBidStatus'] === 'Accepted')
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>

                    <x-slot name="title">Financial Bid</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['financialBidDate'] ?? '' }}</x-slot>
                    
                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['financialBidStatus'] }}</span>
                </x-timeline>

                <x-timeline>
                    <x-slot name="customIcon">
                        <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            @if ($timelines['financialBidStatus'] === 'Accepted')
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-green-600">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-red-500">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </x-slot>

                    <x-slot name="title">AOC</x-slot>
                    <x-slot name="subtitle">on {{ $timelines['financialBidDate'] ?? '' }}</x-slot>

                    <span class="uppercase tracking-wider font-medium text-xs text-gray-600">{{ $timelines['aocCompanyName'] ?? '' }}</span> <br>
                    <x-link href="{{ $timelines['aocDocument'] }}">Download</x-link>
                </x-timeline>

            @else
                <p class="py-6">No bid details found yet.</p>
            @endif
        </div>
    </x-slideovers>
</div>
