<div>
    <x-slot name="title">All notices</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                Notices
            </x-slot>

            <x-slot name="action">
                <x-button tag="a" href="{{ route('notices.create') }}" with-icon icon="add">New Notice</x-button>    
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
{{--     
        <x-card card-classes="mb-6">
            <x-input-search 
                no-margin
                label="Search" 
                name="search" 
                wire:model.debounce.600ms="search" 
                placeholder="Search by notice title"
            />
        </x-card> --}}

        @if($notices->isNotEmpty())
            <x-card no-padding>
                <x-table.table>
                    <thead>
                        <tr>
                            <x-table.thead>Created on</x-table.thead>
                            <x-table.thead>Notice Title</x-table.thead>
                            <x-table.thead></x-table.thead>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($notices as $notice)
                            <tr>
                                <x-table.tdata class="w-1/6">
                                    @date($notice->created_at)
                                </x-table.tdata>

                                <x-table.tdata class="w-4/6">
                                    <x-heading size="lg">{{ $notice->title }}</x-heading>
                                </x-table.tdata>
                              
                                <x-table.tdata class="w-1/6">
                                    <x-link 
                                        href="#"
                                        variant="danger"
                                        onclick="confirm('Are you sure you want to remove the user from this group?') || event.stopImmediatePropagation()"
                                        wire:click="remove('{{ $notice->id }}')" 
                                    >Delete</x-link>
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card>

            <div class="mt-5">{{ $notices->links() }}</div>
        @else 
            <x-card-empty />
        @endif
    </x-section-centered>
</div>
