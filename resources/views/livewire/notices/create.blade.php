<div>
    <x-slot name="title">Notices</x-slot>
    
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('notices') }}">All notices</x-breadcrumb-item>
                    <x-breadcrumb-item>Create New Notice</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>
 
    <x-section-centered>
        <x-card form-action="save">
            <x-card-form class="shadow-none" no-padding>
                <x-slot name="title">Notice</x-slot>
                <x-slot name="description">This notice will appear in the homepage of the eTender portal.</x-slot>
 
                <x-textarea
                    rows="2"
                    label="Notice Title" 
                    name="title"
                    wire:model.defer="title" 
                />

                <x-input 
                    optional
                    label="Link"
                    name="link"
                    wire:model.defer="link"
                />
            </x-card-form>
       
            <x-slot name="footer" class="text-right">
                <div class="mr-4">
                    <x-inline-toastr on="saved">Saved.</x-inline-toastr>
                </div>

                <x-button
                    color="black"
                    with-spinner
                    wire:target="save"
                >Save Notice</x-button>
            </x-slot>
        </x-card>
       
    </x-section-centered>
</div>
