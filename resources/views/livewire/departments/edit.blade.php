<div>
    <x-slot name="title">Departments</x-slot>
  
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:beforeTitle>
                <x-breadcrumb class="text-lg">
                    <x-breadcrumb-item href="{{ route('departments') }}">All Departments</x-breadcrumb-item>
                    <x-breadcrumb-item>Edit Department</x-breadcrumb-item>
                </x-breadcrumb>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>
    <x-section-centered>

        <x-card form-action="update" card-classes="mb-6">
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Department Name</x-slot>
               
                <x-input
                    name="name"
                    wire:model.defer="name"
                />
            </x-card-form>

            <x-slot name="footer" class="flex justify-end">
                <x-button type="submit" with-spinner wire:target="update">Save</x-button>
            </x-slot>
        </x-card>

        <x-card>
            <x-card-form :with-shadow="false" no-padding>
                <x-slot name="title">Department Logo</x-slot>
                
                @if ($departmentImage)
                    <div class="w-40 h-40 rounded overflow-hidden">
                        <img src="{{ $departmentImage }}" loading="lazy" class="object-fit w-full h-40" />
                    </div>
                @else
                    <x-filepond 
                        name="photo"
                        wire:model="photo"
                    />
                @endif
            </x-card-form>

            <x-slot name="footer" class="flex justify-end">
                @if ($departmentImage)
                    <x-button 
                        type="button" 
                        color="white" 
                        class="text-red-600" 
                        with-spinner 
                        wire:target="deleteImage"
                        onclick="confirm('Are you sure you want to remove the image from this department?') || event.stopImmediatePropagation()"
                        wire:click="deleteImage" 
                    >Delete image</x-button>
                @else
                    <x-button type="button" with-spinner wire:target="updateImage,photo" wire:click="updateImage">Save</x-button>
                @endif
            </x-slot>
        </x-card>
         
    </x-section-centered>

    @include('partials.js._filepond-scripts')
</div>
