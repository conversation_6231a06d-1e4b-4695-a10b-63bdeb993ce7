<div>
    <x-slot name="title">All Departments</x-slot>
 
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent>
            <x-slot:title>
                Departments
            </x-slot>

            <x-slot name="action">
                <x-button type="button" x-data="{}" x-on:click="livewire.emitTo('departments.create', 'show')" with-icon icon="add">New Department</x-button>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <x-section-centered>
        @if($departments->isNotEmpty())
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($departments as $department)
                    <x-card no-padding overflow-hidden>
                        <div class="bg-gray-50 h-16 px-4 border-b border-gray-100 flex items-center justify-end">
                            <x-link href="{{ route('departments.edit', $department->id) }}">Edit</x-link>
                        </div>
                        <div class="-mt-10 w-24 h-24 border rounded-full relative bg-white overflow-hidden mx-auto">
                            @if($department->photo_url)
                                <img src="{{ $department->photo_url }}" alt="department-logo" loading="lazy" class="object-cover h-full w-full absolute">
                            @endif
                        </div>
                        <div class="p-5">
                            <x-heading size="xl" class="mb-2">{{ $department->name }}</x-heading>

                            <div class="flex">
                                <div class="flex-1">
                                    Makers: <strong>{{ $department->makerUsersCount }}</strong>
                                </div>
                                <div class="flex-1">
                                    Checkers: <strong>{{ $department->checkerUsersCount }}</strong>
                                </div>
                            </div>
                        </div>
                    </x-card>
                @endforeach
            </div>
            {{-- <x-card no-padding>
                <x-table.table>
                    <thead>
                        <tr>
                            <x-table.thead>Department Name</x-table.thead>
                            <x-table.thead>Logo</x-table.thead>
                            <x-table.thead></x-table.thead>
                        </tr>
                    </thead>
                    <tbody wire:loading.class="opacity-25 base-spinner">
                        @foreach($departments as $department)
                            <tr>
                                <x-table.tdata>
                                    {{ $department->name }}
                                </x-table.tdata>
                                <x-table.tdata>
                                    N/A
                                </x-table.tdata>
                                <x-table.tdata>
                                    <x-link href="{{ route('departments.edit', $department->id) }}">Edit</x-link>
                                </x-table.tdata>
                            </tr>
                        @endforeach
                    </tbody>
                </x-table.table>
            </x-card> --}}
        @else 
            <x-card-empty />
        @endif

        <livewire:departments.create />
    </x-section-centered>
</div>
