<div>
    <div class="bg-gray-100">
        <x-slot name="secondaryTopbar">
            <x-navbar-top-transparent class="bg-cyan-800 py-4">
                <x-slot:beforeTitle>
                    <x-link class="!text-cyan-100 items-center" href="{{ route('front.tenders.show', $tenderId) }}"><x-icon-arrow-left class="w-5 h-5 mr-1" />Back</x-link>  
                </x-slot>

                <x-slot name="title" class="text-gray-100">Apply for Tender No: {{ $tenderNumber }}</x-slot>
    
                <x-slot name="action" class="text-indigo-100">
                    Need help? Contact us <br>
                    <EMAIL>
                </x-slot>
            </x-navbar-top-transparent>
        </x-slot>
 
        <x-section-centered class="pt-6 pb-16">
            
            {{-- <livewire:front.tender-document-payment 
                :tenderId="$tenderId" 
                :tenderFee="$tenderFee"
            /> --}}

            @if ($this->hasPaidTenderFee())
                <x-card form-action="save">
                    <x-card-form :with-shadow="false" no-padding>
                        <x-slot name="title">Basic Details of Company</x-slot>
                        <x-slot name="description">Add company's name, email, phone and so on.</x-slot>
                        
                        <x-input 
                            label="Company Name"
                            name="name"
                            wire:model.defer="name"
                        />

                        <x-input 
                            type="email"
                            label="Company Email"
                            name="email"
                            wire:model.defer="email"
                        />

                        <x-input-number 
                            label="Company Phone"
                            name="phone"
                            wire:model.defer="phone"
                            input-mode="numeric"
                            maxlength="10"
                            minlength="10"
                        />

                    </x-card-form>
                    
                    @if (count($tenderdocuments) > 0)
                        <x-section-border />

                        <x-card-form class="shadow-none" no-padding>
                            <x-slot name="title">Tender Documents</x-slot>
                            <x-slot name="description">Documents to be uploaded for the tendering process.</x-slot>

                                @foreach ($tenderdocuments as $documentIndex => $document)
                                    <input 
                                        type="hidden" 
                                        wire:model.defer="tenderdocuments.{{ $documentIndex }}.documentName"
                                        name="tenderdocuments.{{ $documentIndex }}.documentName"
                                    />
                                    <x-filepond 
                                        label="{{ $document['documentName'] }}"
                                        name="tenderdocuments.{{ $documentIndex }}.document"
                                        wire:model="tenderdocuments.{{ $documentIndex }}.document"
                                        accept-files="{{ $document['type'] }}"
                                        max-file-size="{{ $document['size'] }}"
                                    />
                                @endforeach
                        </x-card-form>
                    @endif

                    @if (count($bidprices))
                        <x-section-border />
        
                        <x-card-form class="shadow-none" no-padding>
                            <x-slot name="title">Tender Bidding</x-slot>
                            <x-slot name="description"></x-slot>

                            <div class="divide-y">
                                @foreach ($bidprices as $itemIndex => $item)
                                    <div class="flex py-3">
                                        <div class="flex-1">
                                            <x-heading size="md">{{ $item['item_type'] }}</x-heading>
                                            <div class="text-sm">{{ $item['quantity'] }} ({{ $item['unit'] }})</div>
                                            <div class="text-sm">Estimated Price: @inr($item['estimated_price'])</div>
                                        </div>
                                        <div class="flex-1">
                                            <input 
                                                type="hidden" 
                                                wire:model.defer="bidprices.{{ $itemIndex }}.id"
                                                name="bidprices.{{ $itemIndex }}.id"
                                            />
                                            <input 
                                                type="hidden" 
                                                wire:model.defer="bidprices.{{ $itemIndex }}.quantity"
                                                name="bidprices.{{ $itemIndex }}.quantity"
                                            />
                                            <x-cleavejs
                                                append-before="&#8377;"
                                                class="pl-6"
                                                no-margin
                                                label="Rate"
                                                name="bidprices.{{ $itemIndex }}.price"
                                                wire:model.defer="bidprices.{{ $itemIndex }}.price"
                                            />
                                        </div>
                                    </div>  
                                @endforeach
                            </div>
                        </x-card-form>
                    @endif

                    <x-slot name="footer" class="flex items-center justify-end">
                        <x-button type="submit" with-spinner>Apply Tender</x-button>
                    </x-slot>
                </x-card>
            @else
                <x-card-empty>
                    <p>Please pay the tender document to download.</p>
                </x-card-empty>
            @endif
        </x-section-centered>
    </div>
</div>
