<div>
    <div class="bg-gray-100">
        <div class="h-32 bg-cyan-800"></div>

        <x-section-centered class="pb-6 -mt-24">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-y-3 md:gap-6">
                <div class="md:col-span-2">
                    <x-heading color="text-white" class="font-semibold" size="2xl">Latest Tenders</x-heading>
                </div>

                <div class="flex items-centers space-x-2">
                    <x-input-search name="search" placeholder="Search tenders..." wire:model.debounce.500ms="search" />

                    <div class="flex-1">
                        <x-select name="options" class="py-1.5 md:py-2" wire:model="filter">
                            <option value="">All tenders</option>
                            <option value="today">Today</option>
                            <option value="last-24">Last 24 hours</option>
                            <option value="yesterday">Yesterday</option>
                            <option value="this-week">This Week</option>
                            <option value="last-7">Last 7 days</option>
                            <option value="this-month">This month</option>
                            <option value="last-30">Last 30 days</option>
                            <option value="last-90">Last 90 days</option>
                        </x-select>
                    </div>
                </div>
            </div>

            @if($tenders->isNotEmpty())
                <div class="space-y-4">
                    @foreach($tenders as $tender)
                        <x-card card-classes="relative pt-3">
                            @if ($tender->is_tender_new === true)
                                <x-ribbon width="w-20">New</x-ribbon>
                            @endif

                            <div class="md:flex md:flex-wrap">
                                <div class="md:flex-1">
                                    <div>
                                        <x-heading>
                                            <a class="hover:text-cyan-600 hover:underline" href="{{ route('front.tenders.show', $tender->id) }}">{{ $tender->tender_title }}</a>
                                        </x-heading>
                                        <div class="my-2 grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                                            <div class="col-span-full md:col-span-2">
                                                {{ $tender->tender_called_by }} <br>
                                                {{ $tender->tender_address }}
                                            </div>

                                            <div class="col-span-full md:col-span-2">
                                                <div class="text-gray-500 text-sm">Reference No.</div>
                                                <x-heading size="md">{{ $tender->tender_number }}</x-heading>
                                            </div>

                                            <div>
                                                <div class="text-gray-500 text-sm">Category</div>
                                                <x-heading size="md">{{ Str::title($tender->tender_category) }}</x-heading>
                                            </div>

                                            <div>
                                                <div class="text-gray-500 text-sm">Type</div>
                                                <x-heading size="md">{{ $tender->tender_type }}</x-heading>
                                            </div>

                                            <div>
                                                <div class="text-gray-500 text-sm">Published Date</div>
                                                <x-heading size="md">@dateWithTime($tender->published_date)</x-heading>
                                            </div>

                                            <div>
                                                <div class="text-gray-500 text-sm">Last Date of Submission</div>
                                                <x-heading size="md">@dateWithTime($tender->last_date_of_submission)</x-heading>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </x-card>
                    @endforeach
                </div>

                <div class="mt-5">{{ $tenders->links() }}</div>
            @else
                <x-card-empty />
            @endif
        </x-section-centered>
    </div>
</div>