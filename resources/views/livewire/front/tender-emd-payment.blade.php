<div>
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent class="bg-cyan-800 py-4 relative">
            <x-slot:beforeTitle>
                <x-link class="!text-cyan-100 items-center" href="{{ route('front.tenders.show', $tenderId) }}"><x-icon-arrow-left class="w-5 h-5 mr-1" />Back</x-link>
            </x-slot>

            <x-slot name="title" class="text-gray-100">EMD payment for Tender No: {{ $tenderNumber }}</x-slot>

            <x-slot name="action" class="text-cyan-100">
                Need help? Contact us <br>
                <EMAIL>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <div class="bg-slate-100">
        <x-section-centered class="pt-6 pb-16">

            @if(session('success'))
                <x-alert variant="success" class="mb-6">{{ session('success') }}</x-alert>
            @elseif(session('error'))
                <x-alert variant="danger" class="mb-6">{{ session('error') }}</x-alert>
            @endif

            @if($emdPayment && $emdPayment?->status)
                <x-card card-classes="mb-6">
                    <x-card-form :with-shadow="false" no-padding>
                        <x-slot name="title">EMD Payment Status</x-slot>
                        <x-slot name="description">Current status of your EMD payment for this tender.</x-slot>

                        <div class="mb-4 p-4 rounded-lg {{ $emdPayment->status->statusLabel() === 'Paid' ? 'bg-green-50 border border-green-200' : ($emdPayment->status->statusLabel() === 'Failed' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200') }}">
                            <h4 class="font-medium {{ $emdPayment->status->statusLabel() === 'Paid' ? 'text-green-800' : ($emdPayment->status->statusLabel() === 'Failed' ? 'text-red-800' : 'text-yellow-800') }} mb-2">
                                EMD Payment Status: {{ $emdPayment->status->statusLabel() }}
                            </h4>
                            <x-description-list>
                                <x-description-list.item>
                                    <x-slot name="title">Payment Reference No.</x-slot>
                                    <x-slot name="description">{{ $emdPayment->razorpay_payment_id ?? $emdPayment->payment_ref_no ?? 'N/A' }}</x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Amount</x-slot>
                                    <x-slot name="description">@inr($emdPayment->amount)</x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Status</x-slot>
                                    <x-slot name="description">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $emdPayment->status->statusLabel() === 'Paid' ? 'bg-green-100 text-green-800' : ($emdPayment->status->statusLabel() === 'Failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                            {{ $emdPayment->status->statusLabel() }}
                                        </span>
                                    </x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Payment Mode</x-slot>
                                    <x-slot name="description">{{ $emdPayment->payment_type?->value ?? 'Online' }}</x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Payment Date</x-slot>
                                    <x-slot name="description">{{ $emdPayment->payment_at?->format('d M Y, h:i A') ?? $emdPayment->created_at->format('d M Y, h:i A') }}</x-slot>
                                </x-description-list.item>
                            </x-description-list>

                            @if($emdPayment->status->statusLabel() === 'Failed')
                                <div class="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
                                    <p class="text-red-800 text-sm">
                                        <strong>EMD Payment Failed:</strong> You can try making the payment again using the form below.
                                    </p>
                                </div>
                            @endif
                        </div>
                    </x-card-form>
                </x-card>
            @endif

            <x-card-form>
                <x-slot name="title">Pay EMD Fee via Online</x-slot>

                <form action="{{ route('front.tenders.emdpayment-rp', $tenderId) }}" method="POST">
                    @csrf

                    <x-money
                        append-before="&#8377;"
                        label="Amount"
                        name="amount"
                        placeholder="0"
                        class="pl-8"
                    />

                    <x-button with-spinner class="py-3 w-full" color="black">Pay EMD Fee</x-button>
                </form>
            </x-card-form>

            <div class="my-10 flex items-center space-x-4">
                <div>Or pay offline by entering the details of the DD below.</div>
                <div class="h-px border-b border-gray-300 flex-1"></div>
            </div>

            <x-card card-classes="mb-6" form-action="save">
                <x-card-form :with-shadow="false" no-padding>
                    <x-slot name="title">Tender EMD Payment</x-slot>
                    <x-slot name="description">Pay your EMD via Demand Draft (DD) and upload a copy of your demand draft.</x-slot>

                    <x-input
                        label="Payee Name"
                        name="payeeName"
                        wire:model.defer="payeeName"
                    />

                    <x-input
                        label="Bank Name"
                        name="bankName"
                        wire:model.defer="bankName"
                    />

                    <x-flatpicker
                        label="Date of Issue"
                        name="issueDate"
                        wire:model.defer="issueDate"
                    />

                    <x-filepond
                        label="Demand Draft Photo"
                        name="ddPhoto"
                        wire:model.defer="ddPhoto"
                    />

                    <x-input
                        label="Challan/Demand Draft No."
                        name="ddNumber"
                        wire:model.defer="ddNumber"
                    />

                    <x-input-number
                        append-before="&#8377;"
                        label="Amount"
                        name="amount"
                        wire:model.defer="amount"
                        class="pl-8 w-40"
                    />
                </x-card-form>

                <x-slot name="footer" class="flex items-center justify-end">
                    <x-button type="submit" with-spinner wire:target="save">Save</x-button>
                </x-slot>
            </x-card>
        </x-section-centered>

    </div>
</div>
