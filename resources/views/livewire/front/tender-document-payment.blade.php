<div>
    <x-slot name="secondaryTopbar">
        <x-navbar-top-transparent class="bg-cyan-800 py-4 relative">
            <x-slot:beforeTitle>
                <x-link class="!text-cyan-100 items-center" href="{{ route('front.tenders.show', $tenderId) }}"><x-icon-arrow-left class="w-5 h-5 mr-1" />Back</x-link>
            </x-slot>

            <x-slot name="title" class="text-gray-100">Document Fee for Tender No: {{ $tenderNumber }}</x-slot>

            <x-slot name="action" class="text-indigo-100">
                Need help? Contact us <br>
                <EMAIL>
            </x-slot>
        </x-navbar-top-transparent>
    </x-slot>

    <div class="bg-slate-100">
        <x-section-centered class="pt-6 pb-16">

            @if(session('payment_success'))
                <div class="bg-white rounded-lg shadow-lg w-full mb-6 p-6">
                    <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-green-800">Payment Successful!</h3>
                                <p class="text-green-700">Your payment has been processed successfully.</p>
                            </div>
                        </div>
                    </div>

                    <h1 class="text-2xl font-bold text-center text-gray-800 mb-6">Payment Details</h1>

                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-700 font-medium">Order ID:</span>
                            <span class="text-gray-900">{{ session('order_id') }}</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-700 font-medium">Payment ID:</span>
                            <span class="text-gray-900">{{ session('payment_id') }}</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-700 font-medium">Amount:</span>
                            <span class="text-xl font-bold">₹{{ session('payment_amount') }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700 font-medium">Payment Date:</span>
                            <span class="text-gray-900">{{ session('payment_date') }}</span>
                        </div>
                    </div>
                </div>
            @elseif(session('success'))
                <x-alert variant="success" class="mb-6">{{ session('success') }}</x-alert>
            @endif

            <x-card card-classes="mb-6">
                <x-card-form :with-shadow="false" no-padding>
                    <x-slot name="title">Tender Fee Payment</x-slot>
                    <x-slot name="description">Tender fee payment to download the documents associated with the tender.</x-slot>

                    @if($documentPaymentdata && $documentPaymentdata?->status)
                        <div class="mb-4 p-4 rounded-lg {{ $documentPaymentdata->status->statusLabel() === 'Paid' ? 'bg-green-50 border border-green-200' : ($documentPaymentdata->status->statusLabel() === 'Failed' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200') }}">
                            <h4 class="font-medium {{ $documentPaymentdata->status->statusLabel() === 'Paid' ? 'text-green-800' : ($documentPaymentdata->status->statusLabel() === 'Failed' ? 'text-red-800' : 'text-yellow-800') }} mb-2">
                                Payment Status: {{ $documentPaymentdata->status->statusLabel() }}
                            </h4>
                            <x-description-list>
                                <x-description-list.item>
                                    <x-slot name="title">Payment Reference No.</x-slot>
                                    <x-slot name="description">{{ $documentPaymentdata->razorpay_payment_id ?? $documentPaymentdata->payment_ref_no ?? 'N/A' }}</x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Amount</x-slot>
                                    <x-slot name="description">@inr($documentPaymentdata->amount / 100)</x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Status</x-slot>
                                    <x-slot name="description">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $documentPaymentdata->status->statusLabel() === 'Paid' ? 'bg-green-100 text-green-800' : ($documentPaymentdata->status->statusLabel() === 'Failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                            {{ $documentPaymentdata->status->statusLabel() }}
                                        </span>
                                    </x-slot>
                                </x-description-list.item>
                                <x-description-list.item>
                                    <x-slot name="title">Payment Mode</x-slot>
                                <x-slot name="description">{{ $documentPaymentdata?->payment_mode }}</x-slot>
                            </x-description-list.item>
                            <x-description-list.item>
                                <x-slot name="title">Payment At</x-slot>
                                <x-slot name="description">{{ $documentPaymentdata?->payment_at?->format('d/m/Y H:i:s') }}</x-slot>
                            </x-description-list.item>
                        </x-description-list>

                        @if($documentPaymentdata->status->statusLabel() === 'Failed')
                            <div class="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
                                <p class="text-red-800 text-sm">
                                    <strong>Payment Failed:</strong> You can try making the payment again by clicking the "Pay now" button below.
                                </p>
                            </div>

                            <div class="mt-4 bg-gray-100 rounded-lg px-4 py-4 flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-2 justify-between">
                                <img class="shrink-0 h-4" src="{{ url('img/pay_methods_branding.png') }}" alt="payment-logo" loading="lazy">
                                <div class="w-full md:w-24">
                                    <x-button tag="a" href="{{ route('front.tenders.documentpayment-rp', $tenderId) }}" with-spinner class="py-3 w-full" color="black">Try Payment Again</x-button>
                                </div>
                            </div>
                        @endif

                        {{-- <p class="mt-4 text-gray-600">Please <x-link href="#">click here</x-link> to download the challan for payment.</p> --}}

                        {{-- <x-button color="black" type="button" class="mt-4" with-spinner wire:target="makeSuccessfulPaymentSimulation" wire:click.prevent="makeSuccessfulPaymentSimulation">Simulate Payment</x-button> --}}
                    @else
                        <x-heading>@inr($tenderFee)</x-heading>
                        {{-- <x-button type="button" wire:click="payTenderFee" with-spinner wire:target="payTenderFee" class=" mt-2">Initiate Payment for Challan</x-button> --}}

                        <div class="mt-4 bg-gray-100 rounded-lg px-4 py-4 flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-2 justify-between">
                            <img class="shrink-0 h-4" src="{{ url('img/pay_methods_branding.png') }}" alt="payment-logo" loading="lazy">
                            <div class="w-full md:w-24">
                                <x-button tag="a" href="{{ route('front.tenders.documentpayment-rp', $tenderId) }}" with-spinner class="py-3 w-full" color="black">Pay now</x-button>
                            </div>
                        </div>
                    @endif
                </x-card-form>

                {{-- <x-slot name="footer">
                    <img class="shrink-0 h-4" src="{{ url('img/pay_methods_branding.png') }}" alt="payment-logo" loading="lazy">
                </x-slot> --}}
            </x-card>
        </x-section-centered>

    </div>
</div>
