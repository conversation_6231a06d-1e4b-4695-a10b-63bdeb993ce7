<div>
    <div class="bg-gray-100 min-h-screen">
        <div class="bg-cyan-800">
            <x-heading size="3xl" color="text-gray-100" class="py-10 px-4 text-center">Company Registration</x-heading>
        </div>
        <div class="pt-10 pb-16 max-w-5xl mx-auto">
            
            <x-banner class="mb-4" />

            <x-card form-action="save">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-10 md:p-10">
                    <div class="md:col-span-3">
                        <x-input
                            label="Company Name"
                            name="name"
                            wire:model.defer="name"
                        />

                        <x-input
                            type="email"
                            label="Company Email"
                            name="email"
                            wire:model.defer="email"
                            hint="Please provide a valid email address, as this will be used in the login process."
                        />
                        
                        <x-input-number
                            label="Company Phone"
                            name="phone"
                            wire:model.defer="phone"
                            input-mode="numeric"
                            maxlength="10"
                            minlength="10"
                            hint="Please provide a valid 10-digit mobile phone number, as this will be the primary means of communication."
                        />

                        <div class="md:w-2/3">
                            <x-input-password 
                                label="Password"
                                type="password"
                                name="password"
                                wire:model.defer="password"
                                {{-- required  --}}
                                autocomplete="new-password" 
                            />
    
                            <x-input
                                label="Password Confirmation" 
                                type="password"
                                name="password_confirmation" 
                                wire:model.defer="password_confirmation"
                                {{-- required  --}}
                            />
                        </div>

                        <p class="text-sm text-gray-500">By registering and clicking the <strong>Create account</strong> button you agree to our <x-link href="#">Terms</x-link> and <x-link href="#">Privacy Policy</x-link>.</p>
                    </div>

                    <div class="md:col-span-2">
                        <div class="p-4 bg-slate-50 rounded-lg">
                            <x-heading size="lg" class="mb-2">Benefits of Registering</x-heading>

                            <div class="flex space-x-3 mb-1">
                                <div class="mt-1 shrink-0 bg-cyan-100 w-4 h-4 rounded-full p-1">
                                    <div class="w-2 h-2 rounded-full bg-cyan-500"></div>
                                </div>

                                <div class="text-gray-600">
                                    Download Tender Documents
                                </div>
                            </div>

                            <div class="flex space-x-3 mb-1">
                                <div class="mt-1 shrink-0 bg-cyan-100 w-4 h-4 rounded-full p-1">
                                    <div class="w-2 h-2 rounded-full bg-cyan-500"></div>
                                </div>

                                <div class="text-gray-600">
                                    Get updates of tender openings.
                                </div>
                            </div>

                            <div class="flex space-x-3 mb-1">
                                <div class="mt-1 shrink-0 bg-cyan-100 w-4 h-4 rounded-full p-1">
                                    <div class="w-2 h-2 rounded-full bg-cyan-500"></div>
                                </div>

                                <div class="text-gray-600">
                                    Can apply for bidding
                                </div>
                            </div>

                            <div class="flex space-x-3 mb-1">
                                <div class="mt-1 shrink-0 bg-cyan-100 w-4 h-4 rounded-full p-1">
                                    <div class="w-2 h-2 rounded-full bg-cyan-500"></div>
                                </div>

                                <div class="text-gray-600">
                                    Get all upcoming tender news.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <x-slot name="footer" class="text-right">
                    <x-button type="submit" class="py-3" with-spinner wire:target="save">Create account</x-button>
                </x-slot>
            </x-card>
          
        </div>
    </div>
</div>
