<!DOCTYPE html>
<html lang="en">
<head>
    <title>Razorpay Order Verification - {{ config('app.name') }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .bg-cyan-800 {
            background-color: #155e75;
        }
        .text-cyan-600 {
            color: #0891b2;
        }
        .hover\:text-cyan-800:hover {
            color: #155e75;
        }
        .log-line {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .json-data {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="bg-slate-100">
        <div class="h-32 bg-cyan-800"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-10 -mt-20">
            <a href="{{ route('front.tenders') }}" class="inline-flex items-center mb-3 text-indigo-100 hover:underline font-medium">
                <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to all tenders
            </a>

            <div class="bg-white rounded-lg shadow-lg w-full mb-6 p-6">
                <h1 class="text-3xl font-bold text-center text-gray-800 mb-6">Razorpay Order Verification</h1>

                <!-- Search Form -->
                <form method="GET" action="{{ route('razorpay.verify') }}" class="mb-6">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <label for="order_id" class="block text-sm font-medium text-gray-700 mb-2">Razorpay Order ID</label>
                            <input type="text" 
                                   id="order_id" 
                                   name="order_id" 
                                   value="{{ $orderId }}" 
                                   placeholder="Enter Razorpay Order ID (e.g., order_xxxxx)"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" 
                                    class="bg-cyan-800 hover:bg-cyan-900 text-white font-bold py-2 px-6 rounded-lg transition duration-200">
                                Verify Order
                            </button>
                        </div>
                    </div>
                </form>

                @if($error)
                    <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-red-800">Error</h3>
                                <p class="text-red-700">{{ $error }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                @if($orderId && !$error)
                    <!-- Order Information -->
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Order Information</h2>
                        
                        @if($paymentData)
                            <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
                                <h3 class="text-lg font-medium text-green-800">Database Record Found</h3>
                                <div class="mt-2 text-green-700">
                                    <p><strong>Payment Type:</strong> {{ ucfirst($paymentType) }} Payment</p>
                                    <p><strong>Amount:</strong> ₹{{ number_format($paymentData->amount/100, 2) }}</p>
                                    <p><strong>Status:</strong> {{ $paymentData->status }}</p>
                                    <p><strong>Payment Reference:</strong> {{ $paymentData->payment_ref_no ?? 'N/A' }}</p>
                                    <p><strong>Payment At:</strong> {{ $paymentData->payment_at ? $paymentData->payment_at->format('d M Y, h:i A') : 'N/A' }}</p>
                                    @if($tender)
                                        <p><strong>Tender:</strong> {{ $tender->tender_number }}</p>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
                                <h3 class="text-lg font-medium text-yellow-800">No Database Record Found</h3>
                                <p class="text-yellow-700">No payment record found in the database for this order ID.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Razorpay Data -->
                    @if($razorpayData)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-4">Razorpay API Response</h2>
                            
                            @if($razorpayData['error'])
                                <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                                    <h3 class="text-lg font-medium text-red-800">Razorpay API Error</h3>
                                    <p class="text-red-700">{{ $razorpayData['error'] }}</p>
                                </div>
                            @else
                                @if($razorpayData['order'])
                                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                                        <h3 class="text-lg font-medium text-blue-800">Order Details</h3>
                                        <div class="mt-2 text-blue-700 json-data">{{ json_encode($razorpayData['order']->toArray(), JSON_PRETTY_PRINT) }}</div>
                                    </div>
                                @endif

                                @if($razorpayData['payments'] && count($razorpayData['payments']->items) > 0)
                                    <div class="bg-purple-50 border-l-4 border-purple-500 p-4 mb-4">
                                        <h3 class="text-lg font-medium text-purple-800">Payment Details</h3>
                                        <div class="mt-2 text-purple-700 json-data">{{ json_encode($razorpayData['payments']->toArray(), JSON_PRETTY_PRINT) }}</div>
                                    </div>
                                @endif
                            @endif
                        </div>
                    @endif

                    <!-- Application Logs -->
                    @if(count($logs) > 0)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-4">Application Logs</h2>
                            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto">
                                @foreach($logs as $log)
                                    <div class="log-line mb-1">{{ $log }}</div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        @if($orderId)
                            <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
                                <h3 class="text-lg font-medium text-yellow-800">No Logs Found</h3>
                                <p class="text-yellow-700">No application logs found for this order ID.</p>
                            </div>
                        @endif
                    @endif
                @endif
            </div>
        </div>
    </div>
</body>
</html>
