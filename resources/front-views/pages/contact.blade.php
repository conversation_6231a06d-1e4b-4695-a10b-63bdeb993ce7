<x-front-layout title="Contact">

       <div class="bg-slate-50 py-24 border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            <x-heading size="4xl" class="mb-2 tracking-tight text-slate-800">Contact us</x-heading>
            <p class="text-slate-600 text-lg mb-16">Our friendly team would love to hear from you!</p>

            {{-- <h2 class="text-center mb-10 md:mb-16 text-4xl font-normal tracking-tight text-slate-800 relative font-serif">We'd love to hear from you</h2> --}}

            <div class="grid grid-cols-1 md:grid-cols-3 gap-10 w-full">
                <div>
                    <svg class="text-cyan-600 w-10 h-10 mb-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        <path opacity="0.4" d="M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>

                    <h3 class="text-lg font-semibold mb-2 text-slate-800">Email Support</h3>
                    <p class="text-slate-600 mb-3">Our friendly team is here to help.</p>
                    <a href="mailto:<EMAIL>" class="inline-block text-cyan-600 hover:underline"><EMAIL></a>
                </div>

                <div>
                    <svg class="text-cyan-600 w-10 h-10 mb-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.4" d="M12.0009 13.4299C13.724 13.4299 15.1209 12.0331 15.1209 10.3099C15.1209 8.58681 13.724 7.18994 12.0009 7.18994C10.2777 7.18994 8.88086 8.58681 8.88086 10.3099C8.88086 12.0331 10.2777 13.4299 12.0009 13.4299Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M3.61971 8.49C5.58971 -0.169998 18.4197 -0.159997 20.3797 8.5C21.5297 13.58 18.3697 17.88 15.5997 20.54C13.5897 22.48 10.4097 22.48 8.38971 20.54C5.62971 17.88 2.46971 13.57 3.61971 8.49Z" stroke="currentColor" stroke-width="1.5"/>
                    </svg>

                    <h3 class="text-lg font-semibold mb-2 text-slate-800">Visit our Office HQ</h3>
                    <p class="text-slate-600">
                        IT Dept. Dima Hasao Autonomus Council <br>
                        Council office. Halflong,
                    </p>
                </div>

                <div>
                    <svg class="text-cyan-600 w-10 h-10 mb-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"/>
                        <path opacity="0.4" d="M18.5 9C18.5 8.4 18.03 7.48 17.33 6.73C16.69 6.04 15.84 5.5 15 5.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path opacity="0.4" d="M22 9C22 5.13 18.87 2 15 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>

                    <h3 class="text-lg font-semibold mb-2 text-slate-800">Phone</h3>
                    <p class="text-slate-600 mb-3">Mon-Fri from 9am to 5pm</p>
                    <a href="tel:+919340075500" class="inline-block text-cyan-600 hover:underline">+91 9340075500</a>
                </div>
            </div>
        </div>
    </div>
    <div class="bg-white">
        <div class="max-w-full w-full h-96" id="contact-map"></div>

        {{-- <div class="flex flex-col md:flex-row h-full"> --}}
            {{-- <div class="flex-1 py-16 md:pb-32">
                <div class="md:max-w-xl md:mx-auto px-4 sm:px-6 lg:px-8">
                    <x-heading size="4xl" class="mb-4 tracking-tight text-slate-800">Contact us</x-heading>
                    <p class="text-slate-500 text-lg mb-8">Our friendly team would love to hear from you!</p>

                    <div>
                        <div class="mb-5">
                            <label for="name" class="font-medium text-slate-700 mb-1 block text-sm">Fullname</label>
                            <input type="text" name="name" id="name" class="form-input transition duration-150 ease-in-out px-3 py-2 block w-full text-gray-700 font-sans rounded-lg text-left focus:outline-none focus:border-cyan-500 focus:ring-cyan-500 shadow-sm border sm:text-sm placeholder-gray-400 disabled:bg-gray-50 border-gray-300 bg-white"
                                placeholder="Martin Joe"
                            />
                        </div>

                        <div class="mb-5">
                            <label for="email" class="font-medium text-slate-700 mb-1 block text-sm">Email</label>
                            <input type="email" name="email" id="email" class="form-input transition duration-150 ease-in-out px-3 py-2 block w-full text-gray-700 font-sans rounded-lg text-left focus:outline-none focus:border-cyan-500 focus:ring-cyan-500 shadow-sm border sm:text-sm placeholder-gray-400 disabled:bg-gray-50 border-gray-300 bg-white"
                                placeholder="<EMAIL>"
                            />
                        </div>

                        <div class="mb-5">
                            <label for="phone" class="font-medium text-slate-700 mb-1 block text-sm">Phone</label>
                            <div class="text-sm mb-1 text-slate-500">Enter a valid 10 digit mobile number.</div>
                            <input type="text" name="phone" id="phone" class="form-input transition duration-150 ease-in-out px-3 py-2 block w-full text-gray-700 font-sans rounded-lg text-left focus:outline-none focus:border-cyan-500 focus:ring-cyan-500 shadow-sm border sm:text-sm placeholder-gray-400 disabled:bg-gray-50 border-gray-300 bg-white"
                                placeholder="00000 00000"
                            />
                        </div>

                        <div class="mb-5">
                            <label for="message" class="font-medium text-slate-700 mb-1 block text-sm">Message</label>
                            <textarea rows="4" name="message" id="message" class="form-input transition duration-150 ease-in-out px-3 py-2 block w-full text-gray-700 font-sans rounded-lg text-left focus:outline-none focus:border-cyan-500 focus:ring-cyan-500 shadow-sm border sm:text-sm placeholder-gray-400 disabled:bg-gray-50 border-gray-300 bg-white"></textarea>
                        </div>

                        <button type="button" class="mt-4 font-medium transition duration-150 ease-in-out bg-gray-700 text-white px-4 py-2 hover:bg-gray-800 rounded-md truncate w-full">Send message</button>
                    </div>
                </div>
            </div> --}}

            {{-- <div class="md:w-2/5 bg-slate-50"> --}}
                {{-- <div class="max-w-full w-full h-96 md:min-h-screen" id="contact-map"></div> --}}
            {{-- </div> --}}
        {{-- </div> --}}
    </div>

    @once
    @push('styles')
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
            integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
            crossorigin=""/>
    @endpush

    @push('scripts-bottom')
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"
            integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA=="
            crossorigin="anonymous"></script>

    <script src="/js/TileLayer.Grayscale.js"></script>
    <script>
        const map = L.map('contact-map', { zoomControl: false }).setView([25.161203, 93.017995], 15);
        
        L.control.zoom({position: 'topright'}).addTo(map);
     
        L.tileLayer.grayscale('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
 
        const icon = L.icon({
            iconUrl: "{{ url('img/location-marker.svg') }}",
            iconSize: [32,32],
        });
  
        L.marker([25.161203, 93.017995], {
            icon: icon
        }).addTo(map).bindPopup(`<div class="text-xs">IT Dept. Dima Hasao Autonomus Council, <br>Council office. Halflong,</div>`);    
    </script>
    @endpush
    @endonce
</x-front-layout>

