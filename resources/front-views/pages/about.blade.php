<x-front-layout title="About">
    <div class="bg-white flex flex-col items-center justify-center relative py-40 px-4">
        <div class="absolute right-0 top-0 h-64 w-64 w-full rounded-full bg-gradient-90 from-indigo-100 via-purple-200 to-pink-400 blur-md opacity-20"></div>
        <h1 class="text-5xl font-normal tracking-tight text-slate-800 relative font-serif">About Freshman</h1>
        <p class="relative text-slate-500 text-xl mt-6 text-center md:max-w-xl md:mx-auto">A starter framework for rapid prototyping built on top of Laravel Breeze and the TALL stack.</p>
       
		<div class="mt-10">
			<div class="grid grid-cols-1 md:grid-cols-3 gap-10 w-full md:max-w-6xl md:mx-auto px-4">
				<div class="backdrop-blur-lg bg-white/25 rounded-lg p-6 text-center">
					<h2 class="text-6xl font-normal mb-2 text-indigo-600 font-serif">200+</h3>
					<h3 class="text-3xl font-normal mb-2 text-slate-700 font-serif">projects completed</h3>
					{{-- <p class="text-slate-600">We've build over 10 plus project using the framework.</p> --}}
				</div>

				<div class="backdrop-blur-lg bg-white/25 rounded-lg p-6 text-center">
					<h2 class="text-6xl font-normal mb-2 text-indigo-600 font-serif">100+</h3>
					<h3 class="text-3xl font-normal mb-2 text-slate-700 font-serif">5-star reviews</h3>
					{{-- <p class="text-slate-600">We're proud of our 5-star rating from developers.</p> --}}
				</div>

				<div class="backdrop-blur-lg bg-white/25 rounded-lg p-6 text-center">
					<h2 class="text-6xl font-normal mb-2 text-indigo-600 font-serif">10k</h3>
					<h3 class="text-3xl font-normal mb-2 text-slate-700 font-serif">global downloads</h3>
					{{-- <p class="text-slate-600">Our frameworks has been downloaded over 10k times.</p> --}}
				</div>
			</div>
		</div>

		<div class="w-full h-20 md:h-40 text-indigo-200/50 inset-x-0 bottom-0 absolute z-10" style="background-image: linear-gradient(currentColor 1px, transparent 1px), linear-gradient(to right, currentColor 1px, transparent 1px); background-size: 40px 40px;"></div>
        <div class="w-full h-20 md:h-40 inset-x-0 bottom-0 absolute z-20 bg-gradient-to-br from-white"></div>
        <div class="w-full h-20 md:h-40 inset-x-0 bottom-0 absolute z-20 bg-gradient-to-bl from-white"></div>
    </div>

	<div class="bg-slate-50 py-24 border-t border-b">
	 	<div class="md:max-w-7xl md:mx-auto px-4 sm:px-6 md:px-8">
			<h2 class="mb-10 text-4xl font-normal tracking-tight text-slate-800 relative font-serif">We're making this framework bigger &amp; better.</h2>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-10 w-full">
				<div>
					<p class="text-slate-600 mb-4">Lorem ipsum dolor sit amet consectetur adipisicing elit. Nulla necessitatibus eos harum repellendus possimus quibusdam ipsa nam! Magnam nemo aut quae quisquam dignissimos praesentium quasi est minima numquam aspernatur illum natus, necessitatibus, fugiat dolor quos reprehenderit! Voluptate est ipsum officiis asperiores? Sed dolorum aperiam laboriosam accusamus dignissimos, soluta laborum porro?</p>

					<p class="text-slate-600">Lorem ipsum dolor sit amet consectetur adipisicing elit. Nulla necessitatibus eos harum repellendus possimus quibusdam ipsa nam! Magnam nemo aut quae quisquam dignissimos praesentium quasi est minima numquam aspernatur illum natus, necessitatibus, fugiat dolor quos reprehenderit! Voluptate est ipsum officiis asperiores? Sed dolorum aperiam laboriosam accusamus dignissimos, soluta laborum porro?</p>
				</div>

				<div>
					<p class="text-slate-600 mb-4">Lorem ipsum dolor sit amet consectetur adipisicing elit. Totam amet officia ullam, est, doloremque sint inventore quae voluptates expedita illum hic accusamus, rerum aspernatur aliquam? Nesciunt repellat quas quasi pariatur, eum perferendis quam ratione aliquam!</p>

					<p class="text-slate-600">Lorem ipsum dolor sit amet consectetur adipisicing elit. Nulla necessitatibus eos harum repellendus possimus quibusdam ipsa nam! Magnam nemo aut quae quisquam dignissimos praesentium quasi est minima numquam aspernatur illum natus, necessitatibus, fugiat dolor quos reprehenderit! Voluptate est ipsum officiis asperiores? Sed dolorum aperiam laboriosam accusamus dignissimos, soluta laborum porro?</p>
				</div>
			</div>
		</div>
    </div>

	<div class="bg-white py-24">
	 	<div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
			<h2 class="mb-10 text-4xl font-normal tracking-tight text-slate-800 relative font-serif">Meet our team</h2>

			<div class="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-10 w-full">
				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">John Doe</h3>
					<p class="text-slate-500 text-sm">Founder &amp; CEO</p>
				</div>

				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">Winter Doe</h3>
					<p class="text-slate-500 text-sm">Product Manager</p>
				</div>

				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">Summer Doe</h3>
					<p class="text-slate-500 text-sm">UX Researcher</p>
				</div>

				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">April Doe</h3>
					<p class="text-slate-500 text-sm">Product Designer</p>
				</div>

				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">Tom Doe</h3>
					<p class="text-slate-500 text-sm">Web Developer</p>
				</div>

				<div>
					<div class="bg-slate-100 h-64 mb-2"></div>
					<h3 class="font-semibold text-slate-800">Jim Doe</h3>
					<p class="text-slate-500 text-sm">Web developer</p>
				</div>
			</div>
		</div>
    </div>

	<div class="bg-slate-50 py-24 border-t border-b">
	 	<div class="md:max-w-2xl md:mx-auto px-4">
			<h2 class="mb-2 text-4xl font-normal tracking-tight text-slate-800 relative font-serif">We're looking for talented people</h2>
			<p class="text-slate-600 mb-10">We're a 100% remote team spread across all the world. Join us!</p>

			<div class="space-y-6">
				<div class="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 w-full bg-white border rounded-lg p-5">
					<div class="md:col-span-4">
						<div class="text-indigo-600 text-sm">Design</div>
						<h3 class="font-semibold text-slate-800 mb-1">Product Designer</h3>
						<p class="text-slate-600 mb-2.5">We're looking for a mid-level product designer to join our team.</p>
						<div class="flex space-x-4 items-center">
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M12.0009 14.1699C9.87086 14.1699 8.13086 12.4399 8.13086 10.2999C8.13086 8.15994 9.87086 6.43994 12.0009 6.43994C14.1309 6.43994 15.8709 8.16994 15.8709 10.3099C15.8709 12.4499 14.1309 14.1699 12.0009 14.1699ZM12.0009 7.93994C10.7009 7.93994 9.63086 8.99994 9.63086 10.3099C9.63086 11.6199 10.6909 12.6799 12.0009 12.6799C13.3109 12.6799 14.3709 11.6199 14.3709 10.3099C14.3709 8.99994 13.3009 7.93994 12.0009 7.93994Z" fill="currentColor"/>
								<path d="M11.9997 22.76C10.5197 22.76 9.02969 22.2 7.86969 21.09C4.91969 18.25 1.65969 13.72 2.88969 8.33C3.99969 3.44 8.26969 1.25 11.9997 1.25C11.9997 1.25 11.9997 1.25 12.0097 1.25C15.7397 1.25 20.0097 3.44 21.1197 8.34C22.3397 13.73 19.0797 18.25 16.1297 21.09C14.9697 22.2 13.4797 22.76 11.9997 22.76ZM11.9997 2.75C9.08969 2.75 5.34969 4.3 4.35969 8.66C3.27969 13.37 6.23969 17.43 8.91969 20C10.6497 21.67 13.3597 21.67 15.0897 20C17.7597 17.43 20.7197 13.37 19.6597 8.66C18.6597 4.3 14.9097 2.75 11.9997 2.75Z" fill="currentColor"/>
								</svg>
								<div class="text-sm text-slate-500">Remote</div>
							</div>
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z" fill="currentColor"/>
									<path d="M15.7096 15.93C15.5796 15.93 15.4496 15.9 15.3296 15.82L12.2296 13.97C11.4596 13.51 10.8896 12.5 10.8896 11.61V7.51001C10.8896 7.10001 11.2296 6.76001 11.6396 6.76001C12.0496 6.76001 12.3896 7.10001 12.3896 7.51001V11.61C12.3896 11.97 12.6896 12.5 12.9996 12.68L16.0996 14.53C16.4596 14.74 16.5696 15.2 16.3596 15.56C16.2096 15.8 15.9596 15.93 15.7096 15.93Z" fill="currentColor"/>
								</svg>
								<div class="text-slate-500 text-sm">Full-time</div>
							</div>
						</div>
					</div>
					<div class="md:text-right">
						<a href="#" class="text-indigo-600 hover:underline inline-flex items-center text-sm">View job
							<svg class="h-4 w-4 -rotate-45 ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M14.4291 18.8201C14.2391 18.8201 14.0491 18.7501 13.8991 18.6001C13.6091 18.3101 13.6091 17.8301 13.8991 17.5401L19.4391 12.0001L13.8991 6.46012C13.6091 6.17012 13.6091 5.69012 13.8991 5.40012C14.1891 5.11012 14.6691 5.11012 14.9591 5.40012L21.0291 11.4701C21.3191 11.7601 21.3191 12.2401 21.0291 12.5301L14.9591 18.6001C14.8091 18.7501 14.6191 18.8201 14.4291 18.8201Z" fill="currentColor"/>
							<path d="M20.33 12.75H3.5C3.09 12.75 2.75 12.41 2.75 12C2.75 11.59 3.09 11.25 3.5 11.25H20.33C20.74 11.25 21.08 11.59 21.08 12C21.08 12.41 20.74 12.75 20.33 12.75Z" fill="currentColor"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 w-full bg-white border rounded-lg p-5">
					<div class="md:col-span-4">
						<div class="text-indigo-600 text-sm">Software Development</div>
						<h3 class="font-semibold text-slate-800 mb-1">Engineering Manager</h3>
						<p class="text-slate-600 mb-2.5">We're looking for a experienced engineering manager to join our team.</p>
						<div class="flex space-x-4 items-center">
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M12.0009 14.1699C9.87086 14.1699 8.13086 12.4399 8.13086 10.2999C8.13086 8.15994 9.87086 6.43994 12.0009 6.43994C14.1309 6.43994 15.8709 8.16994 15.8709 10.3099C15.8709 12.4499 14.1309 14.1699 12.0009 14.1699ZM12.0009 7.93994C10.7009 7.93994 9.63086 8.99994 9.63086 10.3099C9.63086 11.6199 10.6909 12.6799 12.0009 12.6799C13.3109 12.6799 14.3709 11.6199 14.3709 10.3099C14.3709 8.99994 13.3009 7.93994 12.0009 7.93994Z" fill="currentColor"/>
								<path d="M11.9997 22.76C10.5197 22.76 9.02969 22.2 7.86969 21.09C4.91969 18.25 1.65969 13.72 2.88969 8.33C3.99969 3.44 8.26969 1.25 11.9997 1.25C11.9997 1.25 11.9997 1.25 12.0097 1.25C15.7397 1.25 20.0097 3.44 21.1197 8.34C22.3397 13.73 19.0797 18.25 16.1297 21.09C14.9697 22.2 13.4797 22.76 11.9997 22.76ZM11.9997 2.75C9.08969 2.75 5.34969 4.3 4.35969 8.66C3.27969 13.37 6.23969 17.43 8.91969 20C10.6497 21.67 13.3597 21.67 15.0897 20C17.7597 17.43 20.7197 13.37 19.6597 8.66C18.6597 4.3 14.9097 2.75 11.9997 2.75Z" fill="currentColor"/>
								</svg>
								<div class="text-sm text-slate-500">Remote</div>
							</div>
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z" fill="currentColor"/>
									<path d="M15.7096 15.93C15.5796 15.93 15.4496 15.9 15.3296 15.82L12.2296 13.97C11.4596 13.51 10.8896 12.5 10.8896 11.61V7.51001C10.8896 7.10001 11.2296 6.76001 11.6396 6.76001C12.0496 6.76001 12.3896 7.10001 12.3896 7.51001V11.61C12.3896 11.97 12.6896 12.5 12.9996 12.68L16.0996 14.53C16.4596 14.74 16.5696 15.2 16.3596 15.56C16.2096 15.8 15.9596 15.93 15.7096 15.93Z" fill="currentColor"/>
								</svg>
								<div class="text-slate-500 text-sm">Full-time</div>
							</div>
						</div>
					</div>
					<div class="md:text-right">
						<a href="#" class="text-indigo-600 hover:underline inline-flex items-center text-sm">View job
							<svg class="h-4 w-4 -rotate-45 ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M14.4291 18.8201C14.2391 18.8201 14.0491 18.7501 13.8991 18.6001C13.6091 18.3101 13.6091 17.8301 13.8991 17.5401L19.4391 12.0001L13.8991 6.46012C13.6091 6.17012 13.6091 5.69012 13.8991 5.40012C14.1891 5.11012 14.6691 5.11012 14.9591 5.40012L21.0291 11.4701C21.3191 11.7601 21.3191 12.2401 21.0291 12.5301L14.9591 18.6001C14.8091 18.7501 14.6191 18.8201 14.4291 18.8201Z" fill="currentColor"/>
							<path d="M20.33 12.75H3.5C3.09 12.75 2.75 12.41 2.75 12C2.75 11.59 3.09 11.25 3.5 11.25H20.33C20.74 11.25 21.08 11.59 21.08 12C21.08 12.41 20.74 12.75 20.33 12.75Z" fill="currentColor"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 w-full bg-white border rounded-lg p-5">
					<div class="md:col-span-4">
						<div class="text-indigo-600 text-sm">Software Development</div>
						<h3 class="font-semibold text-slate-800 mb-1">Frontend Developer</h3>
						<p class="text-slate-600 mb-2.5">We're looking for a experienced frontsend developer to join our team.</p>
						<div class="flex space-x-4 items-center">
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M12.0009 14.1699C9.87086 14.1699 8.13086 12.4399 8.13086 10.2999C8.13086 8.15994 9.87086 6.43994 12.0009 6.43994C14.1309 6.43994 15.8709 8.16994 15.8709 10.3099C15.8709 12.4499 14.1309 14.1699 12.0009 14.1699ZM12.0009 7.93994C10.7009 7.93994 9.63086 8.99994 9.63086 10.3099C9.63086 11.6199 10.6909 12.6799 12.0009 12.6799C13.3109 12.6799 14.3709 11.6199 14.3709 10.3099C14.3709 8.99994 13.3009 7.93994 12.0009 7.93994Z" fill="currentColor"/>
								<path d="M11.9997 22.76C10.5197 22.76 9.02969 22.2 7.86969 21.09C4.91969 18.25 1.65969 13.72 2.88969 8.33C3.99969 3.44 8.26969 1.25 11.9997 1.25C11.9997 1.25 11.9997 1.25 12.0097 1.25C15.7397 1.25 20.0097 3.44 21.1197 8.34C22.3397 13.73 19.0797 18.25 16.1297 21.09C14.9697 22.2 13.4797 22.76 11.9997 22.76ZM11.9997 2.75C9.08969 2.75 5.34969 4.3 4.35969 8.66C3.27969 13.37 6.23969 17.43 8.91969 20C10.6497 21.67 13.3597 21.67 15.0897 20C17.7597 17.43 20.7197 13.37 19.6597 8.66C18.6597 4.3 14.9097 2.75 11.9997 2.75Z" fill="currentColor"/>
								</svg>
								<div class="text-sm text-slate-500">Remote</div>
							</div>
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z" fill="currentColor"/>
									<path d="M15.7096 15.93C15.5796 15.93 15.4496 15.9 15.3296 15.82L12.2296 13.97C11.4596 13.51 10.8896 12.5 10.8896 11.61V7.51001C10.8896 7.10001 11.2296 6.76001 11.6396 6.76001C12.0496 6.76001 12.3896 7.10001 12.3896 7.51001V11.61C12.3896 11.97 12.6896 12.5 12.9996 12.68L16.0996 14.53C16.4596 14.74 16.5696 15.2 16.3596 15.56C16.2096 15.8 15.9596 15.93 15.7096 15.93Z" fill="currentColor"/>
								</svg>
								<div class="text-slate-500 text-sm">Full-time</div>
							</div>
						</div>
					</div>
					<div class="md:text-right">
						<a href="#" class="text-indigo-600 hover:underline inline-flex items-center text-sm">View job
							<svg class="h-4 w-4 -rotate-45 ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M14.4291 18.8201C14.2391 18.8201 14.0491 18.7501 13.8991 18.6001C13.6091 18.3101 13.6091 17.8301 13.8991 17.5401L19.4391 12.0001L13.8991 6.46012C13.6091 6.17012 13.6091 5.69012 13.8991 5.40012C14.1891 5.11012 14.6691 5.11012 14.9591 5.40012L21.0291 11.4701C21.3191 11.7601 21.3191 12.2401 21.0291 12.5301L14.9591 18.6001C14.8091 18.7501 14.6191 18.8201 14.4291 18.8201Z" fill="currentColor"/>
							<path d="M20.33 12.75H3.5C3.09 12.75 2.75 12.41 2.75 12C2.75 11.59 3.09 11.25 3.5 11.25H20.33C20.74 11.25 21.08 11.59 21.08 12C21.08 12.41 20.74 12.75 20.33 12.75Z" fill="currentColor"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 w-full bg-white border rounded-lg p-5">
					<div class="md:col-span-4">
						<div class="text-indigo-600 text-sm">Sales</div>
						<h3 class="font-semibold text-slate-800 mb-1">Account Executive</h3>
						<p class="text-slate-600 mb-2.5">We're looking for a experienced account executive to join our team.</p>
						<div class="flex space-x-4 items-center">
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M12.0009 14.1699C9.87086 14.1699 8.13086 12.4399 8.13086 10.2999C8.13086 8.15994 9.87086 6.43994 12.0009 6.43994C14.1309 6.43994 15.8709 8.16994 15.8709 10.3099C15.8709 12.4499 14.1309 14.1699 12.0009 14.1699ZM12.0009 7.93994C10.7009 7.93994 9.63086 8.99994 9.63086 10.3099C9.63086 11.6199 10.6909 12.6799 12.0009 12.6799C13.3109 12.6799 14.3709 11.6199 14.3709 10.3099C14.3709 8.99994 13.3009 7.93994 12.0009 7.93994Z" fill="currentColor"/>
								<path d="M11.9997 22.76C10.5197 22.76 9.02969 22.2 7.86969 21.09C4.91969 18.25 1.65969 13.72 2.88969 8.33C3.99969 3.44 8.26969 1.25 11.9997 1.25C11.9997 1.25 11.9997 1.25 12.0097 1.25C15.7397 1.25 20.0097 3.44 21.1197 8.34C22.3397 13.73 19.0797 18.25 16.1297 21.09C14.9697 22.2 13.4797 22.76 11.9997 22.76ZM11.9997 2.75C9.08969 2.75 5.34969 4.3 4.35969 8.66C3.27969 13.37 6.23969 17.43 8.91969 20C10.6497 21.67 13.3597 21.67 15.0897 20C17.7597 17.43 20.7197 13.37 19.6597 8.66C18.6597 4.3 14.9097 2.75 11.9997 2.75Z" fill="currentColor"/>
								</svg>
								<div class="text-sm text-slate-500">Remote</div>
							</div>
							<div class="flex items-center">
								<svg class="w-5 h-5 shrink-0 text-slate-400 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z" fill="currentColor"/>
									<path d="M15.7096 15.93C15.5796 15.93 15.4496 15.9 15.3296 15.82L12.2296 13.97C11.4596 13.51 10.8896 12.5 10.8896 11.61V7.51001C10.8896 7.10001 11.2296 6.76001 11.6396 6.76001C12.0496 6.76001 12.3896 7.10001 12.3896 7.51001V11.61C12.3896 11.97 12.6896 12.5 12.9996 12.68L16.0996 14.53C16.4596 14.74 16.5696 15.2 16.3596 15.56C16.2096 15.8 15.9596 15.93 15.7096 15.93Z" fill="currentColor"/>
								</svg>
								<div class="text-slate-500 text-sm">Full-time</div>
							</div>
						</div>
					</div>
					<div class="md:text-right">
						<a href="#" class="text-indigo-600 hover:underline inline-flex items-center text-sm">View job
							<svg class="h-4 w-4 -rotate-45 ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M14.4291 18.8201C14.2391 18.8201 14.0491 18.7501 13.8991 18.6001C13.6091 18.3101 13.6091 17.8301 13.8991 17.5401L19.4391 12.0001L13.8991 6.46012C13.6091 6.17012 13.6091 5.69012 13.8991 5.40012C14.1891 5.11012 14.6691 5.11012 14.9591 5.40012L21.0291 11.4701C21.3191 11.7601 21.3191 12.2401 21.0291 12.5301L14.9591 18.6001C14.8091 18.7501 14.6191 18.8201 14.4291 18.8201Z" fill="currentColor"/>
							<path d="M20.33 12.75H3.5C3.09 12.75 2.75 12.41 2.75 12C2.75 11.59 3.09 11.25 3.5 11.25H20.33C20.74 11.25 21.08 11.59 21.08 12C21.08 12.41 20.74 12.75 20.33 12.75Z" fill="currentColor"/>
							</svg>
						</a>
					</div>
				</div>
			</div>
		</div>
    </div>
</x-front-layout>
