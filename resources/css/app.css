@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer utilities {
    .scrollbar::-webkit-scrollbar {
        width: 0.5rem;
    }

    .scrollbar::-webkit-scrollbar-thumb {
        background: #e2e8f0;
    }

    .scrollbar::-webkit-scrollbar-track {
        background: rgba(226, 232, 240, 0.12);
    }
}

@layer base {
    @font-face {
        font-family: "HKGrotesk";
        font-style: normal;
        font-weight: 800;
        src: url("/fonts/HKGrotesk-ExtraBold.woff2") format("woff2"),
            url("/fonts/HKGrotesk-ExtraBold.woff") format("woff");
    }
    @font-face {
        font-family: "HKGrotesk";
        font-style: normal;
        font-weight: 700;
        src: url("/fonts/HKGrotesk-Bold.woff2") format("woff2"),
            url("/fonts/HKGrotesk-Bold.woff") format("truetype");
    }
    @font-face {
        font-family: "HKGrotesk";
        font-style: normal;
        font-weight: 600;
        src: url("/fonts/HKGrotesk-SemiBold.woff2") format("woff2"),
            url("/fonts/HKGrotesk-SemiBold.woff") format("woff");
    }
    @font-face {
        font-family: "HKGrotesk";
        font-style: normal;
        font-weight: 500;
        src: url("/fonts/HKGrotesk-Medium.woff2") format("woff2"),
            url("/fonts/HKGrotesk-Medium.woff") format("woff");
    }
    @font-face {
        font-family: "HKGrotesk";
        font-style: normal;
        font-weight: 400;
        src: url("/fonts/HKGrotesk-Regular.woff2") format("woff2"),
            url("/fonts/HKGrotesk-Regular.woff") format("woff");
    }
    @font-face {
        font-family: "HKGrotesk";
        font-style: italic;
        font-weight: 400;
        src: url("/fonts/HKGrotesk-Italic.woff2") format("woff2"),
            url("/fonts/HKGrotesk-Italic.woff") format("woff");
    }
}
 
html {
    scroll-behavior: smooth;
}
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

body {
    -webkit-tap-highlight-color: transparent;
}

[x-cloak] {
    display: none;
}

@keyframes slide-in {
	from { transform: translateY(-3em) }
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}
.base-spinner {
    position: relative;
    overflow: hidden;
}
.base-spinner:before {
    content: "";
    box-sizing: border-box;
    position: absolute;
    background-color: inherit;
    width: 100%;
    height: 100%;
    display: block;
    z-index: 1;
    top: 0;
    left: 0;
}
.base-spinner:after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-top-color: currentColor;
    animation: spinner 0.6s linear infinite;
    z-index: 2;
}
.base-spinner.base-spinner-inverse:after {
    border: 2px solid #f3f4f5;
    border-top-color: #5a67d8 !important;
}

select {
    background-image: url("data:image/svg+xml, %3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='none' stroke='%239CA3AF'%3E%3Cpath d='M7 7l3-3 3 3m0 6l-3 3-3-3' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3C/path%3E%3C/svg%3E");
}