image: amazon/aws-cli:2.13.2

pipelines:
  branches:
    develop:
      - step:
          name: Build and Push to ECR (development)
          services:
            - docker
          caches:
            - docker
          script:
            # build the image
            - docker build -t etender-laravel .
            - docker tag etender-laravel etender-laravel:dev
            # use the pipe to push the image to AWS ECR
            - pipe: atlassian/aws-ecr-push-image:2.4.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_NCHAC
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_NCHAC
                AWS_DEFAULT_REGION: 'ap-south-1'
                IMAGE_NAME: etender-laravel
                TAGS: dev
                DEBUG: "true"

    staging:
      - step:
          name: Build and Push to ECR (staging)
          services:
            - docker
          caches:
            - docker
          script:
            - echo "Login to AWS ECR for staging..."
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com"
            - docker compose build
            - docker tag etender:staging "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com/etender-laravel:staging"
            - docker push "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com/etender-laravel:staging"

    master:
      - step:
          name: Build and Push to ECR (production)
          services:
            - docker
          caches:
            - docker
          script:
            - echo "Login to AWS ECR for production..."
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com"
            - docker compose build
            - docker tag etender:production "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com/etender-laravel:production"
            - docker push "$AWS_ACCOUNT_ID_NCHAC.dkr.ecr.$AWS_REGION.amazonaws.com/etender-laravel:production"