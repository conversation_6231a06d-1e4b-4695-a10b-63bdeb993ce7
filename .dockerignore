# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE

# Node modules and build artifacts
node_modules
npm-debug.log
yarn-error.log

# Laravel specific
/storage/app/*
!/storage/app/.gitignore
/storage/framework/cache/*
!/storage/framework/cache/.gitignore
/storage/framework/sessions/*
!/storage/framework/sessions/.gitignore
/storage/framework/testing/*
!/storage/framework/testing/.gitignore
/storage/framework/views/*
!/storage/framework/views/.gitignore
/storage/logs/*
!/storage/logs/.gitignore
/bootstrap/cache/*
!/bootstrap/cache/.gitignore

# Environment files
.env
.env.*
!.env.example

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
/tests
phpunit.xml
.phpunit.result.cache

# Development tools
.php_cs.cache
.php-cs-fixer.cache

# Vendor (will be installed during build)
# vendor

# Docker files (avoid recursion)
Dockerfile*
docker-compose*
.dockerignore
