/**
 * TinyMCE version 6.0.3 (2022-05-25)
 */
!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const e=t=>e=>(t=>{const e=typeof t;return null===t?"null":"object"===e&&Array.isArray(t)?"array":"object"===e&&(n=r=t,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":e;var n,r,o,s})(e)===t,n=t=>e=>typeof e===t,r=e("string"),o=e("object"),s=e("array"),i=n("boolean"),a=n("function"),l=n("number"),d=()=>{},c=(t,e)=>t===e,u=t=>e=>!t(e),m=(!1,()=>false);class p{constructor(t,e){this.tag=t,this.value=e}static some(t){return new p(!0,t)}static none(){return p.singletonNone}fold(t,e){return this.tag?e(this.value):t()}isSome(){return this.tag}isNone(){return!this.tag}map(t){return this.tag?p.some(t(this.value)):p.none()}bind(t){return this.tag?t(this.value):p.none()}exists(t){return this.tag&&t(this.value)}forall(t){return!this.tag||t(this.value)}filter(t){return!this.tag||t(this.value)?this:p.none()}getOr(t){return this.tag?this.value:t}or(t){return this.tag?this:t}getOrThunk(t){return this.tag?this.value:t()}orThunk(t){return this.tag?this:t()}getOrDie(t){if(this.tag)return this.value;throw new Error(null!=t?t:"Called getOrDie on None")}static from(t){return null==t?p.none():p.some(t)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(t){this.tag&&t(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}p.singletonNone=new p(!1);const g=Array.prototype.slice,h=Array.prototype.indexOf,f=Array.prototype.push,y=(t,e)=>{const n=t.length,r=new Array(n);for(let o=0;o<n;o++){const n=t[o];r[o]=e(n,o)}return r},C=(t,e)=>{for(let n=0,r=t.length;n<r;n++)e(t[n],n)},v=(t,e)=>{const n=[];for(let r=0,o=t.length;r<o;r++){const o=t[r];e(o,r)&&n.push(o)}return n},b=(t,e,n)=>(C(t,((t,r)=>{n=e(n,t,r)})),n),S=(t,e,n)=>{for(let r=0,o=t.length;r<o;r++){const o=t[r];if(e(o,r))return p.some(o);if(n(o,r))break}return p.none()},N=(t,e)=>S(t,e,m),L=(t,e)=>(t=>{const e=[];for(let n=0,r=t.length;n<r;++n){if(!s(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);f.apply(e,t[n])}return e})(y(t,e)),T=t=>{const e=g.call(t,0);return e.reverse(),e},O=(t,e)=>e>=0&&e<t.length?p.some(t[e]):p.none(),A=t=>O(t,0),k=t=>O(t,t.length-1),D=(t,e)=>{const n=[],r=a(e)?t=>((t,e)=>{for(let n=0,r=t.length;n<r;n++)if(e(t[n]))return!0;return!1})(n,(n=>e(n,t))):t=>((t,e)=>{return n=t,r=e,h.call(n,r)>-1;var n,r})(n,t);for(let e=0,o=t.length;e<o;e++){const o=t[e];r(o)||n.push(o)}return n},w=t=>{if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}},x=(t,e)=>{const n=(e||document).createElement(t);return w(n)},B=w,E=(t,e)=>t.dom===e.dom;"undefined"!=typeof window?window:Function("return this;")();const I=t=>t.dom.nodeName.toLowerCase(),M=(1,t=>1===(t=>t.dom.nodeType)(t));const P=t=>e=>M(e)&&I(e)===t,R=t=>p.from(t.dom.parentNode).map(B),U=t=>y(t.dom.childNodes,B),$=(t,e)=>{const n=t.dom.childNodes;return p.from(n[e]).map(B)},_=t=>$(t,0),H=t=>$(t,t.dom.childNodes.length-1),F=(t,e)=>{R(t).each((n=>{n.dom.insertBefore(e.dom,t.dom)}))},K=(t,e)=>{t.dom.appendChild(e.dom)},j=(t,e)=>{C(e,(e=>{K(t,e)}))},V=t=>{t.dom.textContent="",C(U(t),(t=>{z(t)}))},z=t=>{const e=t.dom;null!==e.parentNode&&e.parentNode.removeChild(e)};var Q=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),q=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),W=tinymce.util.Tools.resolve("tinymce.util.VK");const Z=Object.keys,G=(t,e)=>{const n=Z(t);for(let r=0,o=n.length;r<o;r++){const o=n[r];e(t[o],o)}},J=(t,e)=>{const n=t.dom;G(e,((t,e)=>{((t,e,n)=>{if(!(r(n)||i(n)||l(n)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",n,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(e,n+"")})(n,e,t)}))},X=t=>b(t.dom.attributes,((t,e)=>(t[e.name]=e.value,t)),{}),Y=t=>((t,e)=>B(t.dom.cloneNode(!0)))(t),tt=(t,e)=>{const n=((t,e)=>{const n=x(e),r=X(t);return J(n,r),n})(t,e);((t,e)=>{const n=(t=>p.from(t.dom.nextSibling).map(B))(t);n.fold((()=>{R(t).each((t=>{K(t,e)}))}),(t=>{F(t,e)}))})(t,n);const r=U(t);return j(n,r),z(t),n};var et=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),nt=tinymce.util.Tools.resolve("tinymce.util.Tools");const rt=t=>e=>e&&e.nodeName.toLowerCase()===t,ot=t=>e=>e&&t.test(e.nodeName),st=t=>t&&3===t.nodeType,it=ot(/^(OL|UL|DL)$/),at=ot(/^(OL|UL)$/),lt=rt("ol"),dt=ot(/^(LI|DT|DD)$/),ct=ot(/^(DT|DD)$/),ut=ot(/^(TH|TD)$/),mt=rt("br"),pt=(t,e)=>e&&!!t.schema.getTextBlockElements()[e.nodeName],gt=(t,e)=>t&&t.nodeName in e,ht=(t,e,n)=>{const r=t.isEmpty(e);return!(n&&t.select("span[data-mce-type=bookmark]",e).length>0)&&r},ft=(t,e)=>t.isChildOf(e,t.getRoot()),yt=t=>e=>e.options.get(t),Ct=yt("lists_indent_on_tab"),vt=yt("forced_root_block"),bt=yt("forced_root_block_attrs"),St=(t,e)=>{const n=t.dom,r=t.schema.getBlockElements(),o=n.createFragment(),s=vt(t),i=bt(t);let a,l,d;for(l=n.create(s,i),gt(e.firstChild,r)||o.appendChild(l);a=e.firstChild;){const t=a.nodeName;d||"SPAN"===t&&"bookmark"===a.getAttribute("data-mce-type")||(d=!0),gt(a,r)?(o.appendChild(a),l=null):(l||(l=n.create(s,i),o.appendChild(l)),l.appendChild(a))}return d||l.appendChild(n.create("br",{"data-mce-bogus":"1"})),o},Nt=et.DOM,Lt=P("dd"),Tt=P("dt"),Ot=t=>{Tt(t)&&tt(t,"dd")},At=(t,e,n)=>{C(n,"Indent"===e?Ot:e=>((t,e)=>{Lt(e)?tt(e,"dt"):Tt(e)&&R(e).each((n=>((t,e,n)=>{const r=Nt.select('span[data-mce-type="bookmark"]',e),o=St(t,n),s=Nt.createRng();s.setStartAfter(n),s.setEndAfter(e);const i=s.extractContents();for(let e=i.firstChild;e;e=e.firstChild)if("LI"===e.nodeName&&t.dom.isEmpty(e)){Nt.remove(e);break}var a;t.dom.isEmpty(i)||Nt.insertAfter(i,e),Nt.insertAfter(o,e),ht(t.dom,n.parentNode)&&(a=n.parentNode,nt.each(r,(t=>{a.parentNode.insertBefore(t,n.parentNode)})),Nt.remove(a)),Nt.remove(n),ht(t.dom,e)&&Nt.remove(e)})(t,n.dom,e.dom)))})(t,e))},kt=(t,e)=>{if(st(t))return{container:t,offset:e};const n=Q.getNode(t,e);return st(n)?{container:n,offset:e>=t.childNodes.length?n.data.length:0}:n.previousSibling&&st(n.previousSibling)?{container:n.previousSibling,offset:n.previousSibling.data.length}:n.nextSibling&&st(n.nextSibling)?{container:n.nextSibling,offset:0}:{container:t,offset:e}},Dt=t=>{const e=t.cloneRange(),n=kt(t.startContainer,t.startOffset);e.setStart(n.container,n.offset);const r=kt(t.endContainer,t.endOffset);return e.setEnd(r.container,r.offset),e},wt=(t,e)=>{const n=e||t.selection.getStart(!0);return t.dom.getParent(n,"OL,UL,DL",Bt(t,n))},xt=t=>{const e=t.selection.getSelectedBlocks();return v(((t,e)=>{const n=nt.map(e,(e=>t.dom.getParent(e,"li,dd,dt",Bt(t,e))||e));return D(n)})(t,e),dt)},Bt=(t,e)=>{const n=t.dom.getParents(e,"TD,TH");return n.length>0?n[0]:t.getBody()},Et=(t,e)=>{const n=t.dom.getParents(e,"ol,ul",Bt(t,e));return k(n)},It=(t,e)=>{const n=y(e,(e=>Et(t,e).getOr(e)));return D(n)},Mt=(t,e,n=c)=>t.exists((t=>n(t,e))),Pt=(t,e,n)=>t.isSome()&&e.isSome()?p.some(n(t.getOrDie(),e.getOrDie())):p.none(),Rt=(t,e,n)=>t.dispatch("ListMutation",{action:e,element:n}),Ut=($t=/^\s+|\s+$/g,t=>t.replace($t,""));var $t;const _t=(t,e,n)=>{((t,e,n)=>{if(!r(n))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",n,":: Element ",t),new Error("CSS value must be a string: "+n);(t=>void 0!==t.style&&a(t.style.getPropertyValue))(t)&&t.style.setProperty(e,n)})(t.dom,e,n)},Ht=(t,e)=>{K(t.item,e.list)},Ft=(t,e)=>{const n={list:x(e,t),item:x("li",t)};return K(n.list,n.item),n},Kt=t=>((t,e)=>{const n=t.dom;if(1!==n.nodeType)return!1;{const t=n;if(void 0!==t.matches)return t.matches(e);if(void 0!==t.msMatchesSelector)return t.msMatchesSelector(e);if(void 0!==t.webkitMatchesSelector)return t.webkitMatchesSelector(e);if(void 0!==t.mozMatchesSelector)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}})(t,"OL,UL"),jt=t=>_(t).exists(Kt),Vt=t=>t.depth>0,zt=t=>t.isSelected,Qt=t=>{const e=U(t),n=H(t).exists(Kt)?e.slice(0,-1):e;return y(n,Y)},qt=t=>(C(t,((e,n)=>{((t,e)=>{const n=t[e].depth,r=t=>t.depth===n&&!t.dirty,o=t=>t.depth<n;return S(T(t.slice(0,e)),r,o).orThunk((()=>S(t.slice(e+1),r,o)))})(t,n).fold((()=>{e.dirty&&(t=>{t.listAttributes=((t,e)=>{const n={};var r;return((t,e,n,r)=>{G(t,((t,o)=>{(e(t,o)?n:r)(t,o)}))})(t,e,(r=n,(t,e)=>{r[e]=t}),d),n})(t.listAttributes,((t,e)=>"start"!==e))})(e)}),(t=>{return r=t,(n=e).listType=r.listType,void(n.listAttributes={...r.listAttributes});var n,r}))})),t),Wt=(t,e,n,r)=>_(r).filter(Kt).fold((()=>{e.each((t=>{E(t.start,r)&&n.set(!0)}));const o=((t,e,n)=>R(t).filter(M).map((r=>({depth:e,dirty:!1,isSelected:n,content:Qt(t),itemAttributes:X(t),listAttributes:X(r),listType:I(r)}))))(r,t,n.get());e.each((t=>{E(t.end,r)&&n.set(!1)}));const s=H(r).filter(Kt).map((r=>Zt(t,e,n,r))).getOr([]);return o.toArray().concat(s)}),(r=>Zt(t,e,n,r))),Zt=(t,e,n,r)=>L(U(r),(r=>(Kt(r)?Zt:Wt)(t+1,e,n,r))),Gt=(t,e)=>{const n=qt(e);return((t,e)=>{const n=b(e,((e,n)=>n.depth>e.length?((t,e,n)=>{const r=((t,e,n)=>{const r=[];for(let o=0;o<n;o++)r.push(Ft(t,e.listType));return r})(t,n,n.depth-e.length);var o;return(t=>{for(let e=1;e<t.length;e++)Ht(t[e-1],t[e])})(r),((t,e)=>{for(let e=0;e<t.length-1;e++)_t(t[e].item,"list-style-type","none");k(t).each((t=>{J(t.list,e.listAttributes),J(t.item,e.itemAttributes),j(t.item,e.content)}))})(r,n),o=r,Pt(k(e),A(o),Ht),e.concat(r)})(t,e,n):((t,e,n)=>{const r=e.slice(0,n.depth);return k(r).each((e=>{const r=((t,e,n)=>{const r=x("li",t);return J(r,e),j(r,n),r})(t,n.itemAttributes,n.content);((t,e)=>{K(t.list,e),t.item=e})(e,r),((t,e)=>{I(t.list)!==e.listType&&(t.list=tt(t.list,e.listType)),J(t.list,e.listAttributes)})(e,n)})),r})(t,e,n)),[]);return A(n).map((t=>t.list))})(t.contentDocument,n).toArray()},Jt=(t,e,n)=>{const r=((t,e)=>{const n=(t=>{let e=!1;return{get:()=>e,set:t=>{e=t}}})();return y(t,(t=>({sourceList:t,entries:Zt(0,e,n,t)})))})(e,(t=>{const e=y(xt(t),B);return Pt(N(e,u(jt)),N(T(e),u(jt)),((t,e)=>({start:t,end:e})))})(t));C(r,(e=>{((t,e)=>{C(v(t,zt),(t=>((t,e)=>{switch(t){case"Indent":e.depth++;break;case"Outdent":e.depth--;break;case"Flatten":e.depth=0}e.dirty=!0})(e,t)))})(e.entries,n);const r=((t,e)=>L(((t,e)=>{if(0===t.length)return[];{let n=e(t[0]);const r=[];let o=[];for(let s=0,i=t.length;s<i;s++){const i=t[s],a=e(i);a!==n&&(r.push(o),o=[]),n=a,o.push(i)}return 0!==o.length&&r.push(o),r}})(e,Vt),(e=>A(e).exists(Vt)?Gt(t,e):((t,e)=>{const n=qt(e);return y(n,(e=>{const n=((t,e)=>{const n=document.createDocumentFragment();return C(t,(t=>{n.appendChild(t.dom)})),B(n)})(e.content);return B(St(t,n.dom))}))})(t,e))))(t,e.entries);var o;C(r,(e=>{Rt(t,"Indent"===n?"IndentList":"OutdentList",e.dom)})),o=e.sourceList,C(r,(t=>{F(o,t)})),z(e.sourceList)}))},Xt=(t,e)=>{const n=y((t=>{const e=(t=>{const e=Et(t,t.selection.getStart()),n=v(t.selection.getSelectedBlocks(),at);return e.toArray().concat(n)})(t);return It(t,e)})(t),B),r=y((t=>v(xt(t),ct))(t),B);let o=!1;if(n.length||r.length){const s=t.selection.getBookmark();Jt(t,n,e),At(t,e,r),t.selection.moveToBookmark(s),t.selection.setRng(Dt(t.selection.getRng())),t.nodeChanged(),o=!0}return o},Yt=t=>Xt(t,"Indent"),te=t=>Xt(t,"Outdent"),ee=t=>Xt(t,"Flatten");var ne=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager");const re=et.DOM,oe=t=>{const e={},n=n=>{let r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"];if(1===r.nodeType){const t=re.create("span",{"data-mce-type":"bookmark"});r.hasChildNodes()?(o=Math.min(o,r.childNodes.length-1),n?r.insertBefore(t,r.childNodes[o]):re.insertAfter(t,r.childNodes[o])):r.appendChild(t),r=t,o=0}e[n?"startContainer":"endContainer"]=r,e[n?"startOffset":"endOffset"]=o};return n(!0),t.collapsed||n(),e},se=t=>{const e=e=>{let n,r=n=t[e?"startContainer":"endContainer"],o=t[e?"startOffset":"endOffset"];r&&(1===r.nodeType&&(o=(t=>{let e=t.parentNode.firstChild,n=0;for(;e;){if(e===t)return n;1===e.nodeType&&"bookmark"===e.getAttribute("data-mce-type")||n++,e=e.nextSibling}return-1})(r),r=r.parentNode,re.remove(n),!r.hasChildNodes()&&re.isBlock(r)&&r.appendChild(re.create("br"))),t[e?"startContainer":"endContainer"]=r,t[e?"startOffset":"endOffset"]=o)};e(!0),e();const n=re.createRng();return n.setStart(t.startContainer,t.startOffset),t.endContainer&&n.setEnd(t.endContainer,t.endOffset),Dt(n)},ie=t=>{switch(t){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},ae=t=>/\btox\-/.test(t.className),le=(t,e,n)=>{const r=t=>{const r=S(t.parents,it,ut).filter((t=>t.nodeName===e&&!ae(t))).isSome();n(r)},o=t.dom.getParents(t.selection.getNode());return r({parents:o}),t.on("NodeChange",r),()=>t.off("NodeChange",r)},de=(t,e)=>{nt.each(e,((e,n)=>{t.setAttribute(n,e)}))},ce=(t,e,n)=>{((t,e,n)=>{const r=n["list-style-type"]?n["list-style-type"]:null;t.setStyle(e,"list-style-type",r)})(t,e,n),((t,e,n)=>{de(e,n["list-attributes"]),nt.each(t.select("li",e),(t=>{de(t,n["list-item-attributes"])}))})(t,e,n)},ue=(t,e,n,r)=>{let o=e[n?"startContainer":"endContainer"];const s=e[n?"startOffset":"endOffset"];for(1===o.nodeType&&(o=o.childNodes[Math.min(s,o.childNodes.length-1)]||o),!n&&mt(o.nextSibling)&&(o=o.nextSibling);o.parentNode!==r;){if(pt(t,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o},me=(t,e,n)=>{const r=t.selection.getRng();let o="LI";const s=Bt(t,t.selection.getStart(!0)),i=t.dom;if("false"===i.getContentEditable(t.selection.getNode()))return;"DL"===(e=e.toUpperCase())&&(o="DT");const a=oe(r),l=((t,e,n)=>{const r=[],o=t.dom,s=ue(t,e,!0,n),i=ue(t,e,!1,n);let a;const l=[];for(let t=s;t&&(l.push(t),t!==i);t=t.nextSibling);return nt.each(l,(e=>{if(pt(t,e))return r.push(e),void(a=null);if(o.isBlock(e)||mt(e))return mt(e)&&o.remove(e),void(a=null);const s=e.nextSibling;ne.isBookmarkNode(e)&&(it(s)||pt(t,s)||!s&&e.parentNode===n)?a=null:(a||(a=o.create("p"),e.parentNode.insertBefore(a,e),r.push(a)),a.appendChild(e))})),r})(t,r,s);nt.each(l,(r=>{let s;const a=r.previousSibling,l=r.parentNode;dt(l)||(a&&it(a)&&a.nodeName===e&&((t,e,n)=>{const r=t.getStyle(e,"list-style-type");let o=n?n["list-style-type"]:"";return o=null===o?"":o,r===o})(i,a,n)?(s=a,r=i.rename(r,o),a.appendChild(r)):(s=i.create(e),r.parentNode.insertBefore(s,r),s.appendChild(r),r=i.rename(r,o)),((t,e,n)=>{nt.each(["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],(n=>t.setStyle(e,n,"")))})(i,r),ce(i,s,n),ge(t.dom,s))})),t.selection.setRng(se(a))},pe=(t,e,n)=>{return((t,e)=>t&&e&&it(t)&&t.nodeName===e.nodeName)(e,n)&&((t,e,n)=>t.getStyle(e,"list-style-type",!0)===t.getStyle(n,"list-style-type",!0))(t,e,n)&&(r=n,e.className===r.className);var r},ge=(t,e)=>{let n,r;if(n=e.nextSibling,pe(t,e,n)){for(;r=n.firstChild;)e.appendChild(r);t.remove(n)}if(n=e.previousSibling,pe(t,e,n)){for(;r=n.lastChild;)e.insertBefore(r,e.firstChild);t.remove(n)}},he=t=>"list-style-type"in t,fe=(t,e,n)=>{const r=wt(t),s=(t=>{const e=wt(t),n=t.selection.getSelectedBlocks();return((t,e)=>t&&1===e.length&&e[0]===t)(e,n)?(t=>v(t.querySelectorAll("ol,ul,dl"),it))(e):v(n,(t=>it(t)&&e!==t))})(t),i=o(n)?n:{};s.length>0?((t,e,n,r,o)=>{const s=it(e);if(s&&e.nodeName===r&&!he(o))ee(t);else{me(t,r,o);const i=oe(t.selection.getRng()),a=s?[e,...n]:n;nt.each(a,(e=>{((t,e,n,r)=>{if(e.nodeName!==n){const o=t.dom.rename(e,n);ce(t.dom,o,r),Rt(t,ie(n),o)}else ce(t.dom,e,r),Rt(t,ie(n),e)})(t,e,r,o)})),t.selection.setRng(se(i))}})(t,r,s,e,i):((t,e,n,r)=>{if(e!==t.getBody())if(e)if(e.nodeName!==n||he(r)||ae(e)){const o=oe(t.selection.getRng());ce(t.dom,e,r);const s=t.dom.rename(e,n);ge(t.dom,s),t.selection.setRng(se(o)),me(t,n,r),Rt(t,ie(n),s)}else ee(t);else me(t,n,r),Rt(t,ie(n),e)})(t,r,e,i)},ye=et.DOM,Ce=(t,e,n,r)=>{let o=e.startContainer;const s=e.startOffset;if(st(o)&&(n?s<o.data.length:s>0))return o;const i=t.schema.getNonEmptyElements();1===o.nodeType&&(o=Q.getNode(o,s));const a=new q(o,r);for(n&&((t,e)=>!!mt(e)&&t.isBlock(e.nextSibling)&&!mt(e.previousSibling))(t.dom,o)&&a.next();o=a[n?"next":"prev2"]();){if("LI"===o.nodeName&&!o.hasChildNodes())return o;if(i[o.nodeName])return o;if(st(o)&&o.data.length>0)return o}},ve=(t,e)=>{const n=e.childNodes;return 1===n.length&&!it(n[0])&&t.isBlock(n[0])},be=(t,e,n)=>{let r;const o=e.parentNode;if(!ft(t,e)||!ft(t,n))return;it(n.lastChild)&&(r=n.lastChild),o===n.lastChild&&mt(o.previousSibling)&&t.remove(o.previousSibling);const s=n.lastChild;s&&mt(s)&&e.hasChildNodes()&&t.remove(s),ht(t,n,!0)&&V(B(n)),((t,e,n)=>{let r;const o=ve(t,n)?n.firstChild:n;if(((t,e)=>{ve(t,e)&&t.remove(e.firstChild,!0)})(t,e),!ht(t,e,!0))for(;r=e.firstChild;)o.appendChild(r)})(t,e,n),r&&n.appendChild(r);const i=((t,e)=>{const n=t.dom,r=e.dom;return n!==r&&n.contains(r)})(B(n),B(e))?t.getParents(e,it,n):[];t.remove(e),C(i,(e=>{ht(t,e)&&e!==t.getRoot()&&t.remove(e)}))},Se=(t,e)=>{const n=t.dom,r=t.selection,o=r.getStart(),s=Bt(t,o),i=n.getParent(r.getStart(),"LI",s);if(i){const o=i.parentNode;if(o===t.getBody()&&ht(n,o))return!0;const a=Dt(r.getRng()),l=n.getParent(Ce(t,a,e,s),"LI",s);if(l&&l!==i)return t.undoManager.transact((()=>{var n;e?((t,e,n,r)=>{const o=t.dom;if(o.isEmpty(r))((t,e,n)=>{V(B(n)),be(t.dom,e,n),t.selection.setCursorLocation(n,0)})(t,n,r);else{const s=oe(e);be(o,n,r),t.selection.setRng(se(s))}})(t,a,l,i):(n=i).parentNode.firstChild===n?te(t):((t,e,n,r)=>{const o=oe(e);be(t.dom,n,r);const s=se(o);t.selection.setRng(s)})(t,a,i,l)})),!0;if(!l&&!e&&0===a.startOffset&&0===a.endOffset)return t.undoManager.transact((()=>{ee(t)})),!0}return!1},Ne=(t,e)=>t.selection.isCollapsed()?((t,e)=>Se(t,e)||((t,e)=>{const n=t.dom,r=t.selection.getStart(),o=Bt(t,r),s=n.getParent(r,n.isBlock,o);if(s&&n.isEmpty(s)){const r=Dt(t.selection.getRng()),i=n.getParent(Ce(t,r,e,o),"LI",o);if(i)return t.undoManager.transact((()=>{((t,e,n)=>{const r=t.getParent(e.parentNode,t.isBlock,n);t.remove(e),r&&t.isEmpty(r)&&t.remove(r)})(n,s,o),ge(n,i.parentNode),t.selection.select(i,!0),t.selection.collapse(e)})),!0}return!1})(t,e))(t,e):(t=>{const e=t.selection.getStart(),n=Bt(t,e);return!!(t.dom.getParent(e,"LI,DT,DD",n)||xt(t).length>0)&&(t.undoManager.transact((()=>{t.execCommand("Delete"),((t,e)=>{const n=nt.grep(t.select("ol,ul",e));nt.each(n,(e=>{((t,e)=>{const n=e.parentNode;if("LI"===n.nodeName&&n.firstChild===e){const r=n.previousSibling;r&&"LI"===r.nodeName?(r.appendChild(e),ht(t,n)&&ye.remove(n)):ye.setStyle(n,"listStyleType","none")}if(it(n)){const t=n.previousSibling;t&&"LI"===t.nodeName&&t.appendChild(e)}})(t,e)}))})(t.dom,t.getBody())})),!0)})(t),Le=t=>{const e=T(Ut(t).split("")),n=y(e,((t,e)=>{const n=t.toUpperCase().charCodeAt(0)-"A".charCodeAt(0)+1;return Math.pow(26,e)*n}));return b(n,((t,e)=>t+e),0)},Te=t=>{if(--t<0)return"";{const e=t%26,n=Math.floor(t/26);return Te(n)+String.fromCharCode("A".charCodeAt(0)+e)}},Oe=t=>{const e=parseInt(t.start,10);return Mt(t.listStyleType,"upper-alpha")?Te(e):Mt(t.listStyleType,"lower-alpha")?Te(e).toLowerCase():t.start},Ae=(t,e)=>()=>{const n=wt(t);return n&&n.nodeName===e},ke=t=>{t.addCommand("mceListProps",(()=>{(t=>{const e=wt(t);lt(e)&&t.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:Oe({start:t.dom.getAttrib(e,"start","1"),listStyleType:p.some(t.dom.getStyle(e,"list-style-type"))})},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:e=>{(t=>{switch((t=>/^[0-9]+$/.test(t)?2:/^[A-Z]+$/.test(t)?0:/^[a-z]+$/.test(t)?1:t.length>0?4:3)(t)){case 2:return p.some({listStyleType:p.none(),start:t});case 0:return p.some({listStyleType:p.some("upper-alpha"),start:Le(t).toString()});case 1:return p.some({listStyleType:p.some("lower-alpha"),start:Le(t).toString()});case 3:return p.some({listStyleType:p.none(),start:""});case 4:return p.none()}})(e.getData().start).each((e=>{t.execCommand("mceListUpdate",!1,{attrs:{start:"1"===e.start?"":e.start},styles:{"list-style-type":e.listStyleType.getOr("")}})})),e.close()}})})(t)}))};t.add("lists",(t=>((t=>{(0,t.options.register)("lists_indent_on_tab",{processor:"boolean",default:!0})})(t),!1===t.hasPlugin("rtc",!0)?((t=>{Ct(t)&&(t=>{t.on("keydown",(e=>{e.keyCode!==W.TAB||W.metaKeyPressed(e)||t.undoManager.transact((()=>{(e.shiftKey?te(t):Yt(t))&&e.preventDefault()}))}))})(t),(t=>{t.on("keydown",(e=>{e.keyCode===W.BACKSPACE?Ne(t,!1)&&e.preventDefault():e.keyCode===W.DELETE&&Ne(t,!0)&&e.preventDefault()}))})(t)})(t),(t=>{t.on("BeforeExecCommand",(e=>{const n=e.command.toLowerCase();"indent"===n?Yt(t):"outdent"===n&&te(t)})),t.addCommand("InsertUnorderedList",((e,n)=>{fe(t,"UL",n)})),t.addCommand("InsertOrderedList",((e,n)=>{fe(t,"OL",n)})),t.addCommand("InsertDefinitionList",((e,n)=>{fe(t,"DL",n)})),t.addCommand("RemoveList",(()=>{ee(t)})),ke(t),t.addCommand("mceListUpdate",((e,n)=>{o(n)&&((t,e)=>{const n=wt(t);t.undoManager.transact((()=>{o(e.styles)&&t.dom.setStyles(n,e.styles),o(e.attrs)&&G(e.attrs,((e,r)=>t.dom.setAttrib(n,r,e)))}))})(t,n)})),t.addQueryStateHandler("InsertUnorderedList",Ae(t,"UL")),t.addQueryStateHandler("InsertOrderedList",Ae(t,"OL")),t.addQueryStateHandler("InsertDefinitionList",Ae(t,"DL"))})(t)):ke(t),(t=>{const e=e=>()=>t.execCommand(e);t.hasPlugin("advlist")||(t.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:e("InsertOrderedList"),onSetup:e=>le(t,"OL",e.setActive)}),t.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:e("InsertUnorderedList"),onSetup:e=>le(t,"UL",e.setActive)}))})(t),(t=>{const e={text:"List properties...",icon:"ordered-list",onAction:()=>t.execCommand("mceListProps"),onSetup:e=>le(t,"OL",e.setEnabled)};t.ui.registry.addMenuItem("listprops",e),t.ui.registry.addContextMenu("lists",{update:e=>{const n=wt(t,e);return lt(n)?["listprops"]:[]}})})(t),(t=>({backspaceDelete:e=>{Ne(t,e)}}))(t))))}();