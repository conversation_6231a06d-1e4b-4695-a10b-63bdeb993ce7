/**
 * TinyMCE version 6.0.3 (2022-05-25)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.util.VK");const n=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=o=e,(r=String).prototype.isPrototypeOf(n)||(null===(l=o.constructor)||void 0===l?void 0:l.name)===r.name)?"string":t;var n,o,r,l})(t)===e,o=e=>t=>typeof t===e,r=n("string"),l=n("object"),a=n("array"),s=(null,e=>null===e);const i=o("boolean"),c=o("function"),u=(e,t)=>{if(a(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},g=()=>{},m=(e,t)=>e===t;class h{constructor(e,t){this.tag=e,this.value=t}static some(e){return new h(!0,e)}static none(){return h.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?h.some(e(this.value)):h.none()}bind(e){return this.tag?e(this.value):h.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:h.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?h.none():h.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}h.singletonNone=new h(!1);const d=Array.prototype.indexOf,p=Array.prototype.push,f=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!a(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);p.apply(t,e[n])}return t},k=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return h.none()},v=(e,t,n=m)=>e.exists((e=>n(e,t))),y=e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t},x=(e,t)=>e?h.some(t):h.none(),_=e=>t=>t.options.get(e),b=_("link_assume_external_targets"),w=_("link_context_toolbar"),C=_("link_list"),O=_("link_default_target"),N=_("link_default_protocol"),A=_("link_target_list"),T=_("link_rel_list"),S=_("link_class_list"),E=_("link_title"),R=_("allow_unsafe_link_target"),P=_("link_quicklink");var L=tinymce.util.Tools.resolve("tinymce.util.Tools");const M=e=>r(e.value)?e.value:"",D=(e,t)=>{const n=[];return L.each(e,(e=>{const o=(e=>r(e.text)?e.text:r(e.title)?e.title:"")(e);if(void 0!==e.menu){const r=D(e.menu,t);n.push({text:o,items:r})}else{const r=t(e);n.push({text:o,value:r})}})),n},B=(e=M)=>t=>h.from(t).map((t=>D(t,e))),I=e=>B(M)(e),j=B,K=(e,t)=>n=>({name:e,type:"listbox",label:t,items:n}),U=M,q=Object.keys,F=Object.hasOwnProperty,V=(e,t)=>F.call(e,t);var $=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),z=tinymce.util.Tools.resolve("tinymce.util.URI");const G=e=>e&&"a"===e.nodeName.toLowerCase(),H=e=>G(e)&&!!Q(e),J=(e,t)=>{if(e.collapsed)return[];{const n=e.cloneContents(),o=new $(n.firstChild,n),r=[];let l=n.firstChild;do{t(l)&&r.push(l)}while(l=o.next());return r}},W=e=>/^\w+:/i.test(e),Q=e=>e.getAttribute("data-mce-href")||e.getAttribute("href"),X=(e,t)=>{const n=["noopener"],o=e?e.split(/\s+/):[],r=e=>e.filter((e=>-1===L.inArray(n,e))),l=t?(e=>(e=r(e)).length>0?e.concat(n):n)(o):r(o);return l.length>0?(e=>L.trim(e.sort().join(" ")))(l):""},Y=(e,t)=>(t=t||e.selection.getNode(),ne(t)?e.dom.select("a[href]",t)[0]:e.dom.getParent(t,"a[href]")),Z=(e,t)=>(t?t.innerText||t.textContent:e.getContent({format:"text"})).replace(/\uFEFF/g,""),ee=e=>L.grep(e,H).length>0,te=e=>{const t=e.schema.getTextInlineElements();return 0===J(e.selection.getRng(),(e=>1===e.nodeType&&!G(e)&&!V(t,e.nodeName.toLowerCase()))).length},ne=e=>e&&"FIGURE"===e.nodeName&&/\bimage\b/i.test(e.className),oe=(e,t,n)=>{const o=e.selection.getNode(),r=Y(e,o),l=((e,t)=>{const n={...t};if(0===T(e).length&&!R(e)){const e=X(n.rel,"_blank"===n.target);n.rel=e||null}return h.from(n.target).isNone()&&!1===A(e)&&(n.target=O(e)),n.href=((e,t)=>"http"!==t&&"https"!==t||W(e)?e:t+"://"+e)(n.href,b(e)),n})(e,(e=>{return t=["title","rel","class","target"],n=(t,n)=>(e[n].each((e=>{t[n]=e.length>0?e:null})),t),o={href:e.href},((e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)})(t,((e,t)=>{o=n(o,e)})),o;var t,n,o})(n));e.undoManager.transact((()=>{n.href===t.href&&t.attach(),r?(e.focus(),((e,t,n,o)=>{n.each((e=>{V(t,"innerText")?t.innerText=e:t.textContent=e})),e.dom.setAttribs(t,o),e.selection.select(t)})(e,r,n.text,l)):((e,t,n,o)=>{ne(t)?ie(e,t,o):n.fold((()=>{e.execCommand("mceInsertLink",!1,o)}),(t=>{e.insertContent(e.dom.createHTML("a",o,e.dom.encode(t)))}))})(e,o,n.text,l)}))},re=e=>{const{class:t,href:n,rel:o,target:r,text:l,title:a}=e;return((e,t)=>{const n={};var o;return((e,t,n,o)=>{((e,t)=>{const n=q(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}})(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))})(e,((e,t)=>!1===s(e)),(o=n,(e,t)=>{o[t]=e}),g),n})({class:t.getOrNull(),href:n,rel:o.getOrNull(),target:r.getOrNull(),text:l.getOrNull(),title:a.getOrNull()})},le=(e,t,n)=>{const o=((e,t)=>{const n=e.options.get,o={allow_html_data_urls:n("allow_html_data_urls"),allow_script_urls:n("allow_script_urls"),allow_svg_data_urls:n("allow_svg_data_urls")},r=t.href;return{...t,href:z.isDomSafe(r,"a",o)?r:""}})(e,n);e.hasPlugin("rtc",!0)?e.execCommand("createlink",!1,re(o)):oe(e,t,o)},ae=e=>{e.hasPlugin("rtc",!0)?e.execCommand("unlink"):(e=>{e.undoManager.transact((()=>{const t=e.selection.getNode();ne(t)?se(e,t):(e=>{const t=e.dom,n=e.selection,o=n.getBookmark(),r=n.getRng().cloneRange(),l=t.getParent(r.startContainer,"a[href]",e.getBody()),a=t.getParent(r.endContainer,"a[href]",e.getBody());l&&r.setStartBefore(l),a&&r.setEndAfter(a),n.setRng(r),e.execCommand("unlink"),n.moveToBookmark(o)})(e),e.focus()}))})(e)},se=(e,t)=>{const n=e.dom.select("img",t)[0];if(n){const o=e.dom.getParents(n,"a[href]",t)[0];o&&(o.parentNode.insertBefore(n,o),e.dom.remove(o))}},ie=(e,t,n)=>{const o=e.dom.select("img",t)[0];if(o){const t=e.dom.create("a",n);o.parentNode.insertBefore(t,o),t.appendChild(o)}},ce=(e,t)=>k(t,(t=>(e=>{return V(t=e,n="items")&&void 0!==t[n]&&null!==t[n];var t,n})(t)?ce(e,t.items):x(t.value===e,t))),ue=(e,t)=>{const n={text:e.text,title:e.title},o=(e,o)=>{const r=(l=t,a=o.name,"link"===a?l.link:"anchor"===a?l.anchor:h.none()).getOr([]);var l,a;return((e,t,n,o)=>{const r=o[t],l=e.length>0;return void 0!==r?ce(r,n).map((t=>({url:{value:t.value,meta:{text:l?e:t.text,attach:g}},text:l?e:t.text}))):h.none()})(n.text,o.name,r,e)};return{onChange:(e,t)=>{const r=t.name;return"url"===r?(e=>{const t=(o=e.url,x(n.text.length<=0,h.from(o.meta.text).getOr(o.value)));var o;const r=(e=>x(n.title.length<=0,h.from(e.meta.title).getOr("")))(e.url);return t.isSome()||r.isSome()?h.some({...t.map((e=>({text:e}))).getOr({}),...r.map((e=>({title:e}))).getOr({})}):h.none()})(e()):((e,t)=>d.call(e,t))(["anchor","link"],r)>-1?o(e(),t):"text"===r||"title"===r?(n[r]=e()[r],h.none()):h.none()}}};var ge=tinymce.util.Tools.resolve("tinymce.util.Delay");const me=e=>{const t=e.href;return t.indexOf("@")>0&&-1===t.indexOf("/")&&-1===t.indexOf("mailto:")?h.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:e=>({...e,href:"mailto:"+t})}):h.none()},he=(e,t)=>n=>{const o=n.href;return 1===e&&!W(o)||0===e&&/^\s*www(\.|\d\.)/i.test(o)?h.some({message:`The URL you entered seems to be an external link. Do you want to add the required ${t}:// prefix?`,preprocess:e=>({...e,href:t+"://"+o})}):h.none()},de=e=>{const t=e.dom.select("a:not([href])"),n=f(((e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o})(t,(e=>{const t=e.name||e.id;return t?[{text:t,value:"#"+t}]:[]})));return n.length>0?h.some([{text:"None",value:""}].concat(n)):h.none()},pe=e=>{const t=S(e);return t.length>0?I(t):h.none()},fe=e=>{try{return h.some(JSON.parse(e))}catch(e){return h.none()}},ke=(e,t)=>{const n=T(e);if(n.length>0){const o=v(t,"_blank"),r=e=>X(U(e),o);return(!1===R(e)?j(r):I)(n)}return h.none()},ve=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],ye=e=>{const t=A(e);return a(t)?I(t).orThunk((()=>h.some(ve))):!1===t?h.none():h.some(ve)},xe=(e,t,n)=>{const o=e.getAttrib(t,n);return null!==o&&o.length>0?h.some(o):h.none()},_e=(e,t)=>(e=>{const t=t=>e.convertURL(t.value||t.url,"href"),n=C(e);return new Promise((e=>{r(n)?fetch(n).then((e=>e.ok?e.text().then(fe):Promise.reject())).then(e,(()=>e(h.none()))):c(n)?n((t=>e(h.some(t)))):e(h.from(n))})).then((e=>e.bind(j(t)).map((e=>e.length>0?[{text:"None",value:""}].concat(e):e))))})(e).then((n=>{const o=((e,t)=>{const n=e.dom,o=te(e)?h.some(Z(e.selection,t)):h.none(),r=t?h.some(n.getAttrib(t,"href")):h.none(),l=t?h.from(n.getAttrib(t,"target")):h.none(),a=xe(n,t,"rel"),s=xe(n,t,"class");return{url:r,text:o,title:xe(n,t,"title"),target:l,rel:a,linkClass:s}})(e,t);return{anchor:o,catalogs:{targets:ye(e),rels:ke(e,o.target),classes:pe(e),anchor:de(e),link:n},optNode:h.from(t),flags:{titleEnabled:E(e)}}})),be=e=>{const t=(e=>{const t=Y(e);return _e(e,t)})(e);t.then((t=>{const n=((e,t)=>n=>{const o=n.getData();if(!o.url.value)return ae(e),void n.close();const r=e=>h.from(o[e]).filter((n=>!v(t.anchor[e],n))),l={href:o.url.value,text:r("text"),target:r("target"),rel:r("rel"),class:r("linkClass"),title:r("title")},a={href:o.url.value,attach:void 0!==o.url.meta&&o.url.meta.attach?o.url.meta.attach:g};((e,t)=>k([me,he(b(e),N(e))],(e=>e(t))).fold((()=>Promise.resolve(t)),(n=>new Promise((o=>{((e,t,n)=>{const o=e.selection.getRng();ge.setEditorTimeout(e,(()=>{e.windowManager.confirm(t,(t=>{e.selection.setRng(o),n(t)}))}))})(e,n.message,(e=>{o(e?n.preprocess(t):t)}))})))))(e,l).then((t=>{le(e,a,t)})),n.close()})(e,t);return((e,t,n)=>{const o=e.anchor.text.map((()=>({name:"text",type:"input",label:"Text to display"}))).toArray(),r=e.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],l=((e,t)=>{const n=e.anchor,o=n.url.getOr("");return{url:{value:o,meta:{original:{value:o}}},text:n.text.getOr(""),title:n.title.getOr(""),anchor:o,link:o,rel:n.rel.getOr(""),target:n.target.or(t).getOr(""),linkClass:n.linkClass.getOr("")}})(e,h.from(O(n))),a=e.catalogs,s=ue(l,a);return{title:"Insert/Edit Link",size:"normal",body:{type:"panel",items:f([[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],o,r,y([a.anchor.map(K("anchor","Anchors")),a.rels.map(K("rel","Rel")),a.targets.map(K("target","Open link in...")),a.link.map(K("link","Link list")),a.classes.map(K("linkClass","Class"))])])},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:l,onChange:(e,{name:t})=>{s.onChange(e.getData,{name:t}).each((t=>{e.setData(t)}))},onSubmit:t}})(t,n,e)})).then((t=>{e.windowManager.open(t)}))},we=(e,t)=>e.dom.getParent(t,"a[href]"),Ce=e=>we(e,e.selection.getStart()),Oe=(e,t)=>{if(t){const n=Q(t);if(/^#/.test(n)){const t=e.dom.select(n);t.length&&e.selection.scrollIntoView(t[0],!0)}else(e=>{const t=document.createElement("a");t.target="_blank",t.href=e,t.rel="noreferrer noopener";const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),((e,t)=>{document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)})(t,n)})(t.href)}},Ne=e=>()=>{be(e)},Ae=e=>()=>{Oe(e,Ce(e))},Te=(e,t)=>(e.on("NodeChange",t),()=>e.off("NodeChange",t)),Se=e=>t=>{const n=()=>t.setActive(!e.mode.isReadOnly()&&null!==Y(e,e.selection.getNode()));return n(),Te(e,n)},Ee=e=>t=>{const n=()=>t.setEnabled(null!==Y(e,e.selection.getNode()));return n(),Te(e,n)},Re=e=>t=>{const n=t=>{return ee(t)||(n=e.selection.getRng(),J(n,H).length>0);var n},o=e.dom.getParents(e.selection.getStart());return t.setEnabled(n(o)),Te(e,(e=>t.setEnabled(n(e.parents))))};e.add("link",(e=>{(e=>{const t=e.options.register;t("link_assume_external_targets",{processor:e=>{const t=r(e)||i(e);return t?!0===e?{value:1,valid:t}:"http"===e||"https"===e?{value:e,valid:t}:{value:0,valid:t}:{valid:!1,message:"Must be a string or a boolean."}},default:!1}),t("link_context_toolbar",{processor:"boolean",default:!1}),t("link_list",{processor:e=>r(e)||c(e)||u(e,l)}),t("link_default_target",{processor:"string"}),t("link_default_protocol",{processor:"string",default:"https"}),t("link_target_list",{processor:e=>i(e)||u(e,l),default:!0}),t("link_rel_list",{processor:"object[]",default:[]}),t("link_class_list",{processor:"object[]",default:[]}),t("link_title",{processor:"boolean",default:!0}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("link_quicklink",{processor:"boolean",default:!1})})(e),(e=>{e.ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:Ne(e),onSetup:Se(e)}),e.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:Ae(e),onSetup:Ee(e)}),e.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:()=>ae(e),onSetup:Re(e)})})(e),(e=>{e.ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:Ae(e),onSetup:Ee(e)}),e.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onAction:Ne(e)}),e.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:()=>ae(e),onSetup:Re(e)})})(e),(e=>{e.ui.registry.addContextMenu("link",{update:t=>ee(e.dom.getParents(t,"a"))?"link unlink openlink":"link"})})(e),(e=>{const t=t=>{const n=e.selection.getNode();return t.setEnabled(null!==Y(e,n)),g};e.ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:Se(e)},label:"Link",predicate:t=>!!Y(e,t)&&w(e),initValue:()=>{const t=Y(e);return t?Q(t):""},commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:t=>{const n=e.selection.getNode();return t.setActive(!!Y(e,n)),Se(e)(t)},onAction:t=>{const n=t.getValue(),o=(t=>{const n=Y(e),o=te(e);if(!n&&o){const o=Z(e.selection,n);return h.some(o.length>0?o:t)}return h.none()})(n);le(e,{href:n,attach:g},{href:n,text:o,title:h.none(),rel:h.none(),target:h.none(),class:h.none()}),(e=>{e.selection.collapse(!1)})(e),t.hide()}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:t,onAction:t=>{ae(e),t.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:t,onAction:t=>{Ae(e)(),t.hide()}}]})})(e),(e=>{e.on("click",(n=>{const o=we(e,n.target);o&&t.metaKeyPressed(n)&&(n.preventDefault(),Oe(e,o))})),e.on("keydown",(t=>{const n=Ce(e);n&&13===t.keyCode&&(e=>!0===e.altKey&&!1===e.shiftKey&&!1===e.ctrlKey&&!1===e.metaKey)(t)&&(t.preventDefault(),Oe(e,n))}))})(e),(e=>{e.addCommand("mceLink",(()=>{P(e)?e.dispatch("contexttoolbar-show",{toolbarKey:"quicklink"}):Ne(e)()}))})(e),(e=>{e.addShortcut("Meta+K","",(()=>{e.execCommand("mceLink")}))})(e)}))}();