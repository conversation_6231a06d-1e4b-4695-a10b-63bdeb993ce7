var dscSigner = (function ($) {
    var signData;
    var aliasName;
    var storeData;

    var batch_size = 0;
    var batch_token;

    var isCertificateValid = false;

    // For Request to NICDSign Http Server
    var dscServiceUrl = "http://localhost:8019/signer";
    // For Request to NICDSign Https Server
    //var dscServiceUrl = "https://localhost:8020/signer";
    // var dscServiceUrl = "https://localhost:8020/signer";

    var config = {};

    var configure = function (data) {
        if (data) {
            config = data;
        } else alert("Incorrect configuration data.");
    };

    var gstSigner = function (data) {
        signData = data;
        if (config.mode != "batch") {
            logout();
        }
        initialize(function (data) {
            listCertificatesForSigning();
        });
    };

    var getCertificate = function (callback) {
        initialize(function (data) {
            doGetCertificate(function (data) {
                callback(data);
            });
        });
    };

    function initialize(postInitCallback) {
        $.ajax({
            url: dscServiceUrl + "/isInitialized",
            type: "post",
            dataType: "json",
            crossDomain: true,
            contentType: "application/json",
            async: false,
        })
            .done(function (data) {
                if (data.status === "success") {
                    storeData = data;
                    if (data.storePasswordNeeded && !data.initialized) {
                        doInitialize(postInitCallback);
                    } else {
                        doGetCertificate(postInitCallback);
                    }
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
            });
        return true;
    }

    function initializeBatch(size) {
        logout();
        if (size <= 0 || size > 100) {
            alert("Batch size must be between 1 and 100");
            return;
        }
        batch_size = size;
        batch_token = SHA256(makeid());
        return batch_token;
    }

    var getBatchSize = function (callback) {
        return batch_size;
    };

    function doInitialize(postInitCallback) {
        $.ajax({
            url: dscServiceUrl + "/initialize",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            async: false,
        })
            .done(function (data) {
                if (data.status === "success") {
                    postInitCallback(data);
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
            });
    }

    function doGetCertificate(callback) {
        $.ajax({
            url: dscServiceUrl + "/getCertificate",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            async: false,
        })
            .done(function (data) {
                if (data.status === "success") {
                    callback(data);

                    //Do not logout if a batch is running
                    if (batch_size == 0) {
                        logout();
                    }
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
            });
    }

    function listCertificatesForSigning() {
        $.ajax({
            url: dscServiceUrl + "/listCertificates",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            async: false,
        })
            .done(function (data) {
                if (data.status === "success") {
                    if (
                        config.certificateSno == null ||
                        config.certificateSno == ""
                    ) {
                        if (
                            config.certificateData == null ||
                            config.certificateData == ""
                        ) {
                            selectCertificate(data);
                        } else {
                            var flag = false;
                            for (var i in data.certificates) {
                                if (
                                    config.certificateData ===
                                    data.certificates[i].certificateData
                                ) {
                                    doSign(
                                        data.certificates[i].alias,
                                        data.certificates[i].serialNumber,
                                        data.certificates[i]
                                    );
                                    flag = true;
                                }
                            }
                            if (!flag) {
                                alert(
                                    "No token found matching the registered certificate.\nMake sure you have plugged in the correct token and try again."
                                );
                                logout();
                            }
                        }
                    } else {
                        var flag = false;
                        for (var i in data.certificates) {
                            if (
                                config.certificateSno ===
                                data.certificates[i].serialNumber
                            ) {
                                doSign(
                                    data.certificates[i].alias,
                                    data.certificates[i].serialNumber,
                                    data.certificates[i]
                                );
                                flag = true;
                            }
                        }
                        if (!flag) {
                            alert(
                                "No token found matching the registered certificate.\nMake sure you have plugged in the correct token and try again."
                            );
                            logout();
                        }
                    }
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
            });
    }

    function selectCertificate(data) {
        var panel = $("#panel").addClass("modal");
        panel.empty();
        var div = $("<div>").addClass("modal-dialog modal-lg");
        div.append(
            $(
                '<div class="modal-header bg-blue"><h4 class="modal-title">Certificate for Signing</h4></div>'
            )
        );

        for (var i in data.certificates) {
            var notBefore = new Date(data.certificates[i].notBefore);
            var notBeforeDate = notBefore.toString("dd-MMM-yyyy hh:mm tt");
            var notAfter = new Date(data.certificates[i].notAfter);
            var notAfterDate = notAfter.toString("dd-MMM-yyyy hh:mm tt");

            div.append(
                $("<div>")
                    .append(
                        $("<button>", {
                            type: "button",
                            onclick:
                                'dscSigner.doSign("' +
                                data.certificates[i].alias +
                                '", "' +
                                data.certificates[i].serialNumber +
                                '", "' +
                                data.certificates[i].certificateData +
                                '")',
                        })
                            .text("Confirm Signing")
                            .addClass("btn btn-success")
                    )
                    .addClass("modal-footer")
            );
        }
        panel.append(div);
        panel.show();
        var closeBtn =
            "<button onclick='hidePanel()' class='btn btn-sm btn-danger' style='position:fixed;right:300px;top:50px'>Close</button>";
        $("#panel").append(closeBtn);
    }

    function doVerifyCertificate() {
        var certificateData = config.certificateData;
        var data = { action: "VERIFY", cert_data: certificateData };
        $.ajax({
            url: dscapibaseurl + "/certificate",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                if (data.status_cd == 1) {
                    var jsonData = JSON.parse(atob(data.data));
                    if (jsonData.isvalid === "Y") {
                        isCertificateValid = true;
                        return true;
                    } else {
                        logout();
                        alert(atob(data.data));
                        isCertificateValid = false;
                        return false;
                    }
                } else {
                    isCertificateValid = false;
                    return false;
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                isCertificateValid = false;
                return false;
            });
    }

    function doSign(alias, serialNumber, certificate) {
        if (config.certificateData == null || config.certificateData == "") {
            config.certificateData = certificate;
        }
        var preSignResult = true;
        if (config.preSignCallback) {
            // Here : The Verify Certificate method must be invoked in the
            // pre-sign event to prevent signing using an expired certificate
            doVerifyCertificate();
            if (!isCertificateValid) {
                return;
            }
            preSignResult = config.preSignCallback();
        }
        if (isCertificateValid && preSignResult) {
            aliasName = alias;
            if (config.signType === "data") {
                realSign(signData);
            } else if (config.signType === "xml") {
                realSignXML(signData);
            } else if (config.signType === "pdf") {
                if (config.mode === "stamping") {
                    realSignPdfStamping(signData);
                } else if (config.mode === "nostampingv2") {
                    PDFNoStampingV2(signData);
                } else if (config.mode === "batch") {
                    PDFNoStampingBatchV2(signData);
                } else {
                    realSignPdfNoStamping(signData);
                }
            }
        }
    }

    function realSign(signData) {
        try {
            var data = {
                data: btoa(signData),
                type: "SHA256withRSA",
                alias: aliasName,
            };
        } catch (err) {
            var data = {
                data: utoa(signData),
                type: "SHA256withRSA",
                alias: aliasName,
            };
        }
        $.ajax({
            url: dscServiceUrl + "/sign",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                logout();
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.sign,
                            data.token
                        );
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function realSignXML(signData) {
        try {
            var data = {
                data: btoa(signData),
                alias: aliasName,
            };
        } catch (err) {
            var data = {
                data: utoa(signData),
                alias: aliasName,
            };
        }
        $.ajax({
            url: dscServiceUrl + "/signXMLData",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                logout();
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.sign,
                            data.token
                        );
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function realSignPdfNoStamping(signData) {
        var data = {
            base64PdfData: signData,
            alias: aliasName,
            reason: $("#signingReason").val(),
            location: $("#signingLocation").val(),
            tsaURL: $("#tsaURL").val(),
            timeServerURL: $("#timeServerURL").val(),
        };
        $.ajax({
            url: dscServiceUrl + "/signPdfNoStamping",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                logout();
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.signedPdf,
                            data.token
                        );
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function signPDFBatch(signData, token) {
        //Verify batch token
        if (token != batch_token) {
            alert("Invalid batch token");
            return;
        }

        //Logout after end of batch
        if (batch_size == 0) {
            alert("Batch terminated!");
            return;
        }
        gstSigner(signData);
    }

    function PDFNoStampingBatchV2(signData) {
        var StampingObject = new Object();
        StampingObject.stampingX = $("#stampingX").val();
        StampingObject.stampingY = $("#stampingY").val();
        var data = {
            base64PdfData: signData,
            alias: aliasName,
            reason: $("#signingReason").val(),
            location: $("#signingLocation").val(),
            tsaURL: $("#tsaURL").val(),
            timeServerURL: $("#timeServerURL").val(),
            stampingData: StampingObject,
        };
        $.ajax({
            url: dscServiceUrl + "/PDFNoStampingV2",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.signedPdf,
                            data.token
                        );
                    batch_size = batch_size - 1;
                    //Logout after end of batch
                    if (batch_size == 0) {
                        logout();
                    }
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "DSCSigner client is not installed or running.If DSCSigner client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function PDFNoStampingV2(signData) {
        var StampingObject = new Object();
        StampingObject.stampingX = $("#stampingX").val();
        StampingObject.stampingY = $("#stampingY").val();
        var data = {
            base64PdfData: signData,
            alias: aliasName,
            reason: $("#signingReason").val(),
            location: $("#signingLocation").val(),
            tsaURL: $("#tsaURL").val(),
            timeServerURL: $("#timeServerURL").val(),
            stampingData: StampingObject,
        };
        $.ajax({
            url: dscServiceUrl + "/PDFNoStampingV2",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                logout();
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.signedPdf,
                            data.token
                        );
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function realSignPdfStamping(signData) {
        var StampingObject = new Object();
        // StampingObject.mode = $('input[name=stampingMode]:checked').val();
        if ($("#allPageMode").is(":checked")) {
            StampingObject.mode = "A";
        } else if ($("#lastPageMode").is(":checked")) {
            StampingObject.mode = "L";
        } else {
            StampingObject.mode = "L";
        }
        StampingObject.stampingX = $("#stampingX").val();
        StampingObject.stampingY = $("#stampingY").val();
        StampingObject.scale = $("#scale").val();
        StampingObject.stampingImageData = $("#imageData").val();
        var data = {
            base64PdfData: signData,
            alias: aliasName,
            reason: $("#signingReason").val(),
            location: $("#signingLocation").val(),
            tsaURL: $("#tsaURL").val(),
            timeServerURL: $("#timeServerURL").val(),
            stampingData: StampingObject,
        };
        $.ajax({
            url: dscServiceUrl + "/signPdfStamping",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            async: false,
        })
            .done(function (data) {
                logout();
                if (data.status === "success") {
                    $("#panel").hide();
                    if (config.postSignCallback)
                        config.postSignCallback(
                            aliasName,
                            data.signedPdf,
                            data.token
                        );
                } else {
                    alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
                logout();
            });
    }

    function logout() {
        $.ajax({
            url: dscServiceUrl + "/logout",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            async: false,
        })
            .done(function (data) {
                if (data.status !== "success") {
                    //alert(data.errorMessage);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                alert(
                    "NICDSign client is not installed or running.If NICDSign client is already installed, \nremove and reconnect the token before trying again."
                );
            });
    }

    // ucs-2 string to base64 encoded ascii
    function utoa(str) {
        return window.btoa(unescape(encodeURIComponent(str)));
    }
    // base64 encoded ascii to ucs-2 string
    function atou(str) {
        return decodeURIComponent(escape(window.atob(str)));
    }

    function makeid() {
        var text = "";
        var possible =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        for (var i = 0; i < 5; i++)
            text += possible.charAt(
                Math.floor(Math.random() * possible.length)
            );

        return text;
    }

    function SHA256(s) {
        var chrsz = 8;
        var hexcase = 0;
        function safe_add(x, y) {
            var lsw = (x & 0xffff) + (y & 0xffff);
            var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
            return (msw << 16) | (lsw & 0xffff);
        }
        function S(X, n) {
            return (X >>> n) | (X << (32 - n));
        }
        function R(X, n) {
            return X >>> n;
        }
        function Ch(x, y, z) {
            return (x & y) ^ (~x & z);
        }
        function Maj(x, y, z) {
            return (x & y) ^ (x & z) ^ (y & z);
        }
        function Sigma0256(x) {
            return S(x, 2) ^ S(x, 13) ^ S(x, 22);
        }
        function Sigma1256(x) {
            return S(x, 6) ^ S(x, 11) ^ S(x, 25);
        }
        function Gamma0256(x) {
            return S(x, 7) ^ S(x, 18) ^ R(x, 3);
        }
        function Gamma1256(x) {
            return S(x, 17) ^ S(x, 19) ^ R(x, 10);
        }
        function core_sha256(m, l) {
            var K = new Array(
                0x428a2f98,
                0x71374491,
                0xb5c0fbcf,
                0xe9b5dba5,
                0x3956c25b,
                0x59f111f1,
                0x923f82a4,
                0xab1c5ed5,
                0xd807aa98,
                0x12835b01,
                0x243185be,
                0x550c7dc3,
                0x72be5d74,
                0x80deb1fe,
                0x9bdc06a7,
                0xc19bf174,
                0xe49b69c1,
                0xefbe4786,
                0xfc19dc6,
                0x240ca1cc,
                0x2de92c6f,
                0x4a7484aa,
                0x5cb0a9dc,
                0x76f988da,
                0x983e5152,
                0xa831c66d,
                0xb00327c8,
                0xbf597fc7,
                0xc6e00bf3,
                0xd5a79147,
                0x6ca6351,
                0x14292967,
                0x27b70a85,
                0x2e1b2138,
                0x4d2c6dfc,
                0x53380d13,
                0x650a7354,
                0x766a0abb,
                0x81c2c92e,
                0x92722c85,
                0xa2bfe8a1,
                0xa81a664b,
                0xc24b8b70,
                0xc76c51a3,
                0xd192e819,
                0xd6990624,
                0xf40e3585,
                0x106aa070,
                0x19a4c116,
                0x1e376c08,
                0x2748774c,
                0x34b0bcb5,
                0x391c0cb3,
                0x4ed8aa4a,
                0x5b9cca4f,
                0x682e6ff3,
                0x748f82ee,
                0x78a5636f,
                0x84c87814,
                0x8cc70208,
                0x90befffa,
                0xa4506ceb,
                0xbef9a3f7,
                0xc67178f2
            );
            var HASH = new Array(
                0x6a09e667,
                0xbb67ae85,
                0x3c6ef372,
                0xa54ff53a,
                0x510e527f,
                0x9b05688c,
                0x1f83d9ab,
                0x5be0cd19
            );
            var W = new Array(64);
            var a, b, c, d, e, f, g, h, i, j;
            var T1, T2;
            m[l >> 5] |= 0x80 << (24 - (l % 32));
            m[(((l + 64) >> 9) << 4) + 15] = l;
            for (var i = 0; i < m.length; i += 16) {
                a = HASH[0];
                b = HASH[1];
                c = HASH[2];
                d = HASH[3];
                e = HASH[4];
                f = HASH[5];
                g = HASH[6];
                h = HASH[7];
                for (var j = 0; j < 64; j++) {
                    if (j < 16) W[j] = m[j + i];
                    else
                        W[j] = safe_add(
                            safe_add(
                                safe_add(Gamma1256(W[j - 2]), W[j - 7]),
                                Gamma0256(W[j - 15])
                            ),
                            W[j - 16]
                        );
                    T1 = safe_add(
                        safe_add(
                            safe_add(safe_add(h, Sigma1256(e)), Ch(e, f, g)),
                            K[j]
                        ),
                        W[j]
                    );
                    T2 = safe_add(Sigma0256(a), Maj(a, b, c));
                    h = g;
                    g = f;
                    f = e;
                    e = safe_add(d, T1);
                    d = c;
                    c = b;
                    b = a;
                    a = safe_add(T1, T2);
                }
                HASH[0] = safe_add(a, HASH[0]);
                HASH[1] = safe_add(b, HASH[1]);
                HASH[2] = safe_add(c, HASH[2]);
                HASH[3] = safe_add(d, HASH[3]);
                HASH[4] = safe_add(e, HASH[4]);
                HASH[5] = safe_add(f, HASH[5]);
                HASH[6] = safe_add(g, HASH[6]);
                HASH[7] = safe_add(h, HASH[7]);
            }
            return HASH;
        }
        function str2binb(str) {
            var bin = Array();
            var mask = (1 << chrsz) - 1;
            for (var i = 0; i < str.length * chrsz; i += chrsz) {
                bin[i >> 5] |=
                    (str.charCodeAt(i / chrsz) & mask) << (24 - (i % 32));
            }
            return bin;
        }
        function Utf8Encode(string) {
            string = string.replace(/\r\n/g, "\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                } else if (c > 127 && c < 2048) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                } else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        }
        function binb2hex(binarray) {
            var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
            var str = "";
            for (var i = 0; i < binarray.length * 4; i++) {
                str +=
                    hex_tab.charAt(
                        (binarray[i >> 2] >> ((3 - (i % 4)) * 8 + 4)) & 0xf
                    ) +
                    hex_tab.charAt(
                        (binarray[i >> 2] >> ((3 - (i % 4)) * 8)) & 0xf
                    );
            }
            return str;
        }
        s = Utf8Encode(s);
        return binb2hex(core_sha256(str2binb(s), s.length * chrsz));
    }

    return {
        configure: configure,
        certificate: getCertificate,
        initialize: initialize,
        sign: gstSigner,
        doSign: doSign,
        logout: logout,
        initbatch: initializeBatch,
        signpdfbatch: signPDFBatch,
        batchsize: getBatchSize,
    };
})(jQuery);

function hidePanel() {
    $("#panel").hide();
}
