<?php

// Test database connection to AWS RDS
$host = 'etender-db-prod-database-uuxojwhytrvp.c3iq0qo2oiuq.ap-south-1.rds.amazonaws.com';
$port = 3306;
$database = 'etender';
$username = 'etender';
$password = 'dimahasaoIT#';

echo "Testing database connection to AWS RDS...\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Database: $database\n";
echo "Username: $username\n\n";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 30,
        PDO::ATTR_PERSISTENT => false,
    ];
    
    echo "Attempting to connect...\n";
    $start_time = microtime(true);
    
    $pdo = new PDO($dsn, $username, $password, $options);
    
    $end_time = microtime(true);
    $connection_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "✅ Connection successful!\n";
    echo "Connection time: {$connection_time}ms\n\n";
    
    // Test a simple query
    echo "Testing simple query...\n";
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result['test'] == 1) {
        echo "✅ Query test successful!\n";
    } else {
        echo "❌ Query test failed!\n";
    }
    
    // Test sessions table
    echo "\nTesting sessions table...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ Sessions table exists\n";
        
        // Count sessions
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM sessions");
        $count = $stmt->fetch();
        echo "Sessions count: {$count['count']}\n";
    } else {
        echo "❌ Sessions table does not exist\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Connection failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    
    // Check if it's a timeout error
    if (strpos($e->getMessage(), 'timed out') !== false || strpos($e->getMessage(), 'timeout') !== false) {
        echo "\n🔍 This appears to be a timeout error. Possible causes:\n";
        echo "1. Network connectivity issues\n";
        echo "2. AWS RDS security group not allowing your IP\n";
        echo "3. RDS instance is not publicly accessible\n";
        echo "4. Firewall blocking the connection\n";
        echo "5. RDS instance is stopped or unavailable\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Network diagnostics:\n";

// Test basic connectivity
echo "\nTesting basic connectivity to host...\n";
$connection = @fsockopen($host, $port, $errno, $errstr, 10);
if ($connection) {
    echo "✅ Can reach host on port $port\n";
    fclose($connection);
} else {
    echo "❌ Cannot reach host on port $port\n";
    echo "Error: $errstr ($errno)\n";
}

echo "\nDone.\n";
