# eTender Legacy Application

A Laravel-based tender management system built on the TALL stack (Tailwind, Alpine, Laravel, Livewire).

## Local Development Installation

```bash
# Install PHP dependencies
composer install

# Install and build frontend assets
npm install && npm run dev

# Create storage symlink
php artisan storage:link

# Configure database credentials in .env file

# Run migrations and seed the database
php artisan migrate --seed
```

## Deploying to AWS

This application can be deployed to AWS ECS with Fargate using the included deployment script. The deployment is cost-optimized with the following features:

- Uses Fargate Spot instances for reduced compute costs (70% savings)
- Configures auto-scaling with scale-to-zero capability
- Deploys a publicly accessible t4g.micro RDS MySQL instance for the database
- Includes Application Load Balancer with HTTPS support

### Deployment Steps

1. Make sure you have AWS CLI configured with appropriate credentials

2. For the first deployment, you need to provide a database password:

   ```bash
   ./deploy.sh --db-password "YourSecurePassword"
   ```

3. For subsequent deployments, you can omit the password:

   ```bash
   ./deploy.sh
   ```

4. The deployment version will automatically increment each time you deploy, forcing a new task definition to be created and deployed

### Additional Options

- `--branch` - Specify the branch name for environment configuration (default: master)
- `--image-tag` - Specify the Docker image tag (default: git commit hash)

### Cost Optimization

The deployment is configured for minimal AWS costs:
- Fargate tasks use minimal CPU (0.5 vCPU) and memory (1GB)
- Uses Fargate Spot for ~70% cost reduction
- Auto-scaling configured to scale to zero during periods of no traffic
- RDS instance uses t4g.micro (~$15/month)
- CloudWatch logs configured with 7-day retention

### Configuration

Update these values in the deploy.sh script if needed:
- ECR_REPOSITORY_URI
- VPC_ID
- SUBNETS
- ECS_CLUSTER_NAME
- AWS_REGION
- DB_NAME
- DB_USERNAME

### Manual Deployment (Alternative)

If you prefer to deploy manually instead of using the script:

```bash
aws cloudformation deploy \
  --template-file laravel-deployment.yaml \
  --stack-name etender-stack \
  --parameter-overrides \
    BranchName=master \
    VPCId=vpc-0adb6f6ede3ba08a6 \
    Subnets="subnet-09bf79ee94abed510,subnet-0c1df04797e279bf8" \
    ImageTag=latest \
    ExistingECRRepositoryUri=881490112356.dkr.ecr.ap-south-1.amazonaws.com/etender-legacy \
    ExistingECSClusterName=etender-cluster \
    DBName=etender \
    DBUsername=etender \
    DBPassword="YourSecurePassword" \
  --capabilities CAPABILITY_IAM \
  --region ap-south-1 \
  --profile nchac
```
