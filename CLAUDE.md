# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

eTender Legacy is a Laravel-based tender management system built on the TALL stack (Tailwind, Alpine, Laravel, Livewire). It manages the complete tender lifecycle including creation, bidding, document management, payments, and evaluation.

### Key Features

- Tender creation and management
- Document upload and review
- Bidding process management
- EMD (Earnest Money Deposit) and document payment processing via Razorpay
- Admin, department, and company user roles
- Digital signature integration
- Selective vs. open tender classification

## Development Commands

### Environment Setup

```bash
# Install PHP dependencies
composer install

# Install and build frontend assets
npm install && npm run dev

# Create storage symlink
php artisan storage:link

# Run database migrations and seed the database
php artisan migrate --seed
```

### Development Commands

```bash
# Run development server
php artisan serve

# Watch for asset changes during development
npm run watch

# Compile assets for production
npm run prod

# Clear application cache
php artisan cache:clear

# Clear config cache
php artisan config:clear

# Regenerate app key
php artisan key:generate
```

### Testing Commands

```bash
# Run all tests
php artisan test

# Run specific test class
php artisan test --filter=ClassName

# Run with coverage report
php artisan test --coverage
```

## Project Structure

### Key Directories and Files

- `app/Http/Livewire` - Livewire components organized by feature areas (Admin, Tenders, Front, etc.)
- `app/Http/Controllers` - Controllers for non-Livewire functionality
- `app/Models` - Eloquent models (Tender, Bidding, User, etc.)
- `app/Enums` - PHP 8 enums for type-safe status values
- `app/Policies` - Authorization policies
- `app/Services` - Service classes for business logic
- `resources/views/livewire` - Blade templates for Livewire components
- `resources/views/components` - Reusable Blade components
- `public/js/dsc` - Digital signature integration
- `routes/web.php` - Web routes organized by feature

### Key Architectural Components

1. **Tender Workflow**
   - Tenders progress through states defined in `TenderStatus` enum
   - Created by makers, approved by checkers, evaluated by evaluators

2. **User Roles**
   - Admin: Oversees all tenders and departments
   - Department users (makers/checkers): Create and review tenders
   - Evaluators: Review and evaluate bids
   - Companies: Submit bids on tenders

3. **Payment Integration**
   - Razorpay for document purchases and EMD payments
   - Webhook endpoints for payment notifications

4. **Security**
   - Policy-based authorization (`app/Policies`)
   - Role-based middleware (`RoleMiddleware`, `PermissionMiddleware`)
   - Digital signature for bid finalization

## Database Structure

Key models and their relationships:

- `Tender`: Central entity with many tender items, documents, and biddings
- `Bidding`: Represents a company's bid on a tender
- `BiddingPrice`: Line item pricing for tender items
- `User`: Both administrative users and company users
- `CompanyProfile`: Extended information for company users
- `Department`: Organizational departments that own tenders
- `DocumentPayment` & `EmdPayment`: Track payments made by companies

## AWS Deployment

The application includes scripts for deploying to AWS ECS with Fargate. Key features:

- Uses Fargate Spot instances for cost optimization
- Auto-scaling with scale-to-zero capability
- Configurable via CloudFormation templates

Deploy with:
```bash
./deploy.sh
```

For the first deployment, specify a database password:
```bash
./deploy.sh --db-password "YourSecurePassword"
```