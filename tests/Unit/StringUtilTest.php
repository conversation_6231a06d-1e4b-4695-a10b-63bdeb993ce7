<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class StringUtilTest extends TestCase
{
    /**
     * Test string reversal.
     *
     * @return void
     */
    public function test_string_reversal()
    {
        $original = "Hello World";
        $reversed = strrev($original);
        
        $this->assertEquals("dlroW olleH", $reversed);
    }
    
    /**
     * Test string to uppercase conversion.
     *
     * @return void
     */
    public function test_string_to_uppercase()
    {
        $original = "Hello World";
        $uppercase = strtoupper($original);
        
        $this->assertEquals("HELLO WORLD", $uppercase);
    }
}